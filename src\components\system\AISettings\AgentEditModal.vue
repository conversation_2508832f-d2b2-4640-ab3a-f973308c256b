<template>
  <a-modal
    v-model:open="modalVisible"
    title="编辑智能体"
    width="720px"
    :confirm-loading="saving"
    @ok="handleSave"
    @cancel="handleCancel"
    class="agent-edit-modal"
  >
    <a-form layout="vertical" :model="formData" ref="formRef">
      <a-form-item label="智能体名称">
        <a-input
          v-model:value="formData.智能体名称"
          placeholder="请输入智能体名称"
        />
      </a-form-item>

      <a-form-item label="智能体描述">
        <a-textarea
          v-model:value="formData.智能体描述"
          :rows="2"
          placeholder="请输入智能体描述"
        />
      </a-form-item>

      <a-form-item label="关联知识库">
        <a-select
          mode="multiple"
          v-model:value="formData.知识库列表"
          :options="knowledgeBaseOptions"
          :loading="loadingKBs || kbUpdating"
          :disabled="kbUpdating"
          placeholder="选择要关联的知识库"
          @change="onKnowledgeChange"
        />
      </a-form-item>

      <div v-if="customVariables.length > 0">
        <div style="font-weight:600;margin:12px 0 8px;">自定义变量</div>
        <a-row :gutter="12">
          <a-col :span="12" v-for="item in customVariables" :key="'edit_'+item.变量名">
            <a-form-item :label="item.变量名">
              <a-input v-if="item.变量类型==='string'" v-model:value="formData.变量值[item.变量名]" :placeholder="item.变量描述 || '请输入'" />
              <a-input-number v-else-if="item.变量类型==='number'" v-model:value="formData.变量值[item.变量名]" style="width:100%" />
              <a-switch v-else-if="item.变量类型==='boolean'" v-model:checked="formData.变量值[item.变量名]" />
              <a-textarea v-else v-model:value="formData.变量值[item.变量名]" :rows="2" :placeholder="'JSON/文本'" />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import api from '@/services/api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  agentId: {
    type: [String, Number],
    default: null
  },
  knowledgeBaseOptions: {
    type: Array,
    default: () => []
  },
  loadingKBs: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const formRef = ref()
const saving = ref(false)
const kbUpdating = ref(false)
const customVariables = ref([])

const formData = reactive({
  智能体id: null,
  智能体名称: '',
  智能体描述: '',
  知识库列表: [],
  变量值: {}
})

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听agentId变化，加载数据
watch(() => props.agentId, async (newId) => {
  if (newId && props.visible) {
    await loadAgentData(newId)
  }
}, { immediate: true })

// 监听visible变化
watch(() => props.visible, async (visible) => {
  if (visible && props.agentId) {
    await loadAgentData(props.agentId)
  } else if (!visible) {
    resetForm()
  }
})

// 加载智能体数据
const loadAgentData = async (agentId) => {
  console.log('📥 开始加载智能体数据:', agentId)

  try {
    if (!agentId) {
      console.warn('⚠️ 智能体ID为空，跳过加载')
      return
    }

    const res = await api.post('/user/langchain/agents/detail', { 智能体id: agentId })
    console.log('📥 智能体详情API响应:', res)

    if (res?.status === 100 && res?.data) {
      const d = res.data
      console.log('📋 智能体详情数据:', d)

      formData.智能体id = agentId
      formData.智能体名称 = d.智能体名称 || ''
      formData.智能体描述 = d.智能体描述 || ''
      customVariables.value = d.自定义变量 || []

      // 处理自定义变量
      const kv = {}
      customVariables.value.forEach(v => { kv[v.变量名] = v.默认值 })
      formData.变量值 = kv

      // 处理知识库关联 - 支持多种数据格式
      const rel = d.知识库关联 || []
      console.log('📋 原始知识库关联数据:', rel)

      // 提取知识库ID列表，支持多种字段名
      const 知识库ids = rel.map(x => {
        return x.知识id || x.langchain_知识库表id || x.知识库id || x.id
      }).filter(Boolean)

      formData.知识库列表 = 知识库ids
      console.log('📋 处理后的知识库ID列表:', formData.知识库列表)

      console.log('✅ 智能体数据加载完成:', {
        智能体名称: formData.智能体名称,
        智能体描述: formData.智能体描述,
        知识库列表: formData.知识库列表,
        自定义变量: customVariables.value.length
      })
    } else {
      console.error('❌ 智能体详情API返回错误:', res)
      message.error(res?.message || '加载智能体数据失败')
    }
  } catch (e) {
    console.error('❌ 加载智能体数据异常:', e)
    message.error('加载智能体数据失败')
  }
}

// 简化版知识库关联变更处理
const onKnowledgeChange = async (newList) => {
  console.log('🔄 知识库选择变更:', {
    智能体id: formData.智能体id,
    原列表: formData.知识库列表,
    新列表: newList,
    更新中: kbUpdating.value
  })

  // 简化方案：直接更新所有知识库关联
  try {
    if (!formData.智能体id) {
      console.warn('⚠️ 智能体ID为空，无法更新知识库关联')
      return
    }

    if (kbUpdating.value) {
      console.warn('⚠️ 正在更新中，跳过此次操作')
      return
    }

    kbUpdating.value = true

    // 确保数组格式正确
    const prevArray = Array.isArray(formData.知识库列表) ? formData.知识库列表 : []
    const currArray = Array.isArray(newList) ? newList : []

    const prev = new Set(prevArray)
    const curr = new Set(currArray)

    console.log('� 变更检测详情:', {
      原列表: Array.from(prev),
      新列表: Array.from(curr),
      原列表长度: prev.size,
      新列表长度: curr.size
    })

    // 找出变更的知识库ID
    let changedId = null
    let isAdd = false

    // 检查新增的知识库
    for (const id of curr) {
      if (!prev.has(id)) {
        changedId = id
        isAdd = true
        console.log('🆕 发现新增知识库:', id)
        break
      }
    }

    // 检查删除的知识库
    if (changedId === null) {
      for (const id of prev) {
        if (!curr.has(id)) {
          changedId = id
          isAdd = false
          console.log('🗑️ 发现删除知识库:', id)
          break
        }
      }
    }

    // 如果没有找到变更，可能是因为数组顺序不同，但内容相同
    if (changedId === null) {
      // 检查数组长度是否相同
      if (prev.size !== curr.size) {
        console.log('⚠️ 数组长度不同但未找到变更项，这是异常情况')
        console.log('🔧 尝试找出第一个不同的项目进行更新')

        // 如果新列表更长，取第一个新增的
        if (curr.size > prev.size) {
          for (const id of curr) {
            if (!prev.has(id)) {
              changedId = id
              isAdd = true
              console.log('🆕 找到新增项:', id)
              break
            }
          }
        }
        // 如果新列表更短，取第一个删除的
        else {
          for (const id of prev) {
            if (!curr.has(id)) {
              changedId = id
              isAdd = false
              console.log('🗑️ 找到删除项:', id)
              break
            }
          }
        }

        // 如果还是没找到，直接更新本地状态
        if (changedId === null) {
          console.log('📝 仍未找到变更，直接更新本地状态')
          formData.知识库列表 = [...currArray]
          return
        }
      } else {
        console.log('📝 没有实际变更，直接更新本地状态')
        formData.知识库列表 = [...currArray]
        return
      }
    }

    console.log('🔄 调用单个切换API:', {
      智能体id: formData.智能体id,
      知识库id: changedId,
      操作: isAdd ? '关联' : '取消关联'
    })

    const res = await api.post('/user/langchain/agents/knowledge/toggle', {
      智能体id: formData.智能体id,
      知识库id: changedId
    })

    console.log('📥 单个切换API响应:', res)

    if (res?.status === 100) {
      // 后端返回格式：{智能体id: xxx, 知识库列表: [1,2,3]}
      const 列表 = res?.data?.知识库列表 || res?.知识库列表
      console.log('📥 后端返回的完整数据:', res?.data || res)
      console.log('📋 提取的知识库列表:', 列表)

      if (Array.isArray(列表)) {
        formData.知识库列表 = [...列表]
        console.log('✅ 知识库关联更新成功，新列表:', 列表)
        message.success(isAdd ? '知识库关联成功' : '知识库关联已取消')
      } else {
        console.warn('⚠️ API返回的知识库列表格式异常，使用本地状态:', 列表)
        formData.知识库列表 = [...currArray]
        message.success(isAdd ? '知识库关联成功' : '知识库关联已取消')
      }
    } else {
      console.error('❌ 单个切换API返回错误:', res)
      message.error(res?.message || '更新失败')
      // 回滚到原来的状态
      formData.知识库列表 = [...prevArray]
    }
  } catch (e) {
    console.error('❌ 知识库关联更新失败:', e)
    message.error('更新失败，请重试')
    // 回滚到原来的状态
    formData.知识库列表 = [...(formData.知识库列表 || [])]
  } finally {
    kbUpdating.value = false
    console.log('🏁 知识库更新操作完成')
  }
}

// 保存处理
const handleSave = async (event) => {
  console.log('💾 开始保存智能体:', formData)

  try {
    event?.preventDefault?.()

    if (!formData.智能体id) {
      console.error('❌ 智能体ID为空，无法保存')
      message.error('智能体ID缺失，无法保存')
      return
    }

    if (!formData.智能体名称?.trim()) {
      console.error('❌ 智能体名称为空')
      message.error('请输入智能体名称')
      return
    }

    saving.value = true

    const payload = {
      智能体id: formData.智能体id,
      智能体名称: formData.智能体名称.trim(),
      智能体描述: formData.智能体描述 || '',
      知识库列表: formData.知识库列表 || [],
      变量值: formData.变量值 || {}
    }

    console.log('📤 发送保存请求:', payload)

    const res = await api.post('/user/langchain/agents/update', payload)

    console.log('📥 保存响应:', res)

    if (res?.status === 100) {
      console.log('✅ 保存成功')
      message.success('保存成功')
      emit('success')
    } else {
      console.error('❌ 保存失败:', res)
      message.error(res?.message || '保存失败')
    }
  } catch (e) {
    console.error('❌ 保存异常:', e)
    message.error('保存失败，请重试')
  } finally {
    saving.value = false
    console.log('🏁 保存操作完成')
  }
}

// 取消处理
const handleCancel = () => {
  modalVisible.value = false
}

// 重置表单
const resetForm = () => {
  console.log('🔄 重置编辑表单')
  Object.assign(formData, {
    智能体id: null,
    智能体名称: '',
    智能体描述: '',
    知识库列表: [],
    变量值: {}
  })
  customVariables.value = []
  formRef.value?.resetFields()
  console.log('✅ 表单重置完成')
}
</script>

<style scoped>
.agent-edit-modal :deep(.ant-modal-content) {
  border-radius: 12px;
  overflow: hidden;
}

.agent-edit-modal :deep(.ant-modal-header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
  padding: 20px 24px;
}

.agent-edit-modal :deep(.ant-modal-title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}
</style>
