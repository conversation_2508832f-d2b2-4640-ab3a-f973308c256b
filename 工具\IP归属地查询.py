"""
IP归属地查询工具
简单直接的IP归属地查询函数
"""

import requests
from 日志 import 系统日志器, 错误日志器


def 查询IP归属地(ip地址: str) -> str:
    """
    查询IP地址的归属地信息
    
    Args:
        ip地址: 要查询的IP地址
        
    Returns:
        str: 归属地信息，如"北京市"、"内网"、"本地"等
    """
    try:
        # 本地IP处理
        if not ip地址 or ip地址 in ['127.0.0.1', 'localhost', '::1']:
            return '本地'
        
        # 内网IP处理
        if _是内网IP(ip地址):
            return '内网'
        
        # 外网IP查询
        return _查询外网IP归属地(ip地址)
        
    except Exception as e:
        错误日志器.error(f"查询IP归属地失败 {ip地址}: {e}")
        return '未知'


def _是内网IP(ip地址: str) -> bool:
    """判断是否为内网IP"""
    try:
        parts = ip地址.split('.')
        if len(parts) != 4:
            return False
        
        # 192.168.x.x
        if parts[0] == '192' and parts[1] == '168':
            return True
        
        # 10.x.x.x
        if parts[0] == '10':
            return True
        
        # 172.16.x.x - 172.31.x.x
        if parts[0] == '172':
            second = int(parts[1])
            if 16 <= second <= 31:
                return True
        
        return False
    except Exception:
        return False


def _查询外网IP归属地(ip地址: str) -> str:
    """查询外网IP的归属地"""
    try:
        # 使用免费的IP查询API
        url = f"http://ip-api.com/json/{ip地址}?lang=zh-CN&fields=country,regionName,city,status"
        
        response = requests.get(url, timeout=3)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                country = data.get('country', '')
                region = data.get('regionName', '')
                city = data.get('city', '')
                
                # 构建归属地字符串
                if country == '中国':
                    if city and region:
                        return f"{region}{city}"
                    elif region:
                        return region
                    else:
                        return country
                else:
                    return country or '海外'
            else:
                return '未知'
        else:
            return '查询失败'
            
    except requests.Timeout:
        系统日志器.warning(f"IP归属地查询超时: {ip地址}")
        return '查询超时'
    except Exception as e:
        错误日志器.error(f"查询外网IP归属地失败 {ip地址}: {e}")
        return '查询失败'
