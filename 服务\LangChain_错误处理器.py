"""
LangChain错误处理器 - 统一的错误处理和日志记录

功能：
1. 统一的异常处理
2. 标准化的错误响应格式
3. 错误分类和优先级管理
4. 自动错误恢复机制
"""

import logging
import traceback
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional, Tuple, Union


class 错误级别(Enum):
    """错误级别枚举"""
    低 = "low"
    中 = "medium" 
    高 = "high"
    严重 = "critical"


class 错误类型(Enum):
    """错误类型枚举"""
    参数错误 = "parameter_error"
    权限错误 = "permission_error"
    数据库错误 = "database_error"
    网络错误 = "network_error"
    模型错误 = "model_error"
    系统错误 = "system_error"
    业务逻辑错误 = "business_logic_error"


class LangChain错误处理器:
    """统一的错误处理器"""
    
    def __init__(self, 日志器名称: str = "LangChain.错误处理器"):
        self.日志器 = logging.getLogger(日志器名称)
        self.错误统计: Dict[str, int] = {}
    
    def 处理异常(
        self, 
        异常: Exception, 
        上下文: str = "",
        错误类型: 错误类型 = 错误类型.系统错误,
        错误级别: 错误级别 = 错误级别.中,
        用户友好消息: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        统一处理异常
        
        Args:
            异常: 捕获的异常对象
            上下文: 错误发生的上下文信息
            错误类型: 错误类型分类
            错误级别: 错误严重程度
            用户友好消息: 用户友好的错误消息
            
        Returns:
            标准化的错误响应字典
        """
        try:
            # 生成错误ID
            错误ID = f"{错误类型.value}_{int(datetime.now().timestamp())}"
            
            # 获取详细错误信息
            错误消息 = str(异常)
            错误堆栈 = traceback.format_exc()
            
            # 记录错误统计
            self._更新错误统计(错误类型.value)
            
            # 根据错误级别选择日志级别
            if 错误级别 == 错误级别.严重:
                self.日志器.critical(f"🚨 严重错误 [{错误ID}] {上下文}: {错误消息}")
            elif 错误级别 == 错误级别.高:
                self.日志器.error(f"❌ 高级错误 [{错误ID}] {上下文}: {错误消息}")
            elif 错误级别 == 错误级别.中:
                self.日志器.warning(f"⚠️ 中级错误 [{错误ID}] {上下文}: {错误消息}")
            else:
                self.日志器.info(f"ℹ️ 低级错误 [{错误ID}] {上下文}: {错误消息}")
            
            # 详细堆栈信息只在调试模式下记录
            self.日志器.debug(f"错误堆栈 [{错误ID}]: {错误堆栈}")
            
            # 构建标准化响应
            错误响应 = {
                "success": False,
                "error_id": 错误ID,
                "error_type": 错误类型.value,
                "error_level": 错误级别.value,
                "error_message": 用户友好消息 or self._生成用户友好消息(错误类型, 错误消息),
                "context": 上下文,
                "timestamp": datetime.now().isoformat(),
                "technical_details": 错误消息 if 错误级别 in [错误级别.高, 错误级别.严重] else None
            }
            
            return 错误响应
            
        except Exception as e:
            # 错误处理器本身出错的兜底处理
            self.日志器.critical(f"错误处理器异常: {str(e)}")
            return {
                "success": False,
                "error_id": "error_handler_failure",
                "error_type": "system_error",
                "error_level": "critical",
                "error_message": "系统内部错误，请联系管理员",
                "timestamp": datetime.now().isoformat()
            }
    
    def _生成用户友好消息(self, 错误类型: 错误类型, 原始消息: str) -> str:
        """生成用户友好的错误消息"""
        友好消息映射 = {
            错误类型.参数错误: "请检查输入参数是否正确",
            错误类型.权限错误: "您没有执行此操作的权限",
            错误类型.数据库错误: "数据处理出现问题，请稍后重试",
            错误类型.网络错误: "网络连接异常，请检查网络状态",
            错误类型.模型错误: "AI模型处理异常，请稍后重试",
            错误类型.系统错误: "系统暂时不可用，请稍后重试",
            错误类型.业务逻辑错误: "操作无法完成，请检查操作条件"
        }
        
        基础消息 = 友好消息映射.get(错误类型, "操作失败，请稍后重试")
        
        # 对于参数错误，尝试提供更具体的信息
        if 错误类型 == 错误类型.参数错误 and "时间" in 原始消息:
            return "时间格式不正确，请使用 YYYY-MM-DD HH:MM:SS 格式"
        
        return 基础消息
    
    def _更新错误统计(self, 错误类型: str):
        """更新错误统计信息"""
        self.错误统计[错误类型] = self.错误统计.get(错误类型, 0) + 1
    
    def 获取错误统计(self) -> Dict[str, int]:
        """获取错误统计信息"""
        return self.错误统计.copy()
    
    def 清理错误统计(self):
        """清理错误统计信息"""
        self.错误统计.clear()
        self.日志器.info("错误统计信息已清理")


# 创建全局实例
LangChain错误处理器实例 = LangChain错误处理器()


def 安全执行(
    函数: callable,
    *args,
    错误类型: 错误类型 = 错误类型.系统错误,
    错误级别: 错误级别 = 错误级别.中,
    默认返回值: Any = None,
    上下文: str = "",
    **kwargs
) -> Tuple[bool, Any]:
    """
    安全执行函数的装饰器工具
    
    Returns:
        (是否成功, 结果或错误信息)
    """
    try:
        结果 = 函数(*args, **kwargs)
        return True, 结果
    except Exception as e:
        错误响应 = LangChain错误处理器实例.处理异常(
            e, 上下文=上下文 or f"执行函数 {函数.__name__}",
            错误类型=错误类型, 错误级别=错误级别
        )
        return False, 错误响应 if 默认返回值 is None else 默认返回值


async def 安全异步执行(
    异步函数: callable,
    *args,
    错误类型: 错误类型 = 错误类型.系统错误,
    错误级别: 错误级别 = 错误级别.中,
    默认返回值: Any = None,
    上下文: str = "",
    **kwargs
) -> Tuple[bool, Any]:
    """
    安全执行异步函数的工具
    
    Returns:
        (是否成功, 结果或错误信息)
    """
    try:
        结果 = await 异步函数(*args, **kwargs)
        return True, 结果
    except Exception as e:
        错误响应 = LangChain错误处理器实例.处理异常(
            e, 上下文=上下文 or f"执行异步函数 {异步函数.__name__}",
            错误类型=错误类型, 错误级别=错误级别
        )
        return False, 错误响应 if 默认返回值 is None else 默认返回值
