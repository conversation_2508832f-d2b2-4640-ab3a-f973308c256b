<template>
  <div class="member-contribution-chart">
    <div 
      ref="chartContainer" 
      :style="{ height: height }"
      class="chart-container"
    ></div>
    
    <div v-if="loading" class="chart-loading">
      <a-spin size="large" />
    </div>
    
    <div v-if="!loading && (!data || data.length === 0)" class="chart-empty">
      <a-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import gvmService from '../../services/gvmService'

defineOptions({
  name: 'MemberContributionChart'
})

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  height: {
    type: String,
    default: '300px'
  },
  loading: {
    type: Boolean,
    default: false
  },
  maxMembers: {
    type: Number,
    default: 10
  }
})

const emit = defineEmits(['memberClick'])

const chartContainer = ref(null)
let chartInstance = null

const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  updateChart()
  
  chartInstance.on('click', (params) => {
    emit('memberClick', params.data)
  })
}

const updateChart = () => {
  if (!chartInstance || props.loading) return
  
  const colors = gvmService.getChartColors()
  const option = getChartOption(colors)
  
  chartInstance.setOption(option, true)
}

const getChartOption = (colors) => {
  if (!props.data || props.data.length === 0) {
    return {}
  }
  
  // 取前N名成员数据
  const topMembers = props.data.slice(0, props.maxMembers)
  const memberNames = topMembers.map(item => item.name || `成员${item.id}`)
  const salesData = topMembers.map(item => item.sales || 0)
  const maxValue = Math.max(...salesData)
  
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params) => {
        const param = params[0]
        const member = topMembers[param.dataIndex]
        return `
          <div style="margin-bottom: 4px; font-weight: 600;">${param.name}</div>
          <div style="display: flex; align-items: center; margin-bottom: 2px;">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
            <span>销售额: ${gvmService.formatAmount(param.value)}</span>
          </div>
          <div style="font-size: 12px; color: #666;">
            订单数: ${member.orders || 0}单 | 佣金: ${gvmService.formatAmount(member.commission || 0)}
          </div>
        `
      }
    },
    grid: {
      left: '15%',
      right: '4%',
      bottom: '3%',
      top: '8%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => gvmService.formatAmount(value)
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: memberNames,
      axisLabel: {
        interval: 0,
        formatter: (value) => {
          return value.length > 6 ? value.substring(0, 6) + '...' : value
        }
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      }
    },
    series: [
      {
        type: 'bar',
        data: salesData.map((value, index) => ({
          value,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: colors.primary + '80' },
              { offset: 1, color: colors.primary }
            ])
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: colors.primary + 'CC' },
                { offset: 1, color: colors.primary }
              ])
            }
          }
        })),
        barWidth: '60%',
        label: {
          show: true,
          position: 'right',
          formatter: (params) => gvmService.formatAmount(params.value),
          color: '#666',
          fontSize: 12
        },
        animationDuration: 1000,
        animationEasing: 'cubicOut'
      }
    ],
    animation: true
  }
}

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

watch(() => props.data, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

watch(() => props.loading, (newLoading) => {
  if (!newLoading) {
    nextTick(() => {
      updateChart()
    })
  }
})

onMounted(() => {
  nextTick(() => {
    initChart()
  })
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})

defineExpose({
  refreshChart: updateChart,
  getChartInstance: () => chartInstance
})
</script>

<style scoped>
.member-contribution-chart {
  position: relative;
  width: 100%;
}

.chart-container {
  width: 100%;
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.chart-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
</style>
