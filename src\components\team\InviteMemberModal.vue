<template>
  <a-modal
    v-model:open="visible"
    title="邀请成员"
    width="600px"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      layout="vertical"
      autocomplete="off"
    >
      <!-- 手机号输入 -->
      <a-form-item label="手机号" name="手机号">
        <a-input
          v-model:value="form.手机号"
          placeholder="请输入被邀请人的手机号"
          size="large"
          :maxlength="11"
          autocomplete="off"
        />
        <div class="phone-help">
          <a-typography-text type="secondary" style="font-size: 12px;">
            支持邀请已注册用户和未注册用户
          </a-typography-text>
        </div>
      </a-form-item>

      <!-- 简化的角色选择 -->
      <a-form-item label="成员角色" name="角色">
        <a-select 
          v-model:value="form.角色" 
          placeholder="请选择角色"
          style="width: 100%"
          @change="handleSimpleRoleChange"
          :option-label-prop="'customLabel'"
        >
          <a-select-option value="负责人" :customLabel="`🎯 负责人`">
            <div class="role-option">
              <span class="role-icon">🎯</span>
              <div class="role-content">
                <div class="role-name">负责人</div>
                <div class="role-desc">拥有管理权限，可管理成员和团队设置</div>
              </div>
            </div>
          </a-select-option>
          <a-select-option value="成员" :customLabel="`👤 成员`">
            <div class="role-option">
              <span class="role-icon">👤</span>
              <div class="role-content">
                <div class="role-name">成员</div>
                <div class="role-desc">拥有基础权限，可查看团队信息</div>
              </div>
            </div>
          </a-select-option>
        </a-select>
      </a-form-item>

      <!-- 邀请预览 -->
      <div v-if="getInvitePreview()" class="invite-preview">
        <a-alert
          :message="getInvitePreview()"
          type="info"
          show-icon
        />
      </div>
    </a-form>

    <!-- 权限提示 -->
    <div v-if="!hasInvitePermission" class="permission-warning">
      <a-alert
        type="warning"
        show-icon
        message="权限不足"
        description="您没有邀请成员的权限，请联系团队管理员为您分配相应权限。"
        style="margin-top: 16px;"
      />
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <div style="text-align: right;">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button 
            type="primary" 
            @click="handleInvite"
            :loading="loading"
            :disabled="!hasInvitePermission"
          >
            发送邀请
          </a-button>
        </a-space>
      </div>
    </template>
  </a-modal>

  <!-- 邀请成功链接显示弹窗 -->
  <a-modal
    v-model:open="inviteLinkVisible"
    title="邀请发送成功"
    width="500px"
    :footer="null"
    centered
  >
    <div class="invite-success-content">
      <a-result
        status="success"
        :title="inviteLinkData.message || '邀请发送成功'"
      >
        <template #subTitle>
          <div class="invite-link-info">
            <p>邀请链接已生成，请将以下链接发送给被邀请人：</p>
            <div class="link-display">
              <a-input
                :value="inviteLinkData.邀请链接"
                readonly
                size="large"
                style="font-family: monospace;"
              >
                <template #suffix>
                  <a-button 
                    type="link" 
                    @click="copyInviteLink"
                    :loading="copyLoading"
                  >
                    <CopyOutlined />
                    复制
                  </a-button>
                </template>
              </a-input>
            </div>
            <div class="link-details">
              <a-descriptions size="small" :column="1">
                <a-descriptions-item label="邀请类型">
                  {{ inviteLinkData.邀请类型 }}
                </a-descriptions-item>
                <a-descriptions-item label="有效期" v-if="inviteLinkData.过期时间">
                  {{ formatExpireTime(inviteLinkData.过期时间) }}
                </a-descriptions-item>
                <a-descriptions-item label="邀请角色">
                  {{ getRoleDisplayName(inviteLinkData.角色 || form.角色) }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-button @click="inviteLinkVisible = false">关闭</a-button>
            <a-button type="primary" @click="copyAndClose">
              复制链接并关闭
            </a-button>
          </a-space>
        </template>
      </a-result>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { CheckOutlined, CloseOutlined, CopyOutlined } from '@ant-design/icons-vue'
import teamService from '../../services/team'
import { usePermissions } from '../../composables/usePermissions'
import { getRoleDisplayName, getRoleOptions } from '../../utils/roleUtils'
import { isValidPhone, debounce } from '../../utils/teamUtils'

import dayjs from 'dayjs'

defineOptions({
  name: 'InviteMemberModal'
})

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  team: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:open', 'success'])

// 响应式数据
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const loading = ref(false)
const formRef = ref()
const activePermissionKeys = ref([])

// 监听团队权限状态变化
watch(() => props.team?.权限状态, (newPermissionStatus) => {
  console.log('🔄 InviteMemberModal: 权限状态更新:', newPermissionStatus)
}, { deep: true, immediate: true })

// 计算属性
const teamId = computed(() => props.team?.团队id || props.team?.id)

// 使用权限管理组合函数
const { hasPermission } = usePermissions()

const form = reactive({
  手机号: '',
  角色: '成员',
  权限列表: []
})

// 角色权限映射
const rolePermissionsMap = {
  '负责人': ['查看团队信息', '编辑团队信息', '邀请成员', '移除成员', '管理成员角色', '查看成员列表', '查看团队统计'],
  '成员': ['查看团队信息', '查看成员列表']
}

const rules = {
  手机号: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (value && !isValidPhone(value)) {
          return Promise.reject('请输入有效的手机号')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  角色: [
    { required: true, message: '请选择成员角色', trigger: 'change' }
  ]
}

// 计算是否有邀请权限
const hasInvitePermission = computed(() => {
  console.log('🔍 InviteMemberModal 权限检查开始:', {
    'props.team': !!props.team,
    '团队id': props.team?.团队id,
    '权限状态字段存在': '权限状态' in (props.team || {}),
    '权限状态内容': props.team?.权限状态
  })
  
  if (!props.team) {
    console.log('❌ InviteMemberModal: props.team 为空')
    return false
  }
  
  // 优化：直接使用权限状态，避免hasPermission函数的复杂逻辑
  if (props.team.权限状态) {
    const 权限状态 = props.team.权限状态
    
    console.log('🔍 InviteMemberModal 权限检查详情:', {
      团队id: props.team.团队id,
      权限状态存在: !!权限状态,
      是否团队创建者: 权限状态.是否团队创建者,
      用户角色: 权限状态.用户角色,
      能否邀请成员: 权限状态.能否邀请成员,
      权限状态完整内容: 权限状态
    })
    
    // 创建者拥有所有权限
    if (权限状态.是否团队创建者 === true) {
      console.log('✅ InviteMemberModal: 创建者拥有邀请权限')
      return true
    }
    
    // 直接检查邀请成员权限（使用中文字段名）
    if (权限状态.能否邀请成员 === true) {
      console.log('✅ InviteMemberModal: 用户拥有邀请成员权限')
      return true
    }
    
    console.log('❌ InviteMemberModal: 用户没有邀请成员权限，权限状态检查失败')
    return false
  } else {
    console.log('⚠️ InviteMemberModal: props.team.权限状态 不存在，尝试降级检查')
  }
  
  // 降级到 hasPermission 函数，使用中文权限代码
  const result = hasPermission(props.team, '邀请成员')
  console.log('🔄 InviteMemberModal: 使用 hasPermission 函数检查结果:', result)
  return result
})

// 处理简化角色变更
const handleSimpleRoleChange = (role) => {
  form.角色 = role
  // 根据角色自动设置权限
  form.权限列表 = [...(rolePermissionsMap[role] || [])]
}

// 权限配置已简化为固定的角色权限映射
// 删除了重复的加载方法以消除冗余代码

// getRoleDisplayName 已从 roleUtils 导入，删除重复定义

// 获取邀请预览信息
const getInvitePreview = () => {
  if (!form.手机号 || !isValidPhone(form.手机号)) return ''
  
  return `将向 ${form.手机号} 发送邀请，角色为"${form.角色}"`
}

// 新增：邀请链接相关响应式数据
const inviteLinkVisible = ref(false)
const inviteLinkData = ref({})
const copyLoading = ref(false)

// 处理邀请
const handleInvite = async () => {
  try {
    await formRef.value.validate()
    loading.value = true

    // 权限验证：检查是否有邀请成员的权限
    if (!hasInvitePermission.value) {
      console.log('❌ handleInvite: 权限检查失败, hasInvitePermission.value =', hasInvitePermission.value)
      message.error('您没有邀请成员的权限，请联系团队管理员')
      return
    }
    
    console.log('✅ handleInvite: 权限检查通过, 开始发送邀请')

    // 简化的角色映射
    const roleTypeMap = {
      '负责人': 'leader',
      '成员': 'member'
    }

    const inviteData = {
      团队id: teamId.value,
      角色类型: roleTypeMap[form.角色] || 'member',
      权限列表: form.权限列表
    }

    // 统一邀请处理 - 只需手机号
    const response = await teamService.inviteByPhone({
      ...inviteData,
      手机号: form.手机号
    })

    if ([100, 0, 1].includes(response.status)) {
      // 检查是否为友好提示（如"已是团队成员"）
      if (response.data?.友好提示) {
        message.info(response.message)
        handleCancel()
      } else {
        // 邀请成功，显示邀请链接
        if (response.data && response.data.邀请链接) {
          inviteLinkData.value = {
            message: response.message || '邀请发送成功',
            邀请链接: response.data.邀请链接,
            邀请类型: response.data.邀请类型,
            过期时间: response.data.过期时间,
            角色: response.data.角色
          }
          inviteLinkVisible.value = true
        } else {
          message.success(response.message || '邀请发送成功')
        }
        emit('success')
        handleCancel()
      }
    } else {
      throw new Error(response.message || '邀请失败')
    }
  } catch (error) {
    console.error('邀请失败:', error)
    if (error.errorFields) {
      return
    }
    message.error('邀请失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 新增：复制邀请链接
const copyInviteLink = async () => {
  try {
    copyLoading.value = true
    await navigator.clipboard.writeText(inviteLinkData.value.邀请链接)
    message.success('邀请链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    // 降级处理：创建临时输入框进行复制
    const textArea = document.createElement('textarea')
    textArea.value = inviteLinkData.value.邀请链接
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    try {
      document.execCommand('copy')
      message.success('邀请链接已复制到剪贴板')
    } catch (fallbackError) {
      message.error('复制失败，请手动复制链接')
    }
    document.body.removeChild(textArea)
  } finally {
    copyLoading.value = false
  }
}

// 新增：复制链接并关闭弹窗
const copyAndClose = async () => {
  await copyInviteLink()
  inviteLinkVisible.value = false
}

// 新增：格式化过期时间
const formatExpireTime = (expireTime) => {
  if (!expireTime) return '无限期'
  try {
    const expireDate = dayjs(expireTime)
    const now = dayjs()
    const diffDays = expireDate.diff(now, 'day')
    
    if (diffDays > 0) {
      return `${diffDays}天后过期 (${expireDate.format('YYYY-MM-DD HH:mm')})`
    } else {
      return `已过期 (${expireDate.format('YYYY-MM-DD HH:mm')})`
    }
  } catch (error) {
    return expireTime
  }
}

// 处理取消
const handleCancel = () => {
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  Object.assign(form, {
    手机号: '',
    角色: '成员',
    权限列表: []
  })
  
  activePermissionKeys.value = []
  visible.value = false
}

// 生命周期  
onMounted(() => {
  // 初始化默认权限
  handleSimpleRoleChange(form.角色)
})
</script>

<style scoped>
.phone-help {
  margin-top: 4px;
}

/* 角色选择样式 */
.role-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.role-icon {
  font-size: 16px;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
}

.role-content {
  flex: 1;
}

.role-name {
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 2px;
}

.role-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
}

/* 选择器的统一样式调整 */
:deep(.ant-select-selection-item) {
  display: flex;
  align-items: center;
}

.user-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-weight: 500;
}

.user-phone {
  color: #666;
  font-size: 12px;
}

/* 权限配置样式 */
.permission-config-wrapper {
  width: 100%;
}

.simple-permission-config {
  margin-bottom: 16px;
}

.simple-permission-config .ant-radio-group {
  width: 100%;
}

.simple-permission-config .ant-radio {
  display: block;
  margin-bottom: 12px;
  width: 100%;
}

.permission-option {
  margin-left: 8px;
}

.option-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.option-desc {
  color: #666;
  font-size: 12px;
}

.detailed-permission-config {
  margin-top: 16px;
}

.permission-error-tip {
  margin-top: 8px;
}

.permission-group {
  width: 100%;
}

.permission-section {
  margin-bottom: 16px;
}

.permission-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.permission-section .ant-checkbox-wrapper {
  display: block;
  margin-bottom: 8px;
}

.permission-alert {
  margin-top: 16px;
}

.invite-preview {
  margin-top: 16px;
}

.invite-success-content {
  text-align: center;
}

.invite-link-info {
  text-align: left;
  margin-top: 16px;
}

.link-display {
  margin: 16px 0;
  padding: 16px;
  background-color: #f6f6f6;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.link-details {
  margin-top: 16px;
  text-align: left;
}

.link-details .ant-descriptions {
  background-color: #fafafa;
  padding: 12px;
  border-radius: 6px;
}
</style> 