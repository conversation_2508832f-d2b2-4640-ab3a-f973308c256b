<template>
  <div class="ranking-table">
    <a-table
      :columns="columns"
      :data-source="data"
      :loading="loading"
      :pagination="pagination"
      :scroll="{ x: 800 }"
      row-key="id"
      size="middle"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <!-- 排名列 -->
        <template v-if="column.key === 'rank'">
          <div class="rank-cell">
            <div class="rank-badge" :class="`rank-${getRankLevel(record.rank)}`">
              <component v-if="record.rank <= 3" :is="getRankIcon(record.rank)" />
              <span v-else>{{ record.rank }}</span>
            </div>
          </div>
        </template>
        
        <!-- 用户/团队信息列 -->
        <template v-else-if="column.key === 'info'">
          <div class="info-cell">
            <a-avatar :size="32" :src="record.avatar">
              {{ record.name?.charAt(0) }}
            </a-avatar>
            <div class="info-details">
              <div class="info-name">
                <a @click="handleUserClick(record)">{{ record.name }}</a>
              </div>
              <div class="info-team" v-if="type === 'individual'">{{ record.team }}</div>
              <div class="info-members" v-else>{{ record.memberCount }}人</div>
            </div>
          </div>
        </template>
        
        <!-- 销售额列 -->
        <template v-else-if="column.key === 'sales'">
          <div class="sales-cell">
            <span class="sales-value">{{ formatAmount(record.sales) }}</span>
            <div class="sales-bar">
              <div 
                class="sales-progress" 
                :style="{ width: getSalesProgress(record.sales) + '%' }"
              ></div>
            </div>
          </div>
        </template>
        
        <!-- 订单数列 -->
        <template v-else-if="column.key === 'orders'">
          <span class="orders-value">{{ record.orders }}单</span>
        </template>
        
        <!-- 佣金列 -->
        <template v-else-if="column.key === 'commission'">
          <span class="commission-value">{{ formatAmount(record.commission) }}</span>
        </template>
        
        <!-- 增长率列 -->
        <template v-else-if="column.key === 'growth'">
          <div class="growth-cell" :class="record.growth.trend">
            <component :is="getGrowthIcon(record.growth.trend)" />
            <span>{{ record.growth.display || formatGrowth(record.growth.rate) }}</span>
          </div>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { 
  CrownOutlined, 
  TrophyOutlined, 
  StarOutlined,
  ArrowUpOutlined, 
  ArrowDownOutlined, 
  MinusOutlined 
} from '@ant-design/icons-vue'
import gvmService from '../../services/gvmService'

defineOptions({
  name: 'RankingTable'
})

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    default: 'individual', // 'individual' | 'team'
    validator: (value) => ['individual', 'team'].includes(value)
  },
  sortBy: {
    type: String,
    default: 'sales'
  },
  loading: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['change', 'user-click'])

// 表格列配置
const columns = computed(() => [
  {
    title: '排名',
    key: 'rank',
    width: 80,
    fixed: 'left',
    align: 'center'
  },
  {
    title: props.type === 'individual' ? '用户' : '团队',
    key: 'info',
    width: 200,
    fixed: 'left'
  },
  {
    title: '销售额',
    key: 'sales',
    dataIndex: 'sales',
    width: 160,
    sorter: true,
    align: 'right'
  },
  {
    title: '订单数',
    key: 'orders',
    dataIndex: 'orders',
    width: 100,
    sorter: true,
    align: 'right'
  },
  {
    title: '佣金收入',
    key: 'commission',
    dataIndex: 'commission',
    width: 120,
    sorter: true,
    align: 'right'
  },
  {
    title: '增长率',
    key: 'growth',
    width: 100,
    sorter: true,
    align: 'center'
  }
])

// 计算最大销售额（用于进度条）
const maxSales = computed(() => {
  if (!props.data || props.data.length === 0) return 1
  return Math.max(...props.data.map(item => item.sales || 0))
})

// 获取排名等级
const getRankLevel = (rank) => {
  if (rank === 1) return 'gold'
  if (rank === 2) return 'silver'
  if (rank === 3) return 'bronze'
  return 'normal'
}

// 获取排名图标
const getRankIcon = (rank) => {
  switch (rank) {
    case 1:
      return CrownOutlined
    case 2:
      return TrophyOutlined
    case 3:
      return StarOutlined
    default:
      return null
  }
}

// 获取增长趋势图标
const getGrowthIcon = (trend) => {
  switch (trend) {
    case 'up':
      return ArrowUpOutlined
    case 'down':
      return ArrowDownOutlined
    default:
      return MinusOutlined
  }
}

// 格式化金额
const formatAmount = (amount) => {
  return gvmService.formatAmount(amount)
}

// 格式化增长率
const formatGrowth = (rate) => {
  if (!rate && rate !== 0) return '0%'
  return rate > 0 ? `+${rate.toFixed(1)}%` : `${rate.toFixed(1)}%`
}

// 获取销售额进度百分比
const getSalesProgress = (sales) => {
  return Math.min((sales / maxSales.value) * 100, 100)
}

// 事件处理
const handleTableChange = (pagination, filters, sorter) => {
  emit('change', { pagination, filters, sorter })
}

const handleUserClick = (user) => {
  emit('user-click', user)
}
</script>

<style scoped>
.ranking-table {
  width: 100%;
}

.rank-cell {
  display: flex;
  justify-content: center;
}

.rank-badge {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.rank-badge.rank-gold {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #8b4513;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.rank-badge.rank-silver {
  background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
  color: #666;
  box-shadow: 0 2px 8px rgba(192, 192, 192, 0.3);
}

.rank-badge.rank-bronze {
  background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
  color: #654321;
  box-shadow: 0 2px 8px rgba(205, 127, 50, 0.3);
}

.rank-badge.rank-normal {
  background: #f5f5f5;
  color: #666;
  font-size: 12px;
}

.info-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.info-details {
  flex: 1;
  min-width: 0;
}

.info-name {
  font-weight: 600;
  margin-bottom: 2px;
}

.info-name a {
  color: #1890ff;
  text-decoration: none;
}

.info-name a:hover {
  text-decoration: underline;
}

.info-team,
.info-members {
  font-size: 12px;
  color: #8c8c8c;
}

.sales-cell {
  text-align: right;
}

.sales-value {
  display: block;
  font-weight: 600;
  margin-bottom: 4px;
}

.sales-bar {
  width: 100%;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.sales-progress {
  height: 100%;
  background: linear-gradient(90deg, #1890ff 0%, #36cfc9 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.orders-value {
  font-weight: 500;
}

.commission-value {
  font-weight: 600;
  color: #52c41a;
}

.growth-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-weight: 500;
}

.growth-cell.up {
  color: #52c41a;
}

.growth-cell.down {
  color: #f5222d;
}

.growth-cell.stable {
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rank-badge {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .info-cell {
    gap: 8px;
  }
  
  .info-name {
    font-size: 13px;
  }
  
  .info-team,
  .info-members {
    font-size: 11px;
  }
  
  .sales-value {
    font-size: 13px;
  }
}
</style>
