import datetime
import http.client
import json
import traceback
import uuid
from typing import Any, Dict, List, Optional

from fastapi import HTTPException, status

import 状态  # 未使用的导入
from config import API_SPACE_TOKEN
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 数据.样品数据层 import 样品数据层实例
from 日志 import 应用日志器, 接口日志器, 错误日志器


class 异步样品信息服务:
    """处理样品信息相关的服务类"""

    @staticmethod
    def _获取审核状态文本(审核状态: int) -> str:
        """获取审核状态的文本描述"""
        状态映射 = {1: "通过", -1: "拒绝", 0: "待审核"}
        return 状态映射.get(审核状态, "未知")

    @staticmethod
    def 格式化结果集(结果集: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        格式化查询结果集，处理各种数据类型转换为JSON可序列化的格式

        参数:
            结果集: 数据库查询结果列表

        返回:
            处理后的结果列表
        """
        格式化后结果 = []

        for 记录 in 结果集:
            格式化记录 = {}

            # 复制原始记录
            for 键, 值 in 记录.items():
                # 处理datetime类型
                if isinstance(值, datetime.datetime):
                    格式化记录[键] = 值.strftime("%Y-%m-%d %H:%M:%S")
                # 处理date类型
                elif isinstance(值, datetime.date):
                    格式化记录[键] = 值.strftime("%Y-%m-%d")
                # 处理UUID类型
                elif isinstance(值, uuid.UUID):
                    格式化记录[键] = str(值)
                # 处理None值
                elif 值 is None:
                    格式化记录[键] = None
                # 处理其他可能不可序列化的类型
                else:
                    try:
                        # 尝试JSON序列化测试
                        import json

                        json.dumps(值)
                        格式化记录[键] = 值
                    except (TypeError, ValueError):
                        # 如果无法序列化，转换为字符串
                        格式化记录[键] = str(值)

            格式化后结果.append(格式化记录)

        return 格式化后结果

    @staticmethod
    async def 获取物流信息(
        快递公司代码: str, 快递单号: str, 收件人电话: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取物流跟踪信息

        参数:
            快递公司代码: 快递公司代码，如'YTO'表示圆通
            快递单号: 快递单号
            收件人电话: 收件人电话，可选

        返回:
            物流跟踪信息
        """
        try:
            # 记录接口调用
            接口日志器.info(
                f"开始查询物流信息: 快递公司={快递公司代码}, 单号={快递单号}"
            )

            # 准备请求参数
            payload = {"cpCode": 快递公司代码, "mailNo": 快递单号, "orderType": "asc"}

            # 如果提供了电话号码，则添加到请求参数
            if 收件人电话:
                payload["tel"] = 收件人电话

            # 将字典转换为JSON字符串
            json_payload = json.dumps(payload)

            # 准备HTTP连接
            conn = http.client.HTTPSConnection("eolink.o.apispace.com")

            # 设置请求头
            headers = {
                "X-APISpace-Token": API_SPACE_TOKEN,
                "Content-Type": "application/json",
            }

            # 记录请求信息（不包含敏感信息）
            接口日志器.debug(
                f"向第三方API发送请求: URL=eolink.o.apispace.com/wlgj1/paidtobuy_api/trace_search, 请求体={json_payload}"
            )

            # 发送请求
            conn.request(
                "POST", "/wlgj1/paidtobuy_api/trace_search", json_payload, headers
            )

            # 获取响应
            res = conn.getresponse()
            data = res.read()

            # 将响应数据转换为字典
            response_data = json.loads(data.decode("utf-8"))

            # 打印完整响应数据（用于调试）
            应用日志器.debug(f"物流API原始响应: {data.decode('utf-8')}")

            # 记录响应状态
            接口日志器.debug(
                f"第三方API响应: 状态码={res.status}, 响应大小={len(data)}"
            )

            # 检查 API 返回是否成功
            if response_data.get("success"):
                物流追踪信息 = response_data.get("logisticsTrace")
                # 检查物流追踪信息是否存在且不为空
                if (
                    物流追踪信息
                    and isinstance(物流追踪信息, dict)
                    and 物流追踪信息.get("logisticsTraceDetailList")
                ):
                    接口日志器.info(f"成功获取物流信息: 单号={快递单号}")
                    return {
                        "status": 状态.通用.成功,
                        "message": "获取物流信息成功",
                        "data": response_data,
                    }
                else:
                    接口日志器.warning(
                        f"物流API查询成功但无跟踪信息: 单号={快递单号}, 响应={response_data}"
                    )
                    # 虽然API成功，但没有实际物流信息，也应告知用户
                    return {
                        "status": 状态.物流.查询成功但无物流信息,
                        "message": response_data.get("reason")
                        or "查询成功，但暂无物流跟踪信息",
                        "data": response_data,  # 仍然返回原始数据供参考
                    }
            else:
                # API 返回失败
                错误原因 = response_data.get("reason", "未知错误")
                错误日志器.error(
                    f"物流API查询失败: 单号={快递单号}, 原因={错误原因}, 响应={response_data}"
                )
                # 根据API返回的错误原因决定返回给用户的消息
                return {
                    "status": 状态.物流.查询失败,
                    "message": f"物流查询失败: {错误原因}",
                    "data": response_data,  # 返回原始数据供排查
                }

        except json.JSONDecodeError as e:
            错误日志器.error(f"解析物流API响应失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "status": 状态.通用.服务器错误,
                    "message": "解析物流API响应失败",
                },
            )
        except Exception as e:
            错误信息 = traceback.format_exc()
            错误日志器.error(f"获取物流信息失败: {str(e)}\n{错误信息}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "status": 状态.通用.服务器错误,
                    "message": f"获取物流信息失败: {str(e)}",
                },
            )

    @staticmethod
    async def 获取快递公司代码(快递单号: str) -> str:
        """
        根据快递单号获取快递公司代码

        参数:
            快递单号: 快递单号字符串

        返回:
            快递公司代码字符串
        """
        try:
            # 记录接口调用
            接口日志器.info(f"开始识别快递公司: 单号={快递单号}")

            # 创建HTTPS连接
            conn = http.client.HTTPSConnection("eolink.o.apispace.com")

            # 准备请求数据
            payload = json.dumps({"mailNo": 快递单号})
            headers = {
                "X-APISpace-Token": API_SPACE_TOKEN,
                "Content-Type": "application/json",
            }

            # 记录请求信息
            接口日志器.debug(
                f"向第三方API发送请求: URL=eolink.o.apispace.com/wlgj1/paidtobuy_api/mail_discern, 请求体={payload}"
            )

            # 发送请求
            conn.request("POST", "/wlgj1/paidtobuy_api/mail_discern", payload, headers)

            # 获取响应
            res = conn.getresponse()
            data = res.read()

            # 解析响应
            响应数据 = json.loads(data.decode("utf-8"))

            # 记录响应状态和内容
            接口日志器.debug(
                f"第三方API响应: 状态码={res.status}, 响应大小={len(data)}, 响应内容={响应数据}"
            )

            # 新的响应格式解析逻辑
            if 响应数据.get("success") and "expressCompanyList" in 响应数据:
                公司列表 = 响应数据.get("expressCompanyList", [])
                if 公司列表 and len(公司列表) > 0:
                    快递公司代码 = 公司列表[0].get("cpCode")
                    快递公司名称 = 公司列表[0].get("companyName", "未知")
                    接口日志器.info(
                        f"成功识别快递公司: 代码={快递公司代码}, 名称={快递公司名称}"
                    )
                    return 快递公司代码
                else:
                    接口日志器.warning("快递公司列表为空")
                    return ""
            # 兼容旧的响应格式
            elif 响应数据.get("code") == 0:
                快递公司代码 = 响应数据.get("data", {}).get("comCode")
                快递公司名称 = 响应数据.get("data", {}).get("comName", "未知")
                接口日志器.info(
                    f"成功识别快递公司(旧格式): 代码={快递公司代码}, 名称={快递公司名称}"
                )
                return 快递公司代码
            else:
                错误信息 = 响应数据.get("msg", "未知错误")
                接口日志器.warning(f"识别快递公司失败: {错误信息}")
                return ""

        except Exception as e:
            错误信息 = traceback.format_exc()
            错误日志器.error(f"获取快递公司代码异常: {str(e)}\n{错误信息}")
            return ""
        finally:
            # 确保连接被关闭
            if "conn" in locals():
                conn.close()

    @staticmethod
    async def 保存寄样信息(
        收件人: str,
        地址: str,
        电话: str,
        产品id: int,
        数量: int,
        快递单号: str,
        寄样备注: Optional[str] = None,
        样品id: Optional[int] = None,
        用户审核状态: Optional[int] = 0,
        团队审核状态: Optional[int] = 0,
        微信产品对接进度表id: Optional[int] = None,
        快递状态: int = 0,
        快递状态变更时间: Optional[datetime.datetime] = None,
        用户联系人表id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        保存或更新寄样信息

        参数:
            收件人: 收件人姓名
            地址: 收件地址
            电话: 联系电话
            产品id: 产品id
            数量: 样品数量
            快递单号: 快递单号
            寄样备注: 寄样备注信息(可选)
            样品id: 样品记录id(提供时进行更新操作)
            用户审核状态: 用户审核状态(-1-拒绝、0-未审核、1-通过)，默认为0
            团队审核状态: 团队审核状态(-1-拒绝、0-未审核、1-通过)，默认为0
            微信产品对接进度表id: 微信产品对接进度表id
            快递状态: 快递状态(-1-已退回、0-待出库、1-运输中、2-已签收)，默认为0
            快递状态变更时间: 快递状态最后变更时间
            用户联系人表id: 用户联系人表id(可选)

        返回:
            操作结果
        """
        try:
            # 获取数据库连接
            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                当前时间 = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                if 样品id:
                    # 更新操作 - 先查询已有记录
                    # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
                    # {{ Source: context7-mcp on 'PostgreSQL Parameter Syntax' }}
                    现有记录 = await 连接.fetchrow(
                        """
                            SELECT * FROM 用户寄样信息表 WHERE id = $1
                            """,
                        样品id,
                    )

                    if not 现有记录:
                        return {
                            "status": 状态.通用.数据不存在,
                            "message": f"样品记录(ID: {样品id})不存在",
                            "data": None,
                        }

                    # 准备更新字段
                    更新字段 = {}
                    if 收件人:
                        更新字段["收件人"] = 收件人
                    if 地址:
                        更新字段["地址"] = 地址
                    if 电话:
                        更新字段["电话"] = 电话
                    if 产品id:
                        更新字段["用户产品表id"] = 产品id
                    if 数量:
                        更新字段["数量"] = 数量
                    if 快递单号:
                        更新字段["快递单号"] = 快递单号
                    if 寄样备注 is not None:
                        更新字段["寄样备注"] = 寄样备注

                    # 添加新字段的更新
                    if 用户审核状态 is not None:
                        更新字段["用户审核状态"] = 用户审核状态
                    if 团队审核状态 is not None:
                        更新字段["团队审核状态"] = 团队审核状态
                    if 用户联系人表id is not None:
                        更新字段["用户联系人表id"] = 用户联系人表id
                    if 微信产品对接进度表id is not None:
                        更新字段["微信产品对接进度表id"] = 微信产品对接进度表id
                    if 快递状态 is not None:
                        更新字段["快递状态"] = 快递状态
                        # 当快递状态发生变化时，自动更新状态变更时间
                        更新字段["快递状态变更时间"] = 当前时间
                    elif 快递状态变更时间 is not None:
                        if isinstance(快递状态变更时间, datetime.datetime):
                            更新字段["快递状态变更时间"] = 快递状态变更时间.strftime(
                                "%Y-%m-%d %H:%M:%S"
                            )
                        else:
                            更新字段["快递状态变更时间"] = 快递状态变更时间

                    # 如果没有任何字段需要更新，返回成功
                    if not 更新字段:
                        return {
                            "status": 状态.通用.成功,
                            "message": "无数据需要更新",
                            "data": {"id": 样品id},
                        }

                    # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
                    # {{ Source: context7-mcp on 'PostgreSQL Parameter Syntax' }}
                    # 构建更新SQL
                    设置语句 = ", ".join(
                        [
                            f'"{字段}" = ${i + 1}'
                            for i, 字段 in enumerate(更新字段.keys())
                        ]
                    )
                    SQL = f"""
                        UPDATE 用户寄样信息表
                        SET {设置语句}
                        WHERE id = ${len(更新字段) + 1}
                        """

                    # 执行更新
                    await 连接.execute(SQL, *list(更新字段.values()), 样品id)

                    # 日志记录
                    应用日志器.info(
                        f"更新样品记录 ID: {样品id}, 修改字段: {', '.join(更新字段.keys())}"
                    )

                    return {
                        "status": 状态.通用.成功,
                        "message": "更新样品信息成功",
                        "data": {"id": 样品id},
                    }
                else:
                    # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
                    # {{ Source: context7-mcp on 'PostgreSQL Parameter Syntax' }}
                    # 新增操作
                    SQL = """
                        INSERT INTO 用户寄样信息表
                        (收件人, 地址, 电话, 用户产品表id, 数量, 快递单号, 寄样备注, 创建时间,
                        用户审核状态, 团队审核状态, 快递状态, 快递状态变更时间, 用户联系人表id)
                        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                        RETURNING id
                        """

                    快递状态变更时间值 = None
                    if 快递状态 != 0:  # 如果快递状态不是默认值，则设置变更时间
                        快递状态变更时间值 = 当前时间
                    elif 快递状态变更时间 is not None:
                        if isinstance(快递状态变更时间, datetime.datetime):
                            快递状态变更时间值 = 快递状态变更时间.strftime(
                                "%Y-%m-%d %H:%M:%S"
                            )
                        else:
                            快递状态变更时间值 = 快递状态变更时间

                    # 处理快递状态变更时间：只有快递状态不为0时才设置变更时间
                    快递状态变更时间值 = 当前时间 if 快递状态 != 0 else None

                    # 执行插入并获取新ID
                    结果 = await 连接.fetchrow(
                        SQL,
                        收件人,
                        地址,
                        电话,
                        产品id,
                        数量,
                        快递单号,
                        寄样备注,
                        当前时间,
                        用户审核状态,
                        团队审核状态,
                        快递状态,
                        快递状态变更时间值,
                        用户联系人表id,
                    )

                    # 获取新插入的ID
                    新样品id = 结果["id"] if 结果 else None
                    if not 新样品id:
                        raise Exception("创建样品记录失败")

                    # 日志记录
                    应用日志器.info(
                        f"新增样品记录 ID: {新样品id}, 收件人: {收件人}, 快递单号: {快递单号}"
                    )

                    return {
                        "status": 状态.通用.成功,
                        "message": "保存样品信息成功",
                        "data": {"id": 新样品id},
                    }

        except Exception as e:
            错误信息 = traceback.format_exc()
            错误日志器.error(f"保存寄样信息失败: {str(e)}\n{错误信息}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "status": 状态.通用.服务器错误,
                    "message": f"保存寄样信息失败: {str(e)}",
                },
            )

    @staticmethod
    async def 获取样品信息记录列表(
        用户id: int,
        页码: int = 1,
        每页数量: int = 10,
        收件人: Optional[str] = None,
        产品id: Optional[int] = None,
        审核状态: Optional[int] = None,
        快递单号: Optional[str] = None,
        快递状态: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        获取指定用户的样品信息记录列表

        参数:
            用户id: 当前登录用户的ID (必需)
            页码: 页码，从1开始
            每页数量: 每页记录数量
            收件人: 收件人姓名查询条件，可选
            产品id: 产品id查询条件，可选
            审核状态: 审核状态查询条件(-1-拒绝、0-未审核、1-通过)，可选
            快递单号: 快递单号查询条件，可选
            快递状态: 快递状态查询条件(-1-已退回、0-待出库、1-运输中、2-已签收)，可选

        返回:
            分页记录列表及总数
        """
        try:
            # 调用数据层获取样品列表
            样品列表, 总记录数 = await 样品数据层实例.获取用户样品列表(
                用户id=用户id,
                页码=页码,
                每页数量=每页数量,
                收件人=收件人,
                产品id=产品id,
                审核状态=审核状态,
                快递单号=快递单号,
                快递状态=快递状态,
            )

            # 格式化返回数据
            格式化结果列表 = 异步样品信息服务.格式化结果集(样品列表)

            # 构建分页信息
            分页信息 = {
                "当前页": 页码,
                "每页条数": 每页数量,
                "总记录数": 总记录数,
                "总页数": (总记录数 + 每页数量 - 1) // 每页数量,
            }

            return {
                "status": 状态.通用.成功,
                "message": "获取样品信息记录列表成功",
                "data": {"列表": 格式化结果列表, "分页": 分页信息},
            }

        except Exception as e:
            错误信息 = traceback.format_exc()
            错误日志器.error(f"获取样品信息记录列表失败: {str(e)}\n{错误信息}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "status": 状态.通用.服务器错误,
                    "message": f"获取样品信息记录列表失败: {str(e)}",
                },
            )

    @staticmethod
    async def 获取样品信息记录详情(样品id: int) -> Dict[str, Any]:
        """
        获取样品信息记录详情

        参数:
            样品id: 样品记录id

        返回:
            样品信息记录详情
        """
        try:
            # 调用数据层获取样品详情
            记录 = await 样品数据层实例.获取样品详情(样品id)

            if not 记录:
                return {
                    "status": 状态.通用.成功,  # 使用成功状态，但返回空数据
                    "message": f"样品记录(ID: {样品id})不存在",
                    "data": None,
                }

            # 格式化时间字段
            格式化记录 = 异步样品信息服务.格式化结果集([记录])[0] if 记录 else {}

            # 获取关联的审核人信息（如果需要的话，可以在数据层中处理）
            # 这里暂时保持简单，只返回基本信息

            # 返回结果
            return {
                "status": 状态.通用.成功,
                "message": "获取样品信息记录详情成功",
                "data": 格式化记录,
            }

        except Exception as e:
            错误信息 = traceback.format_exc()
            错误日志器.error(f"获取样品信息记录详情失败: {str(e)}\n{错误信息}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "status": 状态.通用.服务器错误,
                    "message": f"获取样品信息记录详情失败: {str(e)}",
                },
            )

    @staticmethod
    async def 审核样品(样品id: int, 审核状态: int, 审核类型: str) -> Dict[str, Any]:
        """
        审核样品

        参数:
            样品id: 样品记录id
            审核状态: 审核状态(-1-拒绝、0-待审核、1-通过)
            审核类型: 审核类型("用户审核" 或 "团队审核")

        返回:
            审核结果
        """
        try:
            # 检查审核状态是否有效
            if 审核状态 not in [-1, 0, 1]:
                return {
                    "status": 状态.通用.参数错误,
                    "message": "无效的审核状态，有效值为：-1(拒绝)、0(待审核)、1(通过)",
                    "data": None,
                }

            # 检查审核类型是否有效
            if 审核类型 not in ["用户审核", "团队审核"]:
                return {
                    "status": 状态.通用.参数错误,
                    "message": "无效的审核类型，有效值为：用户审核、团队审核",
                    "data": None,
                }

            # 获取数据库连接
            async with 异步连接池实例.获取连接() as 连接:
                # 先查询记录是否存在
                样品记录 = await 连接.fetchrow(
                    """
                    SELECT * FROM 用户寄样信息表 WHERE id = $1
                    """,
                    样品id,
                )

                if not 样品记录:
                    return {
                        "status": 状态.通用.数据不存在,
                        "message": f"样品记录(ID: {样品id})不存在",
                        "data": None,
                    }

                # 根据审核类型调用数据层更新方法
                from 数据.样品数据层 import 样品数据层实例

                if 审核类型 == "用户审核":
                    更新成功 = await 样品数据层实例.更新样品审核状态(
                        样品id, 用户审核状态=审核状态
                    )
                else:  # 团队审核
                    更新成功 = await 样品数据层实例.更新样品审核状态(
                        样品id, 团队审核状态=审核状态
                    )

                if not 更新成功:
                    return {
                        "status": 状态.通用.操作失败,
                        "message": "更新审核状态失败",
                        "data": None,
                    }

                # 日志记录
                审核结果文本 = 异步样品信息服务._获取审核状态文本(审核状态)
                应用日志器.info(f"样品记录(ID: {样品id}){审核类型}{审核结果文本}")

                # 获取更新后的记录
                更新后记录 = await 连接.fetchrow(
                    """
                    SELECT * FROM 用户寄样信息表 WHERE id = $1
                    """,
                    样品id,
                )

                # 格式化时间字段
                格式化更新后记录 = (
                    异步样品信息服务.格式化结果集([更新后记录])[0] if 更新后记录 else {}
                )

                return {
                    "status": 状态.通用.成功,
                    "message": f"样品审核{审核结果文本}成功",
                    "data": 格式化更新后记录,
                }

        except Exception as e:
            错误信息 = traceback.format_exc()
            错误日志器.error(f"审核样品失败: {str(e)}\n{错误信息}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "status": 状态.通用.服务器错误,
                    "message": f"审核样品失败: {str(e)}",
                },
            )

    @staticmethod
    async def 更新快递状态(样品id: int, 快递状态: int) -> Dict[str, Any]:
        """
        手动更新样品快递状态

        参数:
            样品id: 样品记录id
            快递状态: 快递状态(-1-已退回、0-待出库、1-运输中、2-已签收)

        返回:
            更新结果
        """
        try:
            # 检查样品id是否有效
            if not 样品id or 样品id <= 0:
                return {
                    "status": 状态.通用.参数错误,
                    "message": f"无效的样品id: {样品id}，必须为正整数",
                    "data": None,
                }

            # 检查快递状态是否有效
            有效快递状态 = [-1, 0, 1, 2]
            if 快递状态 not in 有效快递状态:
                return {
                    "status": 状态.通用.参数错误,
                    "message": f"无效的快递状态: {快递状态}，有效值为: -1(已退回)、0(待出库)、1(运输中)、2(已签收)",
                    "data": None,
                }

            # 获取数据库连接
            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                # 先检查样品记录是否存在
                # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
                # {{ Source: context7-mcp on 'PostgreSQL Parameter Syntax' }}
                样品记录 = await 连接.fetchrow(
                    """
                    SELECT id, 用户产品表id, 快递状态, 快递单号, 收件人, 用户审核状态, 团队审核状态, 用户联系人表id
                    FROM 用户寄样信息表 WHERE id = $1
                    """,
                    样品id,
                )

                if not 样品记录:
                    return {
                        "status": 状态.通用.数据不存在,
                        "message": f"样品记录(ID: {样品id})不存在",
                        "data": None,
                    }

                # 检查样品审核状态 - 需要用户审核和团队审核都通过
                用户审核状态 = 样品记录.get("用户审核状态")
                团队审核状态 = 样品记录.get("团队审核状态")

                if (用户审核状态 != 1 or 团队审核状态 != 1) and 快递状态 > 0:
                    return {
                        "status": 状态.通用.操作失败,
                        "message": "样品未通过完整审核，不能更新为发货状态",
                        "data": {
                            "样品id": 样品id,
                            "用户审核状态": 用户审核状态,
                            "团队审核状态": 团队审核状态,
                            "审核状态说明": f"用户审核：{异步样品信息服务._获取审核状态文本(用户审核状态)}，团队审核：{异步样品信息服务._获取审核状态文本(团队审核状态)}",
                        },
                    }

                # 如果快递单号为空但想设置为运输中或已签收状态，则不允许
                if not 样品记录.get("快递单号") and 快递状态 > 0:
                    return {
                        "status": 状态.通用.操作失败,
                        "message": "样品尚未设置快递单号，不能更新为运输中或已签收状态",
                        "data": {
                            "样品id": 样品id,
                            "收件人": 样品记录.get("收件人", ""),
                        },
                    }

                # 如果当前状态与要更新的状态相同，则无需更新
                if 样品记录.get("快递状态") == 快递状态:
                    return {
                        "status": 状态.通用.成功,
                        "message": "快递状态未变更，保持当前状态",
                        "data": {
                            "样品id": 样品id,
                            "快递状态": 快递状态,
                            "快递状态文本": "已退回"
                            if 快递状态 == -1
                            else "待出库"
                            if 快递状态 == 0
                            else "运输中"
                            if 快递状态 == 1
                            else "已签收",
                        },
                    }

                # 记录原始状态
                原快递状态 = 样品记录.get("快递状态", 0)

                # 获取当前时间
                当前时间 = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # 更新快递状态
                更新结果 = await 连接.execute(
                    """
                    UPDATE 用户寄样信息表
                    SET 快递状态 = $1, 快递状态变更时间 = $2
                    WHERE id = $3
                    """,
                    快递状态,
                    当前时间,
                    样品id,
                )

                # 确认更新成功（PostgreSQL的execute返回影响的行数）
                if 更新结果 == "UPDATE 0":
                    return {
                        "status": 状态.通用.操作失败,
                        "message": "更新快递状态失败，数据未更改",
                        "data": None,
                    }

                # 记录日志
                应用日志器.info(
                    f"样品记录(ID: {样品id})快递状态从{原快递状态}变更为{快递状态}"
                )

                # 获取产品信息
                产品id = 样品记录.get("用户产品表id")
                if 产品id:
                    产品信息 = await 连接.fetchrow(
                        """
                        SELECT 产品名称 FROM 用户产品表 WHERE id = $1
                        """,
                        产品id,
                    )
                    产品名称 = 产品信息.get("产品名称", "") if 产品信息 else ""
                else:
                    产品名称 = ""

                # 构建快递状态文本描述
                快递状态文本 = (
                    "已退回"
                    if 快递状态 == -1
                    else "待出库"
                    if 快递状态 == 0
                    else "运输中"
                    if 快递状态 == 1
                    else "已签收"
                )
                原快递状态文本 = (
                    "已退回"
                    if 原快递状态 == -1
                    else "待出库"
                    if 原快递状态 == 0
                    else "运输中"
                    if 原快递状态 == 1
                    else "已签收"
                )

                # 如果状态变更为已签收(2)，则查找相关的微信产品对接进度记录并更新样品状态
                if 快递状态 == 2:
                    try:
                        # 通过用户寄样信息表id查找微信产品对接进度表记录
                        微信对接记录 = await 连接.fetchrow(
                            """
                            SELECT id FROM 微信产品对接进度表
                            WHERE 用户寄样信息表id = $1
                            """,
                            样品id,
                        )

                        if 微信对接记录:
                            # 更新微信产品对接进度表中的样品状态为"到样"(4)
                            await 连接.execute(
                                """
                                UPDATE 微信产品对接进度表
                                SET 样品状态 = 4
                                WHERE id = $1
                                """,
                                微信对接记录["id"],
                            )
                            应用日志器.info(
                                f"已更新微信产品对接进度表(ID: {微信对接记录['id']})的样品状态为到样"
                            )
                    except Exception as e:
                        错误日志器.error(
                            f"更新微信产品对接进度表样品状态失败: {str(e)}"
                        )

                # 如果有快递单号且状态为运输中，尝试获取最新物流信息
                最新物流信息 = None
                物流查询成功 = False
                if 样品记录.get("快递单号") and 快递状态 == 1:
                    try:
                        快递公司代码 = await 异步样品信息服务.获取快递公司代码(
                            样品记录.get("快递单号")
                        )
                        if 快递公司代码:
                            物流信息 = await 异步样品信息服务.获取物流信息(
                                快递公司代码, 样品记录.get("快递单号")
                            )
                            if 物流信息.get("status") == 状态.通用.成功:
                                # 物流信息查询成功，但当前不需要使用返回的数据
                                pass
                    except Exception as e:
                        错误日志器.warning(
                            f"获取物流信息失败，但不影响状态更新: {str(e)}"
                        )

                # 返回结果
                响应数据 = {
                    "样品id": 样品id,
                    "产品id": 产品id,
                    "产品名称": 产品名称,
                    "收件人": 样品记录.get("收件人", ""),
                    "快递单号": 样品记录.get("快递单号", ""),
                    "原快递状态": 原快递状态,
                    "原快递状态文本": 原快递状态文本,
                    "快递状态": 快递状态,
                    "快递状态文本": 快递状态文本,
                    "快递状态变更时间": 当前时间,
                }

                # 如果获取到了物流信息，加入响应
                if 最新物流信息:
                    响应数据["最新物流信息"] = 最新物流信息
                elif 快递状态 == 1 and 样品记录.get("快递单号") and not 物流查询成功:
                    响应数据["物流查询提示"] = (
                        "尝试获取物流信息未成功，请稍后再试或手动查询"
                    )

                return {
                    "status": 状态.通用.成功,
                    "message": f"快递状态已更新为: {快递状态文本}",
                    "data": 响应数据,
                }

        except Exception as e:
            错误信息 = traceback.format_exc()
            错误日志器.error(f"更新快递状态失败: {str(e)}\n{错误信息}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "status": 状态.通用.服务器错误,
                    "message": f"更新快递状态失败: {str(e)}",
                },
            )

    @staticmethod
    async def 更新样品物流状态(样品id: int, 物流信息: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新样品物流状态信息

        参数:
            样品id: 样品记录id
            物流信息: 物流API返回的信息

        返回:
            更新操作的结果
        """
        try:
            应用日志器.info(f"开始更新样品id {样品id} 的物流状态")

            # 从物流信息中提取需要的数据
            物流追踪信息 = 物流信息.get("logisticsTrace", {})
            物流状态标识 = 物流追踪信息.get("logisticsStatus", "")
            原始物流状态描述 = 物流追踪信息.get(
                "logisticsStatusDesc", ""
            )  # 获取原始描述
            物流跟踪详情列表 = 物流追踪信息.get("logisticsTraceDetailList", [])
            物流跟踪详情 = json.dumps(物流跟踪详情列表, ensure_ascii=False)
            快递公司代码 = 物流追踪信息.get("cpCode", "")

            # 物流状态标识映射到系统状态值
            状态映射 = {
                "WAIT": 0,  # 待出库
                "TRANSPORT": 1,  # 运输中
                "SIGN": 2,  # 已签收
                "RETURN": -1,  # 已退回
            }

            # 检查物流信息是否有效
            if not 物流跟踪详情列表:
                # 如果物流详情为空，则标记为单号异常
                应用日志器.warning(
                    f"样品id {样品id} 物流查询成功但无跟踪详情，标记为单号异常"
                )
                系统快递状态 = -10  # 或者可以定义一个新的状态码，比如 -10 代表异常
                物流状态描述 = "单号异常"
                物流状态标识 = "ABNORMAL"  # 定义一个内部标识
            else:
                # 物流信息有效，正常处理
                系统快递状态 = 状态映射.get(物流状态标识, 1)  # 默认为运输中
                物流状态描述 = 原始物流状态描述

            async with 异步连接池实例.获取连接() as 连接:
                # 检查样品记录是否存在
                样品记录 = await 连接.fetchrow(
                    """
                    SELECT id, 快递状态描述, 快递状态标识, 用户联系人表id
                    FROM 用户寄样信息表
                    WHERE id = $1
                    """,
                    样品id,
                )

                if not 样品记录:
                    return {
                        "status": 状态.通用.数据不存在,
                        "message": f"样品记录(ID: {样品id})不存在",
                    }

                # 判断物流状态是否有变化
                状态有变化 = 样品记录.get("快递状态标识") != 物流状态标识
                当前时间 = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
                # {{ Source: context7-mcp on 'PostgreSQL Parameter Syntax' }}
                # 更新快递状态信息
                更新SQL = """
                        UPDATE 用户寄样信息表
                        SET 快递状态 = $1,
                            快递状态标识 = $2,
                            快递状态描述 = $3,
                            物流跟踪详情 = $4,
                            快递公司 = $5
                    """

                参数列表 = [
                    系统快递状态,
                    物流状态标识,
                    物流状态描述,
                    物流跟踪详情,
                    快递公司代码,
                ]

                # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
                # {{ Source: context7-mcp on 'PostgreSQL Parameter Syntax' }}
                # 如果状态有变化，更新状态变更时间
                if 状态有变化:
                    更新SQL += ", 快递状态变更时间 = $6"
                    参数列表.append(当前时间)

                更新SQL += " WHERE id = $1"
                参数列表.append(样品id)

                # 执行更新
                应用日志器.debug(f"更新SQL: {更新SQL}, 参数: {参数列表}")
                await 连接.execute(更新SQL, *参数列表)

                # 如果状态变更为已签收(2)，则查找相关的微信产品对接进度记录并更新样品状态
                if 系统快递状态 == 2:
                    try:
                        # 通过用户寄样信息表id查找微信产品对接进度表记录
                        微信对接记录 = await 连接.fetchrow(
                            """
                            SELECT id FROM 微信产品对接进度表
                            WHERE 用户寄样信息表id = $1
                            """,
                            样品id,
                        )

                        if 微信对接记录:
                            # 更新微信产品对接进度表中的样品状态为"到样"(4)
                            await 连接.execute(
                                """
                                UPDATE 微信产品对接进度表
                                SET 样品状态 = 4
                                WHERE id = $1
                                """,
                                微信对接记录["id"],
                            )
                            应用日志器.info(
                                f"已更新微信产品对接进度表(ID: {微信对接记录['id']})的样品状态为到样"
                            )
                    except Exception as e:
                        错误日志器.error(
                            f"更新微信产品对接进度表样品状态失败: {str(e)}"
                        )

                    return {
                        "status": 状态.通用.成功,
                        "message": "物流状态更新成功",
                        "data": {
                            "样品id": 样品id,
                            "快递状态标识": 系统快递状态,
                            "快递状态描述": 物流状态描述,
                            "状态有变化": 状态有变化,
                        },
                    }

        except Exception as e:
            错误信息 = traceback.format_exc()
            错误日志器.error(f"更新样品物流状态失败: {str(e)}\n{错误信息}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "status": 状态.通用.服务器错误,
                    "message": f"更新样品物流状态失败: {str(e)}",
                },
            )

    @staticmethod
    async def 更新快递单号(样品id: int, 快递单号: str) -> Dict[str, Any]:
        """
        更新样品信息记录的快递单号

        参数:
            样品id: 样品记录id
            快递单号: 新的快递单号

        返回:
            包含操作结果的字典
        """
        try:
            应用日志器.info(f"开始更新样品id {样品id} 的快递单号为 {快递单号}")

            # 首先检查样品id是否存在
            # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
            # {{ Source: context7-mcp on 'PostgreSQL Parameter Syntax' }}
            检查查询 = """
                SELECT COUNT(*) AS 记录数
                FROM 用户寄样信息表
                WHERE id = $1
            """

            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                结果 = await 连接.fetchrow(检查查询, 样品id)

                if not 结果 or 结果["记录数"] == 0:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail={
                            "status": 状态.通用.记录不存在,
                            "message": f"样品id {样品id} 不存在",
                        },
                    )

                # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
                # {{ Source: context7-mcp on 'PostgreSQL Parameter Syntax' }}
                # 更新快递单号
                更新查询 = """
                    UPDATE 用户寄样信息表
                    SET 快递单号 = $1,
                        更新时间 = NOW()
                    WHERE id = $2
                """

                更新结果 = await 连接.execute(更新查询, 快递单号, 样品id)

                # 检查是否成功更新（PostgreSQL的execute返回影响的行数）
                if 更新结果 == "UPDATE 0":
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail={
                            "status": 状态.通用.操作失败,
                            "message": "更新快递单号失败",
                        },
                    )

                应用日志器.info(f"成功更新样品id {样品id} 的快递单号")

                # 获取物流信息并更新物流状态
                try:
                    # 获取快递公司代码
                    快递公司代码 = await 异步样品信息服务.获取快递公司代码(快递单号)

                    if 快递公司代码:
                        # 获取物流信息
                        物流信息结果 = await 异步样品信息服务.获取物流信息(
                            快递公司代码, 快递单号
                        )

                        if 物流信息结果.get("status") == 状态.通用.成功:
                            # 更新物流状态
                            物流状态结果 = await 异步样品信息服务.更新样品物流状态(
                                样品id, 物流信息结果.get("data", {})
                            )

                            return {
                                "status": 状态.通用.成功,
                                "message": "快递单号更新成功，物流状态已自动更新",
                                "data": {
                                    "快递单号更新": True,
                                    "物流状态更新": 物流状态结果.get("status")
                                    == 状态.通用.成功,
                                    "物流状态信息": 物流状态结果.get("data"),
                                },
                            }
                    else:  # 如果快递公司代码获取失败
                        应用日志器.warning(
                            f"样品id {样品id} 的新快递单号 {快递单号} 无法识别，将更新快递状态描述"
                        )
                        # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
                        # {{ Source: context7-mcp on 'PostgreSQL Parameter Syntax' }}
                        更新描述SQL = """
                                UPDATE 用户寄样信息表
                                SET 快递状态描述 = $1
                                WHERE id = $2
                            """
                        await 连接.execute(
                            更新描述SQL, "单号有误，无法识别快递公司", 样品id
                        )
                        # 返回不同的成功消息
                        return {
                            "status": 状态.通用.成功,
                            "message": "快递单号已更新，但无法识别或格式有误，物流状态未更新",
                            "data": {
                                "快递单号更新": True,
                                "物流状态更新": False,
                                "快递状态描述": "单号有误，无法识别快递公司",
                            },
                        }

                except Exception as e:
                    错误日志器.warning(
                        f"自动更新物流状态失败，但不影响快递单号更新: {str(e)}"
                    )

                # 如果尝试自动更新物流失败（例如API异常）或未执行（例如公司代码有效但获取物流失败）
                return {
                    "status": 状态.通用.成功,
                    "message": "快递单号更新成功，自动更新物流状态时遇到问题或未执行",
                }

        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            错误信息 = traceback.format_exc()
            错误日志器.error(f"更新快递单号时发生错误: {str(e)}\n{错误信息}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "status": 状态.通用.服务器错误,
                    "message": f"更新快递单号失败: {str(e)}",
                },
            )

    async def 获取用户样品统计(self, 用户id: int) -> Dict[str, Any]:
        """
        获取用户样品统计信息

        参数:
            用户id: 用户id

        返回:
            样品统计数据字典
        """
        try:
            接口日志器.info(f"开始获取用户样品统计信息，用户id: {用户id}")

            # 调用数据层获取统计信息
            统计数据 = await 样品数据层实例.获取样品统计信息(用户id)

            if 统计数据 and 统计数据.get("总数", 0) > 0:
                结果 = {
                    "总数": 统计数据.get("总数", 0),
                    "待审核": 统计数据.get("待审核", 0),
                    "已发货": 统计数据.get("已发货", 0),
                    "已送达": 统计数据.get("已签收", 0),  # 映射到前端期望的字段名
                }
                接口日志器.info(f"获取用户样品统计成功: {结果}")
                return 结果
            else:
                # 返回默认值
                默认结果 = {"总数": 0, "待审核": 0, "已发货": 0, "已送达": 0}
                接口日志器.info(f"用户无样品数据，返回默认统计: {默认结果}")
                return 默认结果

        except Exception as e:
            错误信息 = traceback.format_exc()
            错误日志器.error(f"获取用户样品统计时发生错误: {str(e)}\n{错误信息}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "status": 状态.通用.服务器错误,
                    "message": f"获取样品统计失败: {str(e)}",
                },
            )
