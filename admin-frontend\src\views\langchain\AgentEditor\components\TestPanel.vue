<template>
  <div :class="$style.testPanel">
    <!-- 测试面板头部 -->
    <div :class="$style.header">
      <div :class="$style.title">
        <span>🤖 智能体测试</span>
        <a-badge :count="totalMessages" :offset="[10, 0]" />
      </div>
      <div :class="$style.controls">
        <a-select
          v-model:value="testMode"
          size="small"
          :class="$style.modeSelect"
          @change="handleModeChange"
        >
          <a-select-option value="user">👤 用户对话</a-select-option>
        </a-select>
        <a-button
          size="small"
          type="text"
          @click="scrollToBottom"
          :class="$style.scrollBtn"
          title="滚动到底部"
        >
          ⬇️
        </a-button>
        <a-button
          size="small"
          type="text"
          @click="clearHistory"
          :class="$style.clearBtn"
        >
          清空
        </a-button>
      </div>
    </div>

    <!-- 统一的对话区域 -->
    <div :class="$style.chatContainer">
      <!-- 消息列表 -->
      <div :class="$style.messages" ref="messagesRef">
        <div v-if="currentMessages.length === 0" :class="$style.emptyState">
          <div :class="$style.emptyIcon">{{ getModeIcon(testMode) }}</div>
          <div :class="$style.emptyText">{{ getModeDescription(testMode) }}</div>
        </div>

        <div
          v-for="(msg, index) in currentMessages"
          :key="index"
          :class="[$style.message, $style[msg.role]]"
        >
          <div :class="$style.messageContent">
            <div :class="$style.content">{{ msg.content }}</div>
            <div :class="$style.meta">
              <span :class="$style.time">{{ msg.timestamp }}</span>
              <span v-if="msg.tokens" :class="$style.tokens">{{ msg.tokens }} tokens</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div :class="$style.inputArea">
        <!-- 测试用户ID设置 -->
        <div :class="$style.userIdSetting">
          <a-input
            v-model:value="testUserId"
            placeholder="测试用户ID（可选，不填则使用管理员ID）"
            size="small"
            :class="$style.userIdInput"
            allow-clear
          >
            <template #prefix>
              <span :class="$style.userIdPrefix">👤</span>
            </template>
          </a-input>
          <a-tooltip title="管理员可以指定用户ID来模拟不同用户的对话场景">
            <a-button size="small" type="text" :class="$style.helpBtn">
              ❓
            </a-button>
          </a-tooltip>
        </div>

        <!-- 自定义变量输入区域 -->
        <div v-if="hasCustomVariables" :class="$style.variablesInput">
          <a-collapse ghost>
            <a-collapse-panel key="variables" header="🔧 自定义变量">
              <div :class="$style.variablesList">
                <div
                  v-for="variable in customVariables"
                  :key="variable.变量名"
                  :class="$style.variableItem"
                >
                  <a-form-item
                    :label="variable.变量名"
                    :class="$style.variableFormItem"
                  >
                    <a-input
                      v-if="variable.变量类型 === 'string'"
                      v-model:value="variableValues[variable.变量名]"
                      :placeholder="variable.默认值 || `请输入${variable.变量名}`"
                      size="small"
                    />
                    <a-input-number
                      v-else-if="variable.变量类型 === 'number'"
                      v-model:value="variableValues[variable.变量名]"
                      :placeholder="variable.默认值 || `请输入${variable.变量名}`"
                      size="small"
                      style="width: 100%"
                    />
                    <a-switch
                      v-else-if="variable.变量类型 === 'boolean'"
                      v-model:checked="variableValues[variable.变量名]"
                      size="small"
                    />
                    <a-textarea
                      v-else-if="variable.变量类型 === 'text'"
                      v-model:value="variableValues[variable.变量名]"
                      :placeholder="variable.默认值 || `请输入${variable.变量名}`"
                      :auto-size="{ minRows: 2, maxRows: 4 }"
                      size="small"
                    />
                    <a-input
                      v-else
                      v-model:value="variableValues[variable.变量名]"
                      :placeholder="variable.默认值 || `请输入${variable.变量名}`"
                      size="small"
                    />
                    <div v-if="variable.变量描述" :class="$style.variableDesc">
                      {{ variable.变量描述 }}
                    </div>
                  </a-form-item>
                </div>
              </div>
              <div :class="$style.variableActions">
                <a-button
                  size="small"
                  @click="resetVariables"
                  :class="$style.resetBtn"
                >
                  重置变量
                </a-button>
                <a-button
                  size="small"
                  type="primary"
                  ghost
                  @click="fillDefaultValues"
                  :class="$style.fillBtn"
                >
                  填充默认值
                </a-button>
              </div>
            </a-collapse-panel>
          </a-collapse>
        </div>

        <!-- 用户对话输入 -->
        <div :class="$style.chatInput">
          <a-textarea
            v-model:value="inputMessage"
            placeholder="输入消息测试用户对话接口..."
            :auto-size="{ minRows: 1, maxRows: 4 }"
            @press-enter="handleSendMessage"
            :disabled="isLoading"
            :class="$style.input"
          />
          <a-button
            type="primary"
            :loading="isLoading"
            @click="handleSendMessage"
            :class="$style.sendBtn"
          >
            发送
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import adminLangchainService from '@/services/adminLangchainService'
import { message } from 'ant-design-vue'
import { computed, nextTick, ref, watch } from 'vue'

// Props
const props = defineProps({
  agentId: {
    type: [String, Number],
    default: null
  },
  agentForm: {
    type: Object,
    default: () => ({})
  }
})

// 测试模式
const testMode = ref('user')
const inputMessage = ref('')
const testUserId = ref('')
const isLoading = ref(false)

// 消息历史
const userMessages = ref([])

// 消息容器引用
const messagesRef = ref(null)

// 自定义变量相关
const variableValues = ref({})

// 计算属性
const currentMessages = computed(() => {
  return userMessages.value
})

const totalMessages = computed(() => {
  return userMessages.value.length
})

// 自定义变量相关计算属性
const customVariables = computed(() => {
  return props.agentForm?.自定义变量 || []
})

const hasCustomVariables = computed(() => {
  return customVariables.value.length > 0
})

// 调试工具：获取组件状态信息
const getDebugInfo = () => {
  return {
    testMode: testMode.value,
    inputMessage: inputMessage.value,
    isLoading: isLoading.value,
    totalMessages: totalMessages.value,
    timestamp: new Date().toISOString()
  }
}

// 开发环境下暴露调试工具到全局
if (process.env.NODE_ENV === 'development') {
  window.TestPanelDebug = {
    getDebugInfo
  }
}

// 自定义变量管理方法
const resetVariables = () => {
  variableValues.value = {}
  message.success('变量已重置')
}

const fillDefaultValues = () => {
  customVariables.value.forEach(variable => {
    if (variable.默认值 !== undefined && variable.默认值 !== null) {
      variableValues.value[variable.变量名] = variable.默认值
    }
  })
  message.success('已填充默认值')
}

const initializeVariables = () => {
  // 初始化变量值
  customVariables.value.forEach(variable => {
    if (!(variable.变量名 in variableValues.value)) {
      // 根据变量类型设置初始值
      switch (variable.变量类型) {
        case 'string':
        case 'text':
          variableValues.value[variable.变量名] = variable.默认值 || ''
          break
        case 'number':
          variableValues.value[variable.变量名] = variable.默认值 || 0
          break
        case 'boolean':
          variableValues.value[variable.变量名] = variable.默认值 || false
          break
        default:
          variableValues.value[variable.变量名] = variable.默认值 || ''
      }
    }
  })
}

// 工具方法
const getModeIcon = () => {
  return '👤'
}

const getModeDescription = () => {
  return '发送消息测试用户对话接口'
}

// 监听自定义变量变化，自动初始化
watch(
  () => props.agentForm?.自定义变量,
  (newVariables) => {
    if (newVariables && newVariables.length > 0) {
      initializeVariables()
    }
  },
  { immediate: true, deep: true }
)

// 添加消息到历史
const addMessage = (content, role = 'user', tokens = null) => {
  const msg = {
    content,
    role,
    timestamp: new Date().toLocaleTimeString(),
    tokens
  }

  userMessages.value.push(msg)

  // 平滑滚动到底部
  nextTick(() => {
    if (messagesRef.value) {
      // 使用平滑滚动
      messagesRef.value.scrollTo({
        top: messagesRef.value.scrollHeight,
        behavior: 'smooth'
      })
    }
  })
}

// 处理模式切换
const handleModeChange = (mode) => {
  console.log(`[TestPanel] 切换测试模式: ${testMode.value} -> ${mode}`)
  testMode.value = mode
  inputMessage.value = ''
}

// 清空历史
const clearHistory = () => {
  userMessages.value = []
}

// 手动滚动到底部的方法
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesRef.value) {
      messagesRef.value.scrollTo({
        top: messagesRef.value.scrollHeight,
        behavior: 'smooth'
      })
    }
  })
}

// 发送消息
const handleSendMessage = async () => {
  if (!inputMessage.value.trim()) {
    message.warning('请输入测试消息')
    return
  }

  if (!props.agentId) {
    message.warning('请先保存智能体后再进行测试')
    return
  }

  try {
    isLoading.value = true

    // 添加用户消息，显示测试用户信息和变量信息
    const userInfo = testUserId.value ? `[测试用户: ${testUserId.value}]` : '[管理员测试]'
    const variableInfo = hasCustomVariables.value && Object.keys(variableValues.value).some(key =>
      variableValues.value[key] !== undefined && variableValues.value[key] !== null && variableValues.value[key] !== ''
    ) ? ' 🔧' : ''
    addMessage(`${userInfo}${variableInfo} ${inputMessage.value}`, 'user')

    const messageContent = inputMessage.value
    inputMessage.value = ''

    // 构建请求数据，支持自定义用户ID和自定义变量
    const requestData = {
      消息: messageContent,
      会话id: null,
      上下文信息: null
    }

    // 如果指定了测试用户ID，则添加到请求中
    if (testUserId.value && testUserId.value.trim()) {
      requestData.测试用户id = testUserId.value.trim()
    }

    // 添加自定义变量到请求中
    if (hasCustomVariables.value) {
      const 有效变量 = {}
      customVariables.value.forEach(variable => {
        const value = variableValues.value[variable.变量名]
        if (value !== undefined && value !== null && value !== '') {
          有效变量[variable.变量名] = value
        }
      })

      if (Object.keys(有效变量).length > 0) {
        requestData.自定义变量 = 有效变量
        console.log('🔧 发送自定义变量:', 有效变量)
      }
    }

    // 调用管理员测试对话接口（支持用户ID覆盖）
    const response = await adminLangchainService.chatWithAdminTest(props.agentId, requestData)

    // 添加助手回复
    if (response && response.success) {
      // 用户对话接口返回的数据结构：data.对话信息.智能体回复
      const 对话信息 = response.data?.对话信息 || {}
      const 使用统计 = response.data?.使用统计 || {}
      const 测试信息 = response.data?.测试信息 || {}

      const replyContent = 对话信息.智能体回复 || response.data?.智能体回复 || '智能体回复异常'
      const tokens = 使用统计.令牌消耗 || response.data?.令牌消耗 || null

      // 如果是测试模式，显示测试信息
      let displayContent = replyContent
      if (测试信息.测试模式) {
        const testInfo = 测试信息.是否模拟用户 ?
          `[模拟用户${测试信息.实际用户id}回复]` :
          `[管理员${测试信息.管理员id}回复]`
        displayContent = `${testInfo}\n${replyContent}`
      }

      addMessage(displayContent, 'assistant', tokens)
    } else {
      addMessage(`错误: ${response?.error || '接口调用失败'}`, 'system')
    }

  } catch (error) {
    console.error('[TestPanel] 消息发送失败:', error)
    addMessage(`测试失败: ${error.message}`, 'system')
    message.error('测试失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}


</script>

<style module>
.testPanel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-left: 1px solid #e8e8e8;
  position: relative;
}

.header {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #262626;
}

.controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.modeSelect {
  min-width: 120px;
}

.scrollBtn {
  color: #1890ff;
  font-size: 12px;
  transition: all 0.2s ease;
}

.scrollBtn:hover {
  color: #40a9ff;
  background-color: #f0f8ff;
}

.clearBtn {
  color: #8c8c8c;
}

.chatContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  gap: 12px;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 transparent;
  /* 平滑滚动 */
  scroll-behavior: smooth;
  /* 确保有最小高度以触发滚动 */
  min-height: 200px;
  /* 最大高度限制，确保不会无限增长 */
  max-height: calc(100vh - 300px);
}

/* Webkit浏览器滚动条样式 */
.messages::-webkit-scrollbar {
  width: 6px;
}

.messages::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.messages::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.messages::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 滚动阴影效果 */
.messages::before,
.messages::after {
  content: '';
  position: sticky;
  left: 0;
  right: 0;
  height: 10px;
  pointer-events: none;
  z-index: 1;
}

.messages::before {
  top: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), transparent);
}

.messages::after {
  bottom: 0;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.8), transparent);
}

.emptyState {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #8c8c8c;
  text-align: center;
}

.emptyIcon {
  font-size: 48px;
  margin-bottom: 16px;
}

.emptyText {
  font-size: 14px;
}

.message {
  display: flex;
  margin-bottom: 12px;
}

.message.user {
  justify-content: flex-end;
}

.message.assistant {
  justify-content: flex-start;
}

.message.system {
  justify-content: center;
}

.messageContent {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 12px;
  word-wrap: break-word;
}

.message.user .messageContent {
  background: #1890ff;
  color: white;
}

.message.assistant .messageContent {
  background: #f6f6f6;
  color: #262626;
}

.message.system .messageContent {
  background: #fff2e8;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.content {
  white-space: pre-wrap;
  line-height: 1.5;
}

.meta {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  opacity: 0.7;
}

.time {
  color: inherit;
}

.tokens {
  color: inherit;
  font-weight: 500;
}

.inputArea {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.userIdSetting {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 12px;
  padding: 10px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.userIdInput {
  flex: 1;
}

.userIdPrefix {
  color: #1890ff;
  font-size: 14px;
}

.helpBtn {
  padding: 0 4px;
  color: #8c8c8c;
  font-size: 12px;
}

.helpBtn:hover {
  color: #1890ff;
}

.errorState {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: #fff2e8;
  border: 1px solid #ffd591;
  border-radius: 6px;
  margin-bottom: 16px;
}

.errorAlert {
  margin-bottom: 0;
}

.chatInput {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.input {
  flex: 1;
}

.sendBtn {
  flex-shrink: 0;
}

/* 自定义变量输入区域样式 */
.variablesInput {
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 12px;
}

.variablesList {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 12px;
}

.variableItem {
  background: #fafafa;
  border-radius: 6px;
  padding: 8px;
}

.variableFormItem {
  margin-bottom: 0 !important;
}

.variableFormItem :global(.ant-form-item-label) {
  padding-bottom: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

.variableFormItem :global(.ant-form-item-control) {
  line-height: 1.2;
}

.variableDesc {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  line-height: 1.3;
}

.variableActions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.resetBtn,
.fillBtn {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .testPanel {
    border-left: none;
    border-top: 1px solid #e8e8e8;
  }

  .header {
    padding: 8px 12px;
  }

  .title {
    font-size: 14px;
  }

  .modeSelect {
    min-width: 100px;
  }

  .messages {
    padding: 12px;
    /* 移动端调整最大高度 */
    max-height: calc(100vh - 250px);
    min-height: 150px;
  }

  .inputArea {
    padding: 12px;
  }

  /* 移动端滚动条样式调整 */
  .messages::-webkit-scrollbar {
    width: 4px;
  }
}

@media (max-width: 480px) {
  .header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .controls {
    justify-content: space-between;
  }



  .input {
    min-height: 60px;
  }
}
</style>