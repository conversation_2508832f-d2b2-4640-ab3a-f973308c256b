<template>
  <div class="agent-editor">
    <!-- 顶部操作栏 -->
    <div class="editor-header">
      <div class="header-main">
        <!-- 返回按钮 -->
        <a-button type="text" @click="返回列表" class="back-btn" size="small">
          <template #icon><ArrowLeftOutlined /></template>
        </a-button>

        <!-- 智能体基础信息 -->
        <div class="agent-basic-info">
          <div class="agent-name-section">
            <a-input
              v-model:value="智能体表单.智能体名称"
              :placeholder="编辑模式 ? '编辑智能体名称' : '请输入智能体名称'"
              :maxlength="50"
              class="agent-name-input"
              @blur="() => 验证智能体名称(智能体表单.智能体名称)"
            />
            <a-tag v-if="编辑模式" color="blue" size="small" class="id-tag">
              ID: {{ 智能体id }}
            </a-tag>
          </div>
          <div class="agent-description-section">
            <a-input
              v-model:value="智能体表单.智能体描述"
              placeholder="简要描述智能体的功能和用途"
              :maxlength="200"
              class="description-input"
              @blur="() => 验证智能体描述(智能体表单.智能体描述)"
            />
          </div>
        </div>

        <!-- 状态指示器 -->
        <div class="status-indicators" v-if="编辑模式">
          <a-space size="small">
            <a-badge :status="智能体表单.对话模型id ? 'success' : 'default'" text="模型" />
            <a-badge :status="智能体表单.启用rag ? 'success' : 'default'" text="RAG" />
            <a-badge :status="智能体表单.输出模式 === 'pydantic' ? 'success' : 'default'" text="结构化" />
            <a-badge :status="智能体表单.工具列表?.length > 0 ? 'success' : 'default'" text="工具" />
          </a-space>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="header-actions">
        <a-space size="small">
          <!-- 工具菜单 -->
          <a-dropdown v-if="编辑模式" placement="bottomRight">
            <a-button type="text" size="small" class="tool-btn">
              <template #icon><ToolOutlined /></template>
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="设置测试Token">
                  <template #icon>🔑</template>
                  设置Token
                </a-menu-item>
                <a-menu-item @click="调试API">
                  <template #icon>🔧</template>
                  调试API
                </a-menu-item>
                <a-menu-item @click="导出配置">
                  <template #icon>📤</template>
                  导出配置
                </a-menu-item>
                <a-menu-item @click="查看使用统计">
                  <template #icon>📊</template>
                  使用统计
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>

          <!-- 重置按钮 -->
          <a-button type="text" @click="重置表单" :disabled="表单状态.保存中 || 表单状态.加载中" size="small" class="reset-btn">
            <template #icon><ReloadOutlined /></template>
          </a-button>

          <!-- 保存按钮 -->
          <a-button
            type="primary"
            @click="保存智能体"
            :loading="表单状态.保存中"
            :disabled="表单状态.加载中 || !表单是否有效"
            size="small"
            class="save-btn"
          >
            <template #icon><SaveOutlined /></template>
            {{ 编辑模式 ? '保存' : '创建' }}
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 保存成功的全局提示 -->
    <div v-if="表单状态.最近保存状态" :class="['global-status-bar', 表单状态.最近保存状态.type]">
      <a-space>
        <span v-if="表单状态.最近保存状态.type === 'success'">✅</span>
        <span v-else-if="表单状态.最近保存状态.type === 'error'">❌</span>
        <span v-else>⚠️</span>
        <span>{{ 表单状态.最近保存状态.message }}</span>
        <a-button v-if="表单状态.最近保存状态.action" type="link" size="small" @click="表单状态.最近保存状态.action.handler">
          {{ 表单状态.最近保存状态.action.text }}
        </a-button>
      </a-space>
      <a-button type="text" size="small" @click="表单状态.最近保存状态 = null">
        <CloseOutlined />
      </a-button>
    </div>

    <!-- 主要内容区域 - 三栏布局 -->
    <div class="main-content">
      <!-- 左侧导航栏组件 -->
      <ConfigNavigation
        :current-step="当前配置步骤"
        :agent-form="智能体表单"
        @step-change="切换配置步骤"
        @reset="重置配置"
        @export="导出配置"
      />

      <!-- 中间配置区域组件 -->
      <ConfigContent
        :current-step="当前配置步骤"
        :agent-form="智能体表单"
        :agent-id="智能体id"
        :loading="表单状态.加载中"
        :model-options="对话模型列表"
        :knowledge-base-options="可用知识库列表"
        :available-tools="可用工具列表"
        :tools-detail="工具详情数据"
        :embedding-model-options="嵌入模型列表"
        :auto-save="保存智能体"
        @form-change="处理表单变化"
        @tool-toggle="处理工具切换"
      />

      <!-- 右侧测试面板组件 -->
      <TestPanel
        :agent-id="智能体id"
        :agent-form="智能体表单"
      />
    </div>
  </div>
</template>

<script setup>
import {
  ArrowLeftOutlined,
  CloseOutlined,
  ReloadOutlined,
  SaveOutlined,
  ToolOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 导入组件
import ConfigContent from './AgentEditor/components/ConfigContent.vue'
import ConfigNavigation from './AgentEditor/components/ConfigNavigation.vue'
import TestPanel from './AgentEditor/components/TestPanel.vue'

// 导入composables
import { useAgentAPI } from './AgentEditor/composables/useAgentAPI'
import { useAgentForm } from './AgentEditor/composables/useAgentForm'
import { useFormValidation } from './AgentEditor/composables/useFormValidation'

// 路由
const route = useRoute()
const router = useRouter()

// 使用composables
const {
  智能体表单,
  表单状态,
  表单是否有效,
  重置表单,
  批量更新表单,
  设置保存状态
} = useAgentForm()

const {
  对话模型列表,
  嵌入模型列表,
  可用知识库列表,
  可用工具列表,
  加载智能体数据,
  加载智能体关联知识库,
  加载智能体关联工具详情,
  保存智能体: API保存智能体,
  初始化基础数据
} = useAgentAPI()

const {
  验证智能体名称,
  验证智能体描述
} = useFormValidation()

// 本地状态
const 当前配置步骤 = ref('basic')
const 智能体id = computed(() => route.params.id)
const 编辑模式 = computed(() => !!智能体id.value)
const 工具详情数据 = ref({ 工具列表: [] })

// 计算属性
const 已启用工具列表 = computed(() => {
  return 智能体表单.工具列表 || []
})

// 方法
const 返回列表 = () => {
  router.push('/langchain/agents')
}

const 切换配置步骤 = async (step) => {
  当前配置步骤.value = step

  // 如果切换到工具页面且是编辑模式，加载工具详情
  if (step === 'tools' && 编辑模式.value && 智能体id.value) {
    try {
      const 关联工具数据 = await 加载智能体关联工具详情(智能体id.value)
      const 关联工具列表 = 关联工具数据?.工具列表 || []

      // 更新工具详情数据
      工具详情数据.value = 关联工具数据

      // 更新表单中的工具列表，只包含启用的工具
      const 启用的工具名称列表 = 关联工具列表
        .filter(tool => tool.启用状态)
        .map(tool => tool.工具名称)

      // 更新表单数据
      const 更新数据 = {
        ...智能体表单.value,
        工具列表: 启用的工具名称列表,
        启用工具调用: 启用的工具名称列表.length > 0
      }

      批量更新表单(更新数据)

      console.log('🔧 工具页面数据加载完成:', {
        总工具数: 关联工具列表.length,
        启用工具数: 启用的工具名称列表.length,
        启用工具: 启用的工具名称列表
      })
    } catch (error) {
      console.error('加载工具详情失败:', error)
    }
  }
}

const 处理表单变化 = (formData) => {
  批量更新表单(formData)
}

const 处理工具切换 = (toolName) => {
  console.log('工具切换:', toolName)
}



const 保存智能体 = async () => {
  try {
    // 检查表单有效性
    if (!表单是否有效.value) {
      message.error('请完善必填信息：智能体名称和对话模型')
      return
    }

    表单状态.保存中 = true

    const 结果 = await API保存智能体(智能体表单, 编辑模式.value, 智能体id.value)

    设置保存状态({
      type: 'success',
      message: 编辑模式.value ? '智能体更新成功' : '智能体创建成功'
    })

    if (!编辑模式.value && 结果?.id) {
      // 创建成功后跳转到编辑页面
      router.replace(`/langchain/agents/edit/${结果.id}`)
    }
  } catch (error) {
    设置保存状态({
      type: 'error',
      message: error.message || '保存失败'
    })
  } finally {
    表单状态.保存中 = false
  }
}

const 重置配置 = () => {
  重置表单()
}

const 导出配置 = () => {
  const 配置数据 = {
    智能体配置: 智能体表单,
    导出时间: new Date().toISOString()
  }

  const blob = new Blob([JSON.stringify(配置数据, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `agent_config_${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)

  message.success('配置已导出')
}

const 设置测试Token = () => {
  message.info('设置测试Token功能开发中')
}

const 调试API = () => {
  message.info('调试API功能开发中')
}

const 查看使用统计 = () => {
  message.info('使用统计功能开发中')
}

// 生命周期
onMounted(async () => {
  try {
    表单状态.加载中 = true

    // 初始化基础数据
    await 初始化基础数据()

    // 如果是编辑模式，加载智能体数据
    if (编辑模式.value) {
      const 智能体数据 = await 加载智能体数据(智能体id.value)

      // 检查数据有效性
      if (!智能体数据 || typeof 智能体数据 !== 'object') {
        throw new Error('智能体数据无效')
      }

      // 加载智能体关联的知识库详情
      const 关联知识库数据 = await 加载智能体关联知识库(智能体id.value)
      const 关联知识库列表 = 关联知识库数据?.知识库列表 || []

      // 加载智能体关联的工具详情
      const 关联工具数据 = await 加载智能体关联工具详情(智能体id.value)
      const 关联工具列表 = 关联工具数据?.工具列表 || []

      // 直接使用后端数据，确保字段一致
      let 映射后数据
      try {
        映射后数据 = {}

        // 安全复制所有字段，处理null值
        for (const [key, value] of Object.entries(智能体数据)) {
          if (value === null) {
            // 为null字段设置合适的默认值
            if (key.includes('列表') || key === '自定义变量' || key === '标签') {
              映射后数据[key] = []
            } else if (key.includes('提示词') || key.includes('设定') || key.includes('规范')) {
              映射后数据[key] = ''
            } else {
              映射后数据[key] = value
            }
          } else {
            映射后数据[key] = value
          }
        }

        // 后端已经处理了数据类型，直接使用
        // 只处理数组字段的默认值
        if (!Array.isArray(映射后数据.知识库列表)) 映射后数据.知识库列表 = []
        if (!Array.isArray(映射后数据.工具列表)) 映射后数据.工具列表 = []
        if (!Array.isArray(映射后数据.自定义变量)) 映射后数据.自定义变量 = []
        if (!Array.isArray(映射后数据.标签)) 映射后数据.标签 = []

        // 使用关联知识库的ID列表
        映射后数据.知识库列表 = 关联知识库列表.map(kb => kb.知识id || kb.id || kb.langchain_知识库表id)

        console.log('📚 关联知识库数据:', 关联知识库列表)
        console.log('📚 映射后的知识库ID列表:', 映射后数据.知识库列表)

        // 从关联知识库数据中提取RAG配置
        if (关联知识库列表.length > 0) {
          // 使用第一个知识库的配置作为默认配置（如果有多个知识库，可以考虑合并策略）
          const 第一个知识库配置 = 关联知识库列表[0]

          // 映射RAG配置字段
          if (第一个知识库配置.检索策略) {
            映射后数据.检索策略 = 第一个知识库配置.检索策略
          }
          if (第一个知识库配置.相似度阈值 !== undefined) {
            映射后数据.相似度阈值 = 第一个知识库配置.相似度阈值
          }
          if (第一个知识库配置.最大检索数量) {
            映射后数据.最大检索数量 = 第一个知识库配置.最大检索数量
          }
          if (第一个知识库配置.嵌入模型id) {
            映射后数据.嵌入模型id = 第一个知识库配置.嵌入模型id
          }

          // 处理查询优化配置
          if (第一个知识库配置.查询优化配置) {
            映射后数据.查询优化配置 = 第一个知识库配置.查询优化配置
          }

          console.log('✅ 从关联知识库提取RAG配置:', {
            检索策略: 映射后数据.检索策略,
            相似度阈值: 映射后数据.相似度阈值,
            最大检索数量: 映射后数据.最大检索数量,
            嵌入模型id: 映射后数据.嵌入模型id,
            查询优化配置: 映射后数据.查询优化配置
          })
        }

        // 从关联知识库数据中提取RAG配置
        if (关联知识库列表.length > 0) {
          // 使用第一个知识库的配置作为默认配置（如果有多个知识库，可以考虑合并策略）
          const 第一个知识库配置 = 关联知识库列表[0]

          // 映射RAG配置字段
          if (第一个知识库配置.检索策略) {
            映射后数据.检索策略 = 第一个知识库配置.检索策略
          }
          if (第一个知识库配置.相似度阈值 !== undefined) {
            映射后数据.相似度阈值 = 第一个知识库配置.相似度阈值
          }
          if (第一个知识库配置.最大检索数量) {
            映射后数据.最大检索数量 = 第一个知识库配置.最大检索数量
          }
          if (第一个知识库配置.嵌入模型id) {
            映射后数据.嵌入模型id = 第一个知识库配置.嵌入模型id
          }

          // 处理查询优化配置
          if (第一个知识库配置.查询优化配置) {
            映射后数据.查询优化配置 = 第一个知识库配置.查询优化配置
          }

          console.log('✅ 从关联知识库提取RAG配置:', {
            检索策略: 映射后数据.检索策略,
            相似度阈值: 映射后数据.相似度阈值,
            最大检索数量: 映射后数据.最大检索数量,
            嵌入模型id: 映射后数据.嵌入模型id,
            查询优化配置: 映射后数据.查询优化配置
          })
        }

        // 使用关联工具的名称列表
        映射后数据.工具列表 = 关联工具列表.filter(tool => tool.启用状态).map(tool => tool.工具名称)

        // 确保输出格式有默认值
        if (!映射后数据.输出格式) 映射后数据.输出格式 = 'text'

      } catch (mappingError) {
        console.error('❌ 数据映射失败:', mappingError)
        throw new Error('数据映射失败: ' + mappingError.message)
      }

      批量更新表单(映射后数据)
    }
  } catch (error) {
    message.error('页面初始化失败：' + error.message)
  } finally {
    表单状态.加载中 = false
  }
})
</script>

<style scoped>
.agent-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
  overflow: hidden;
  position: relative;
}

.editor-header {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  flex-shrink: 0;
  min-height: 60px;
  z-index: 10;
}

.header-main {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.back-btn {
  color: #8c8c8c;
  flex-shrink: 0;
}

.back-btn:hover {
  color: #1890ff;
}

.agent-basic-info {
  flex: 1;
  min-width: 0;
  max-width: 500px;
}

.agent-name-section {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
}

.agent-name-input {
  font-size: 15px;
  font-weight: 500;
  flex: 1;
  min-width: 0;
}

.id-tag {
  font-size: 11px;
  flex-shrink: 0;
}

.description-input {
  font-size: 12px;
  color: #8c8c8c;
}

.status-indicators {
  flex-shrink: 0;
  margin-left: 8px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-shrink: 0;
}

.tool-btn,
.reset-btn {
  color: #8c8c8c;
}

.tool-btn:hover,
.reset-btn:hover {
  color: #1890ff;
}

.save-btn {
  min-width: 80px;
}

.global-status-bar {
  padding: 6px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  flex-shrink: 0;
  z-index: 9;
}

.global-status-bar.success {
  background: #f6ffed;
  border-bottom: 1px solid #b7eb8f;
  color: #52c41a;
}

.global-status-bar.error {
  background: #fff2f0;
  border-bottom: 1px solid #ffccc7;
  color: #ff4d4f;
}

.global-status-bar.warning {
  background: #fffbe6;
  border-bottom: 1px solid #ffe58f;
  color: #faad14;
}

.main-content {
  flex: 1;
  display: grid;
  grid-template-columns: 240px minmax(400px, 1fr) minmax(320px, 430px);
  gap: 0;
  overflow: hidden;
  min-height: 0;
  background: #f0f2f5;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .main-content {
    grid-template-columns: 220px minmax(350px, 1fr) minmax(300px, 400px);
  }
}

@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 200px minmax(300px, 1fr) minmax(280px, 370px);
  }

  .editor-header {
    padding: 6px 12px;
  }

  .header-main {
    gap: 8px;
  }

  .agent-basic-info {
    max-width: 350px;
  }
}

@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 180px minmax(250px, 1fr) minmax(260px, 320px);
  }
}

@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
  }

  .editor-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
    padding: 8px 12px;
  }

  .header-main {
    flex-direction: column;
    gap: 6px;
  }

  .agent-name-section {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }

  .status-indicators {
    margin-left: 0;
    align-self: flex-start;
  }

  .header-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .editor-header {
    padding: 6px 8px;
  }

  .agent-basic-info {
    max-width: none;
  }

  .header-actions {
    flex-wrap: wrap;
    gap: 4px;
  }
}
</style>

