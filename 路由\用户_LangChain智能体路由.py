"""
用户端 - LangChain智能体访问路由

功能：
1. 获取用户可用的智能体列表
2. 智能体对话接口
3. 对话历史查询
4. 智能体使用统计
"""

from datetime import datetime
from typing import Optional, Dict, Any

from fastapi import APIRouter, Body, Depends
from pydantic import BaseModel, Field

from 依赖项.认证 import 获取当前用户
from 数据模型.响应模型 import 统一响应模型
from 日志 import 应用日志器 as 路由日志器
from 日志 import 错误日志器
from 服务.LangChain_智能体服务 import LangChain智能体服务实例
from 数据.LangChain_智能体数据层 import LangChain智能体数据层实例
from 状态 import 状态
from 服务.LangChain_知识库服务 import LangChain知识库服务实例

# 导入工具函数
from 服务.LangChain_智能体工具函数 import (
    获取用户可用智能体列表 as 工具_获取用户可用智能体列表,
    验证用户智能体访问权限 as 工具_验证用户智能体访问权限
)

# 创建路由器
用户LangChain智能体路由 = APIRouter(tags=["用户-LangChain智能体"])


# ==================== 请求模型定义 ====================


class 用户智能体对话请求模型(BaseModel):
    """用户智能体对话请求模型（请求体中直接携带模板变量同名字段）"""

    智能体id: int = Field(..., description="智能体id")
    消息内容: str = Field(..., description="用户消息内容", min_length=1)
    会话id: Optional[str] = Field(None, description="会话id")
    上下文信息: Optional[str] = Field(None, description="额外上下文信息")

    class Config:
        extra = "allow"  # 允许直接在请求体中携带模板变量同名字段


class 用户智能体列表请求模型(BaseModel):
    """用户智能体列表请求模型"""

    页码: int = Field(1, description="页码", ge=1)
    每页数量: int = Field(20, description="每页数量", ge=1, le=100)

    搜索关键词: Optional[str] = Field(None, description="搜索关键词")


class 对话历史查询请求模型(BaseModel):
    """对话历史查询请求模型"""

    智能体id: Optional[int] = Field(None, description="智能体id过滤")
    会话id: Optional[str] = Field(None, description="会话id过滤")
    页码: int = Field(1, description="页码", ge=1)
    每页数量: int = Field(50, description="每页数量", ge=1, le=100)
    开始时间: Optional[str] = Field(None, description="开始时间")
    结束时间: Optional[str] = Field(None, description="结束时间")


class 清空智能体记忆请求模型(BaseModel):
    """清空智能体记忆请求模型"""

    智能体id: int = Field(..., description="智能体id")
    会话id: Optional[str] = Field(None, description="会话id，不指定则清空所有记忆")


# ==================== 模板化创建/编辑 请求模型 ====================


class 用户模板创建智能体请求模型(BaseModel):
    """基于模板创建用户自有智能体的请求模型"""

    模板智能体id: int = Field(..., description="作为模板的智能体id")
    智能体名称: str = Field(..., description="新智能体名称", min_length=1, max_length=100)
    智能体描述: Optional[str] = Field("", description="新智能体描述", max_length=200)
    知识库列表: Optional[list[int]] = Field(None, description="要关联的知识库id列表")
    变量值: Optional[dict] = Field(None, description="模板自定义变量的填充值，键为变量名")
    langchain_模型配置表id: Optional[int] = Field(None, description="可选：覆盖模板的模型配置id")
    温度参数: Optional[float] = Field(None, description="可选：覆盖模板的温度参数")
    最大令牌数: Optional[int] = Field(None, description="可选：覆盖模板的最大令牌数")


class 用户智能体详情请求模型(BaseModel):
    """获取用户可见的智能体详情"""

    智能体id: int = Field(..., description="智能体id")


class 用户更新智能体请求模型(BaseModel):
    """用户更新自有智能体的请求模型（不暴露管理员复杂字段）"""

    智能体id: int = Field(..., description="待更新的智能体id")
    智能体名称: Optional[str] = Field(None, description="智能体名称")
    智能体描述: Optional[str] = Field(None, description="智能体描述")
    变量值: Optional[dict] = Field(None, description="更新模板变量默认值，键为变量名")


class 用户智能体知识库变更请求模型(BaseModel):
    """用户对智能体的知识库进行单条关联/取消的请求模型"""
    智能体id: int = Field(..., description="智能体id")
    知识库id: int = Field(..., description="知识库id")
class 用户知识库列表请求模型(BaseModel):
    页码: int = Field(1, ge=1)
    每页数量: int = Field(20, ge=1, le=100)
    搜索关键词: Optional[str] = None



# ==================== API接口实现 ====================


@用户LangChain智能体路由.post("/agents/available", summary="获取用户可用智能体列表")
async def 获取用户可用智能体列表(
    请求数据: 用户智能体列表请求模型, 用户: dict = Depends(获取当前用户)
):
    """
    获取用户可用的智能体列表
    包括：分配给用户的智能体 + 公开智能体
    """
    try:
        # 服务层已在启动时初始化，无需重复初始化

        # 构建查询参数
        查询参数 = {
            "页码": 请求数据.页码,
            "每页数量": 请求数据.每页数量,
            "搜索关键词": 请求数据.搜索关键词,
        }

        # 直接调用工具函数获取数据
        结果 = await 工具_获取用户可用智能体列表(
            用户["id"], 查询参数
        )

        路由日志器.info(
            f"用户 {用户['id']} 获取智能体列表成功，返回 {结果['总数量']} 条记录"
        )

        return 统一响应模型.成功(结果, "获取智能体列表成功")

    except Exception as e:
        错误日志器.error(
            f"获取用户智能体列表API异常 (用户: {用户['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1601, f"获取智能体列表失败: {str(e)}")


@用户LangChain智能体路由.post("/agents/templates", summary="获取可用模板智能体列表")
async def 获取可用模板智能体列表(
    请求数据: 用户智能体列表请求模型, 用户: dict = Depends(获取当前用户)
):
    """仅返回设为模板的并且用户可访问的模板智能体列表"""
    try:
        查询参数 = {
            "页码": 请求数据.页码,
            "每页数量": 请求数据.每页数量,
            "搜索关键词": 请求数据.搜索关键词,
        }
        列表, 总数量 = await LangChain智能体数据层实例.获取用户可用模板智能体列表(用户["id"], 查询参数)
        结果 = {
            "智能体列表": 列表,
            "总数量": 总数量,
            "当前页码": 请求数据.页码,
            "每页数量": 请求数据.每页数量,
        }
        return 统一响应模型.成功(结果, "获取模板列表成功")
    except Exception as e:
        错误日志器.error(f"获取模板列表失败: {str(e)}", exc_info=True)
        return 统一响应模型.失败(1601, f"获取模板列表失败: {str(e)}")


@用户LangChain智能体路由.post("/agents/chat", summary="用户与智能体对话")
async def 用户与智能体对话(
    请求数据: 用户智能体对话请求模型 = Body(...), 用户: dict = Depends(获取当前用户)
):
    """
    用户与智能体对话的正式API接口

    功能特点:
    - 消耗用户算力
    - 记录对话历史
    - 支持结构化输出
    - 完整的用户体验优化

    请求参数:
    - **消息内容**: 用户要发送的消息
    - **会话id**: 可选的会话id，用于保持对话上下文
    - **上下文信息**: 可选的额外上下文信息
    """
    路由日志器.info(
        f"💬 用户对话请求 - 用户id: {用户['id']}, 智能体id: {请求数据.智能体id}"
    )

    try:
        # 服务层已在启动时初始化，无需重复初始化
        路由日志器.info("🔧 LangChain智能体服务已就绪")

        # 验证用户是否有权限访问该智能体
        路由日志器.info(
            f"🔍 验证用户访问权限: 用户{用户['id']} -> 智能体{请求数据.智能体id}"
        )
        权限验证结果 = await 工具_验证用户智能体访问权限(
            用户["id"], 请求数据.智能体id
        )

        if not 权限验证结果.get("success"):
            错误信息 = 权限验证结果.get("error", "无权限访问该智能体")
            路由日志器.warning(f"⚠️ 用户权限验证失败: {错误信息}")
            return 统一响应模型.失败(1403, 错误信息)

        # 生成会话id（如果未提供）
        会话id = (
            请求数据.会话id
            or f"user_{用户['id']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        路由日志器.info(f"🆔 使用会话id: {会话id}")

        # 解析自定义变量：优先用显式传入的自定义变量，否则从请求体的额外字段中自动提取
        try:
            # 读取请求体所有字段（Pydantic v2）
            原始请求字典 = 请求数据.model_dump()
        except Exception:
            原始请求字典 = {}

        变量值: Dict[str, Any] = {}

        # 从数据库读取该智能体定义的自定义变量清单
        智能体详情 = await LangChain智能体数据层实例.获取智能体详情完整(请求数据.智能体id)
        自定义变量清单 = (智能体详情 or {}).get("自定义变量") or []
        保留字段 = {"智能体id", "消息内容", "会话id", "上下文信息"}
        for 变量 in 自定义变量清单:
            名称 = 变量.get("变量名")
            if not 名称:
                continue
            if 名称 in 保留字段:
                continue
            if 名称 in 原始请求字典 and 原始请求字典[名称] is not None:
                值 = 原始请求字典[名称]
                # 按变量类型做轻量转换
                类型 = (变量.get("变量类型") or "").lower()
                try:
                    if 类型 == "number":
                        if isinstance(值, str):
                            if 值.isdigit():
                                值 = int(值)
                            else:
                                值 = float(值)
                    elif 类型 == "boolean":
                        if isinstance(值, str):
                            值 = 值.strip().lower() in ("1", "true", "yes", "on")
                        elif isinstance(值, (int, float)):
                            值 = bool(值)
                    elif 类型 in ("array", "object"):
                        if isinstance(值, str):
                            import json as _json
                            try:
                                值 = _json.loads(值)
                            except Exception:
                                pass
                    # string/text 默认原值
                except Exception:
                    pass
                变量值[名称] = 值

        # 调用服务层进行对话
        路由日志器.info("🚀 开始调用智能体对话服务...")
        对话结果 = await LangChain智能体服务实例.智能体对话(
            智能体id=请求数据.智能体id,
            用户表id=用户["id"],
            用户消息=请求数据.消息内容,
            会话id=会话id,
            测试模式=False,  # 用户正式对话，非测试模式
            自定义变量=变量值 if 变量值 else None,
        )

        路由日志器.info(f"📨 对话服务返回结果: status={对话结果.get('status')}")

        # 检查对话结果状态
        if 对话结果.get("status") == 100:  # 成功状态码
            路由日志器.info("✅ 用户智能体对话成功")

            # 提取data字段中的实际数据
            对话数据 = 对话结果.get("data", {})

            # 处理智能体回复
            智能体回复 = 对话数据.get("智能体回复")
            结构化输出 = 对话数据.get("结构化输出", False)
            输出格式 = 对话数据.get("输出格式", "text")

            # 如果是结构化输出且回复是JSON字符串，解析为对象
            if (结构化输出 or 输出格式 in ["json", "structured"]) and isinstance(
                智能体回复, str
            ):
                try:
                    if 智能体回复.strip().startswith(
                        "{"
                    ) and 智能体回复.strip().endswith("}"):
                        import json

                        智能体回复 = json.loads(智能体回复)
                        路由日志器.info("✅ 结构化输出JSON解析成功，返回对象格式")
                    else:
                        路由日志器.info("📝 结构化输出但非JSON格式，保持原始文本")
                except json.JSONDecodeError as e:
                    路由日志器.warning(f"⚠️ 结构化输出JSON解析失败: {e}，保持原始文本")
                except Exception as e:
                    路由日志器.warning(f"⚠️ 结构化输出处理异常: {e}，保持原始文本")

            # 构建用户友好的响应数据
            响应数据 = {
                "对话信息": {
                    "智能体回复": 智能体回复,
                    "会话id": 对话数据.get("会话id", 会话id),
                    "对话时间": 对话数据.get("对话时间"),
                    "处理时长": 对话数据.get("处理时长", 0),
                },
                "智能体信息": {
                    "智能体id": 请求数据.智能体id,
                    "智能体名称": 对话数据.get("智能体名称", "智能助手"),
                    "输出格式": 输出格式,
                    "结构化输出": 结构化输出,
                },
                "使用统计": {
                    "令牌消耗": 对话数据.get("令牌消耗", 0),
                    "知识库使用": bool(对话数据.get("RAG检索信息")),
                },
            }

            # 如果有RAG检索信息，添加到响应中
            if 对话数据.get("RAG检索信息"):
                响应数据["知识库信息"] = 对话数据.get("RAG检索信息")

            路由日志器.info("📋 构建用户对话响应数据完成")
            return 统一响应模型.成功(响应数据, "智能体对话成功")

        else:
            # 对话失败
            错误信息 = 对话结果.get("message", "对话失败")
            路由日志器.error(f"❌ 用户智能体对话失败: {错误信息}")
            return 统一响应模型.失败(1602, f"智能体对话失败: {错误信息}")

    except Exception as e:
        路由日志器.error(f"❌ 用户智能体对话异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(1603, f"智能体对话失败: {str(e)}")


@用户LangChain智能体路由.post("/conversations/history", summary="获取对话历史")
async def 获取对话历史(
    请求数据: 对话历史查询请求模型, 用户: dict = Depends(获取当前用户)
):
    """
    获取用户的对话历史记录
    """
    try:
        # 服务层已在启动时初始化，无需重复初始化

        # 构建查询参数
        查询参数 = {
            "页码": 请求数据.页码,
            "每页数量": 请求数据.每页数量,
            "智能体id": 请求数据.智能体id,
            "会话id": 请求数据.会话id,
            "开始时间": 请求数据.开始时间,
            "结束时间": 请求数据.结束时间,
        }

        # 调用服务层获取数据
        结果 = await LangChain智能体服务实例.获取用户对话历史(用户["id"], 查询参数)

        路由日志器.info(
            f"用户 {用户['id']} 获取对话历史成功，返回 {结果['总数量']} 条记录"
        )

        return 统一响应模型.成功(结果, "获取对话历史成功")

    except Exception as e:
        错误日志器.error(
            f"获取对话历史API异常 (用户: {用户['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1604, f"获取对话历史失败: {str(e)}")


@用户LangChain智能体路由.post("/agents/clear-memory", summary="清空智能体记忆")
async def 清空智能体记忆(
    请求数据: 清空智能体记忆请求模型 = Body(...), 用户: dict = Depends(获取当前用户)
):
    """
    清空智能体的记忆（用户操作）
    """
    try:
        # 服务层已在启动时初始化，无需重复初始化

        # 调用服务层清空记忆
        结果 = await LangChain智能体服务实例.清空智能体记忆(
            用户["id"], 请求数据.智能体id
        )

        if 结果.get("success"):
            路由日志器.info(
                f"用户 {用户['id']} 清空智能体 {请求数据.智能体id} 记忆成功"
            )
            return 统一响应模型.成功(None, 结果.get("message", "智能体记忆清空成功"))
        else:
            return 统一响应模型.失败(1403, 结果.get("error", "清空智能体记忆失败"))

    except Exception as e:
        错误日志器.error(
            f"清空智能体记忆API异常 (用户: {用户['id']}, 智能体: {请求数据.智能体id}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1605, f"清空智能体记忆失败: {str(e)}")


@用户LangChain智能体路由.post("/my-statistics", summary="获取我的智能体使用统计")
async def 获取我的智能体使用统计(用户: dict = Depends(获取当前用户)):
    """
    获取用户的智能体使用统计
    """
    try:
        # 服务层已在启动时初始化，无需重复初始化

        # 调用服务层获取统计数据
        结果 = await LangChain智能体服务实例.获取用户智能体使用统计(用户["id"])

        if 结果.get("success"):
            路由日志器.info(f"用户 {用户['id']} 获取智能体使用统计成功")
            return 统一响应模型.成功(结果.get("统计数据"), "获取智能体使用统计成功")
        else:
            return 统一响应模型.失败(1606, 结果.get("error", "获取智能体使用统计失败"))

    except Exception as e:
        错误日志器.error(
            f"获取用户统计API异常 (用户: {用户['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1606, f"获取使用统计失败: {str(e)}")


@用户LangChain智能体路由.post("/agents/create-from-template", summary="用户基于模板创建智能体")
async def 用户基于模板创建智能体(
    请求数据: 用户模板创建智能体请求模型 = Body(...), 用户: dict = Depends(获取当前用户)
):
    """
    用户通过选择一个模板智能体，创建属于自己的智能体。

    规则：
    - 复制模板的系统提示词、用户提示词、模型配置与输出模式
    - 用户可覆盖模型配置的少量参数（模型配置id、温度、最大令牌数）
    - 自定义变量：沿用模板结构，将"变量值"作为默认值写入
    - 知识库关联：按传入的知识库列表进行关联（不复制模板的关联）
    - 新建的智能体固定为私有（是否公开=0），归属当前用户
    """
    try:
        # 获取模板详情
        模板详情 = await LangChain智能体数据层实例.获取智能体详情完整(请求数据.模板智能体id)
        if not 模板详情:
            return 统一响应模型.失败(状态.LangChain.智能体不存在, "模板智能体不存在")

        # 必须是模板，且用户可访问（公开或分配或本人创建）
        if not 模板详情.get("设为模板"):
            return 统一响应模型.失败(状态.LangChain.智能体不存在, "该智能体未设为模板")
        if not (模板详情.get("是否公开") or 模板详情.get("用户表id") == 用户["id"]):
            权限 = await 工具_验证用户智能体访问权限(用户["id"], 请求数据.模板智能体id)
            if not 权限.get("success"):
                return 统一响应模型.失败(1403, "无权限使用该模板")

        # 组装自定义变量：使用模板结构，填充默认值
        模板自定义变量 = 模板详情.get("自定义变量") or []
        变量值映射 = 请求数据.变量值 or {}
        新自定义变量 = []
        for 变量 in 模板自定义变量:
            try:
                变量名 = 变量.get("变量名")
                if not 变量名:
                    continue
                新变量 = dict(变量)
                if 变量名 in 变量值映射:
                    新变量["默认值"] = 变量值映射[变量名]
                新自定义变量.append(新变量)
            except Exception:
                # 保底加入
                新自定义变量.append(变量)

        # 组装创建数据
        智能体创建数据 = {
            "用户表id": 用户["id"],
            "智能体名称": 请求数据.智能体名称,
            "智能体描述": 请求数据.智能体描述 or "",
            "模型名称": 模板详情.get("模型名称"),
            "langchain_模型配置表id": 请求数据.langchain_模型配置表id or 模板详情.get("langchain_模型配置表id"),
            "温度参数": 请求数据.温度参数 if 请求数据.温度参数 is not None else 模板详情.get("温度参数", 0.7),
            "最大令牌数": 请求数据.最大令牌数 if 请求数据.最大令牌数 is not None else 模板详情.get("最大令牌数", 4000),
            "记忆窗口大小": 模板详情.get("记忆窗口大小", 10),
            "启用rag": False,  # 创建时默认不启用，后续可在编辑中关联知识库
            "输出格式": 模板详情.get("输出格式", "text"),
            "自定义回复格式": 模板详情.get("自定义回复格式"),
            "自定义变量": 新自定义变量,
            "标签": 模板详情.get("标签") or [],
            "是否公开": False,
            "是否启用": True,
            "系统提示词": 模板详情.get("系统提示词", ""),
            "用户提示词": 模板详情.get("用户提示词", ""),
        }

        创建结果 = await LangChain智能体服务实例.创建智能体(智能体创建数据)
        if 创建结果.get("status") == 状态.通用.成功 and 创建结果.get("data"):
            新智能体id = 创建结果["data"].get("智能体id")

            # 模板创建不再批量设置知识库关联，前端创建后通过即时切换接口逐个关联

            返回数据 = {
                "智能体id": 新智能体id,
                "智能体名称": 请求数据.智能体名称,
                "是否公开": False,
            }
            return 统一响应模型.成功(返回数据, "智能体创建成功")

        错误信息 = 创建结果.get("message", "创建失败")
        return 统一响应模型.失败(状态.LangChain.智能体创建失败, 错误信息)

    except Exception as e:
        错误日志器.error(f"用户模板创建智能体失败: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态.LangChain.智能体创建失败, f"创建智能体失败: {str(e)}")


@用户LangChain智能体路由.post("/agents/detail", summary="获取用户可见的智能体详情")
async def 获取用户智能体详情(
    请求数据: 用户智能体详情请求模型, 用户: dict = Depends(获取当前用户)
):
    """返回用户可访问的智能体详情（带自定义变量与知识库关联）"""
    try:
        # 先获取详情以判断是否公开或归属
        详情 = await LangChain智能体数据层实例.获取智能体详情完整(请求数据.智能体id)
        if not 详情:
            return 统一响应模型.失败(状态.LangChain.智能体不存在, "智能体不存在")

        # 公开或本人创建则允许直接查看，否则校验权限
        if not (详情.get("是否公开") or 详情.get("用户表id") == 用户["id"]):
            权限 = await 工具_验证用户智能体访问权限(用户["id"], 请求数据.智能体id)
            if not 权限.get("success"):
                return 统一响应模型.失败(1403, 权限.get("error", "无权限访问该智能体"))

        # 附带知识库关联
        知识库关联 = await LangChain智能体数据层实例.获取智能体关联知识库配置(请求数据.智能体id)
        返回 = {
            **详情,
            "知识库关联": 知识库关联,
        }
        return 统一响应模型.成功(返回, "获取智能体详情成功")

    except Exception as e:
        错误日志器.error(f"获取用户智能体详情失败: {str(e)}", exc_info=True)
        return 统一响应模型.失败(1504, f"获取智能体详情失败: {str(e)}")


@用户LangChain智能体路由.post("/agents/update", summary="用户更新自有智能体")
async def 用户更新自有智能体(
    请求数据: 用户更新智能体请求模型 = Body(...), 用户: dict = Depends(获取当前用户)
):
    """
    用户更新自己名下的智能体：支持名称、描述、变量默认值、知识库关联。
    不暴露管理员的高级配置项。
    """
    try:
        # 校验拥有权或访问权限（仅允许拥有者更新）
        详情 = await LangChain智能体数据层实例.获取智能体详情完整(请求数据.智能体id)
        if not 详情:
            return 统一响应模型.失败(状态.LangChain.智能体不存在, "智能体不存在")
        if 详情.get("用户表id") != 用户["id"]:
            return 统一响应模型.失败(1403, "仅允许更新本人创建的智能体")

        更新数据: Dict[str, Any] = {}
        if 请求数据.智能体名称 is not None:
            更新数据["智能体名称"] = 请求数据.智能体名称
        if 请求数据.智能体描述 is not None:
            更新数据["智能体描述"] = 请求数据.智能体描述

        # 更新自定义变量默认值
        if 请求数据.变量值:
            当前变量 = 详情.get("自定义变量") or []
            变量映射 = 请求数据.变量值
            新变量列表 = []
            for 变量 in 当前变量:
                try:
                    名 = 变量.get("变量名")
                    新项 = dict(变量)
                    if 名 in 变量映射:
                        新项["默认值"] = 变量映射[名]
                    新变量列表.append(新项)
                except Exception:
                    新变量列表.append(变量)
            更新数据["自定义变量"] = 新变量列表

        # 不再支持批量覆盖知识库列表，所有关联改走 /agents/knowledge/toggle 即时生效

        if 更新数据:
            更新结果 = await LangChain智能体数据层实例.更新智能体配置(请求数据.智能体id, 更新数据)
            if not 更新结果:
                return 统一响应模型.失败(状态.LangChain.智能体更新失败, "更新智能体配置失败")

        return 统一响应模型.成功({"智能体id": 请求数据.智能体id}, "智能体更新成功")

    except Exception as e:
        错误日志器.error(f"用户更新智能体失败: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态.LangChain.智能体更新失败, f"更新智能体失败: {str(e)}")


@用户LangChain智能体路由.post("/agents/knowledge-bases", summary="获取智能体关联知识库列表")
async def 获取智能体关联知识库列表(
    请求数据: 用户智能体详情请求模型, 用户: dict = Depends(获取当前用户)
):
    """获取智能体关联的知识库ID列表（专用接口）"""
    try:
        # 先获取详情以判断是否公开或归属
        详情 = await LangChain智能体数据层实例.获取智能体详情完整(请求数据.智能体id)
        if not 详情:
            return 统一响应模型.失败(状态.LangChain.智能体不存在, "智能体不存在")

        # 公开或本人创建则允许直接查看，否则校验权限
        if not (详情.get("是否公开") or 详情.get("用户表id") == 用户["id"]):
            权限 = await 工具_验证用户智能体访问权限(用户["id"], 请求数据.智能体id)
            if not 权限.get("success"):
                return 统一响应模型.失败(1403, 权限.get("error", "无权限访问该智能体"))

        # 获取关联的知识库ID列表
        知识库id列表 = await LangChain智能体数据层实例.获取智能体关联知识库列表(请求数据.智能体id)

        路由日志器.info(
            f"用户 {用户['id']} 获取智能体 {请求数据.智能体id} 关联知识库成功，数量: {len(知识库id列表)}"
        )

        return 统一响应模型.成功({
            "智能体id": 请求数据.智能体id,
            "知识库列表": [int(kid) for kid in 知识库id列表]  # 转换为整数类型
        }, "获取智能体关联知识库列表成功")

    except Exception as e:
        错误日志器.error(f"获取智能体关联知识库列表失败: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态.通用.失败, f"获取智能体关联知识库列表失败: {str(e)}")


@用户LangChain智能体路由.post("/agents/knowledge/toggle", summary="切换智能体单个知识库关联（即选即更）")
async def 切换智能体知识库关联(
    请求数据: 用户智能体知识库变更请求模型 = Body(...), 用户: dict = Depends(获取当前用户)
):
    """如果已关联则取消，否则建立关联。成功后根据是否还有关联自动开关RAG。"""
    try:
        详情 = await LangChain智能体数据层实例.获取智能体详情完整(请求数据.智能体id)
        if not 详情:
            return 统一响应模型.失败(状态.LangChain.智能体不存在, "智能体不存在")
        if 详情.get("用户表id") != 用户["id"]:
            return 统一响应模型.失败(1403, "仅允许操作本人创建的智能体")

        # 读取当前关联集合
        当前关联 = await LangChain智能体数据层实例.获取智能体关联知识库配置(请求数据.智能体id)
        已有关联ids = {int(x.get("知识id")) for x in (当前关联 or []) if x.get("知识id") is not None}

        目标id = int(请求数据.知识库id)
        if 目标id in 已有关联ids:
            新集合 = [kid for kid in 已有关联ids if kid != 目标id]
            操作 = "取消关联"
        else:
            新集合 = list(已有关联ids) + [目标id]
            操作 = "建立关联"

        await LangChain智能体数据层实例.更新智能体知识库关联(请求数据.智能体id, 新集合)
        # 自动切换RAG
        await LangChain智能体数据层实例.更新智能体配置(请求数据.智能体id, {"启用rag": bool(新集合)})

        return 统一响应模型.成功({"智能体id": 请求数据.智能体id, "知识库列表": 新集合}, f"{操作}成功")
    except Exception as e:
        错误日志器.error(f"切换智能体知识库关联失败: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态.通用.失败, f"操作失败: {str(e)}")


@用户LangChain智能体路由.post("/knowledge/list", summary="获取用户可访问的知识库列表")
async def 获取用户可访问的知识库列表(
    请求数据: 用户知识库列表请求模型, 用户: dict = Depends(获取当前用户)
):
    try:
        查询参数 = {
            "页码": 请求数据.页码,
            "每页数量": 请求数据.每页数量,
            "搜索关键词": 请求数据.搜索关键词,
        }
        结果 = await LangChain知识库服务实例.获取用户可访问知识库列表(用户["id"], 查询参数)
        if 结果.get("success"):
            return 统一响应模型.成功(结果, "获取知识库列表成功")
        return 统一响应模型.失败(状态.通用.失败, 结果.get("error", "获取知识库列表失败"))
    except Exception as e:
        错误日志器.error(f"获取用户可访问知识库列表失败: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态.通用.失败, f"获取知识库列表失败: {str(e)}")
