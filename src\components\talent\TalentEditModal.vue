<template>
  <a-modal
    v-model:open="visible"
    title="编辑达人信息"
    :width="800"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      class="talent-edit-form"
    >
      <!-- 达人基本信息展示 -->
      <div class="talent-preview">
        <a-avatar 
          :size="64" 
          :src="formData.avatar" 
          :alt="formData.nickname"
          class="talent-avatar"
        >
          <template #icon>
            <user-outlined />
          </template>
        </a-avatar>
        <div class="talent-basic-info">
          <h3>{{ formData.nickname || '未知昵称' }}</h3>
          <p>{{ formData.account_douyin || '抖音号未知' }}</p>
          <a-tag color="blue">
            粉丝 {{ formatNumber(formData.粉丝数) }}
          </a-tag>
        </div>
      </div>

      <a-divider />

      <!-- 可编辑信息区域 -->
      <a-row :gutter="16">
        <!-- 联系方式类型选择 -->
        <a-col :span="8">
          <a-form-item name="联系方式类型">
            <template #label>
              <span style="color: red;">*</span> 达人商务联系方式类型
            </template>
            <a-select
              v-model:value="formData.联系方式类型"
              placeholder="请选择联系方式类型"
              allow-clear
            >
              <a-select-option value="微信">微信</a-select-option>
              <a-select-option value="手机">手机</a-select-option>
              <a-select-option value="邮箱">邮箱</a-select-option>
              <a-select-option value="QQ">QQ</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 联系方式内容 -->
        <a-col :span="16">
          <a-form-item name="微信号">
            <template #label>
              <span style="color: red;">*</span> 达人商务联系方式
            </template>
            <a-input-group compact>
              <a-input
                v-model:value="formData.微信号"
                :placeholder="getContactPlaceholder()"
                allow-clear
                style="width: calc(100% - 100px)"
              />
              <a-button
                type="primary"
                @click="bindWechatNumber"
                :loading="bindingWechat"
                style="width: 100px"
                :disabled="!formData.联系方式类型"
              >
                {{ getBindButtonText() }}
              </a-button>
            </a-input-group>
            <div class="form-help-text">
              {{ getHelpText() }}
            </div>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 个人备注 -->
      <a-form-item label="个人备注" name="个人备注">
        <a-textarea 
          v-model:value="formData.个人备注" 
          placeholder="记录与此达人的沟通情况、合作意向、注意事项等..."
          :rows="4"
          :maxlength="500"
          show-count
          allow-clear
        />
      </a-form-item>

      <!-- 个人标签 -->
      <a-form-item label="个人标签" name="个人标签">
        <div class="tags-container">
          <!-- 显示现有标签 -->
          <div class="existing-tags" v-if="formData.个人标签 && formData.个人标签.length > 0">
            <a-tag 
              v-for="tag in formData.个人标签" 
              :key="tag"
              closable
              @close="removeTag(tag)"
              color="blue"
            >
              {{ tag }}
            </a-tag>
          </div>
          
          <!-- 添加新标签 -->
          <a-input
            v-if="tagInputVisible"
            ref="tagInputRef"
            v-model:value="newTagValue"
            size="small"
            style="width: 120px"
            @blur="handleTagConfirm"
            @keyup.enter="handleTagConfirm"
            placeholder="输入标签名"
          />
          <a-tag
            v-else
            @click="showTagInput"
            style="background: #ffffff; border-style: dashed; cursor: pointer;"
          >
            <plus-outlined />
            添加标签
          </a-tag>
        </div>
      </a-form-item>

      <!-- 预设标签快速选择 -->
      <a-form-item label="快速标签">
        <div class="preset-tags">
          <a-tag 
            v-for="tag in presetTags" 
            :key="tag"
            :color="formData.个人标签?.includes(tag) ? 'blue' : 'default'"
            @click="togglePresetTag(tag)"
            style="cursor: pointer; margin-bottom: 8px;"
          >
            {{ tag }}
          </a-tag>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import {
    PlusOutlined,
    UserOutlined
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { nextTick, reactive, ref, watch } from 'vue'
// 正确导入talentService实例
import talentService from '@/services/talentService'

/**
 * 组件属性定义
 */
const props = defineProps({
  // 模态框显示状态
  open: {
    type: Boolean,
    default: false
  },
  // 要编辑的达人数据
  talent: {
    type: Object,
    default: () => ({})
  }
})

/**
 * 组件事件定义
 */
const emit = defineEmits([
  'update:open',    // 更新显示状态
  'success',        // 编辑成功
  'cancel'          // 取消编辑
])

/**
 * 响应式数据
 */
// 模态框显示状态
const visible = ref(false)
// 表单加载状态
const loading = ref(false)
// 表单引用
const formRef = ref()
// 标签输入相关
const tagInputVisible = ref(false)
const tagInputRef = ref()
const newTagValue = ref('')

// 微信号绑定状态
const bindingWechat = ref(false)

// 表单数据
const formData = reactive({
  id: null,
  nickname: '',
  account_douyin: '',
  avatar: '',
  粉丝数: 0,
  微信号: '',
  联系方式类型: '', // 初始为空，让用户主动选择
  个人备注: '',
  个人标签: []
})

// 表单验证规则
const rules = {
  联系方式类型: [
    {
      validator: (_rule, value) => {
        if (!value) {
          return Promise.reject('请选择联系方式类型')
        }
        return Promise.resolve()
      }
    }
  ],
  微信号: [
    {
      validator: (_rule, value) => {
        if (!value || !value.trim()) {
          return Promise.reject('请输入联系方式')
        }
        return Promise.resolve()
      }
    }
  ]
}

// 预设标签列表
const presetTags = [
  '高质量',
  '好沟通',
  '回复快',
  '专业',
  '价格合理',
  '配合度高',
  '粉丝活跃',
  '带货能力强',
  '内容优质',
  '长期合作',
  '新人友好',
  '档期充足'
]

/**
 * 监听器
 */
// 监听外部 open 状态变化
watch(
  () => props.open,
  (newValue) => {
    visible.value = newValue
    if (newValue) {
      initFormData()
    }
  },
  { immediate: true }
)

// 监听内部 visible 状态变化
watch(visible, (newValue) => {
  emit('update:open', newValue)
})

/**
 * 工具方法
 */
// 格式化数字显示
const formatNumber = (num) => {
  if (!num) return '0'
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toString()
}

// 根据联系方式类型获取输入框占位符
const getContactPlaceholder = () => {
  const type = formData.联系方式类型
  switch (type) {
    case '微信':
      return '请输入微信号'
    case '手机':
      return '请输入手机号'
    case '邮箱':
      return '请输入邮箱地址'
    case 'QQ':
      return '请输入QQ号'
    default:
      return '请输入联系方式'
  }
}

// 根据联系方式类型获取绑定按钮文本
const getBindButtonText = () => {
  const type = formData.联系方式类型
  switch (type) {
    case '微信':
      return '绑定微信'
    case '手机':
      return '保存手机'
    case '邮箱':
      return '保存邮箱'
    case 'QQ':
      return '保存QQ'
    default:
      return '保存'
  }
}

// 根据联系方式类型获取帮助文本
const getHelpText = () => {
  const type = formData.联系方式类型
  switch (type) {
    case '微信':
      return '输入微信号后点击"绑定微信"将自动建立完整的关联关系'
    case '手机':
      return '输入手机号后点击"保存手机"将保存联系方式信息'
    case '邮箱':
      return '输入邮箱地址后点击"保存邮箱"将保存联系方式信息'
    case 'QQ':
      return '输入QQ号后点击"保存QQ"将保存联系方式信息'
    default:
      return '请先选择联系方式类型，然后输入相应的联系方式'
  }
}

/**
 * 初始化表单数据并回显已有的个人信息
 * 支持从达人对象中回显微信号、手机号、邮箱等个人信息
 */
const initFormData = () => {
  console.log('🚀 开始初始化表单数据')
  
  if (!props.talent) {
    console.warn('⚠️ 未接收到达人数据，使用默认值')
    return
  }
  
  console.log('📊 接收到的完整达人数据:', JSON.stringify(props.talent, null, 2))
  
  // 设置基础信息（达人基本资料，不可编辑）
  formData.id = props.talent.id
  formData.nickname = props.talent.nickname || ''
  formData.account_douyin = props.talent.account_douyin || ''
  formData.avatar = props.talent.avatar || ''
  formData.粉丝数 = props.talent.粉丝数 || 0
  
  console.log('✅ 基础信息设置完成:', {
    id: formData.id,
    nickname: formData.nickname,
    account_douyin: formData.account_douyin,
    粉丝数: formData.粉丝数
  })
  
  // 回显个人信息（用户可编辑的私人资料）
  // 检查每个字段是否存在，存在则进行回显
  formData.微信号 = props.talent.微信号 || props.talent.联系方式 || ''
  formData.联系方式类型 = props.talent.联系方式类型 || '' // 不设置默认值，让用户主动选择
  formData.个人备注 = props.talent.个人备注 || ''

  console.log('📝 个人信息回显完成:', {
    微信号: formData.微信号,
    联系方式类型: formData.联系方式类型,
    个人备注: formData.个人备注?.substring(0, 50) + (formData.个人备注?.length > 50 ? '...' : '')
  })
  
  // 处理个人标签数组，确保是可操作的副本
  if (Array.isArray(props.talent.个人标签)) {
    formData.个人标签 = [...props.talent.个人标签]
    console.log('🏷️ 个人标签回显:', formData.个人标签)
  } else if (typeof props.talent.个人标签 === 'string') {
    // 如果标签以字符串形式存储，尝试解析
    try {
      const parsedTags = JSON.parse(props.talent.个人标签)
      formData.个人标签 = Array.isArray(parsedTags) ? parsedTags : []
      console.log('🏷️ 解析字符串标签:', formData.个人标签)
    } catch (e) {
      console.warn('⚠️ 标签解析失败，使用空数组:', e)
      formData.个人标签 = []
    }
  } else {
    formData.个人标签 = []
    console.log('🏷️ 无标签数据，使用空数组')
  }
  
  console.log('✨ 表单数据初始化完成，准备回显到界面')
  
  // 强制更新表单显示（确保Vue响应式系统检测到变化）
  nextTick(() => {
    console.log('🔄 强制刷新表单显示，当前表单数据快照:')
    console.log('个人信息字段值:', {
      微信号: formData.微信号,
      手机号: formData.手机号,
      邮箱: formData.邮箱,
      合作状态: formData.合作状态,
      个人备注: formData.个人备注,
      个人标签数量: formData.个人标签.length
    })
  })
}

/**
 * 标签管理方法
 * 提供添加、删除、切换标签的功能，限制最多10个标签
 */

/**
 * 显示自定义标签输入框
 * 点击"添加标签"按钮时调用
 */
const showTagInput = () => {
  tagInputVisible.value = true
  nextTick(() => {
    tagInputRef.value?.focus()
  })
}

/**  
 * 确认添加自定义标签
 * 验证标签内容并添加到个人标签列表
 */
const handleTagConfirm = () => {
  const value = newTagValue.value.trim()
  
  // 检查标签是否有效且不重复
  if (!value) {
    tagInputVisible.value = false
    newTagValue.value = ''
    return
  }
  
  if (formData.个人标签.includes(value)) {
    message.warning('该标签已存在')
    newTagValue.value = ''
    return
  }
  
  // 检查标签数量限制
  if (formData.个人标签.length >= 10) {
    message.warning('最多只能添加10个标签')
    return
  }
  
  // 添加标签
  formData.个人标签.push(value)
  tagInputVisible.value = false
  newTagValue.value = ''
}

/**
 * 移除指定标签
 * @param {string} tag - 要移除的标签名称
 */
const removeTag = (tag) => {
  const index = formData.个人标签.indexOf(tag)
  if (index > -1) {
    formData.个人标签.splice(index, 1)
  }
}

/**
 * 切换预设标签的选中状态
 * 如果标签已选中则移除，未选中则添加
 * @param {string} tag - 预设标签名称
 */
const togglePresetTag = (tag) => {
  const index = formData.个人标签.indexOf(tag)
  
  if (index > -1) {
    // 标签已存在，移除它
    formData.个人标签.splice(index, 1)
  } else {
    // 标签不存在，添加它（需要检查数量限制）
    if (formData.个人标签.length >= 10) {
      message.warning('最多只能添加10个标签')
      return
    }
    formData.个人标签.push(tag)
  }
}



/**
 * 微信号绑定功能
 */
const bindWechatNumber = async () => {
  try {
    // 验证微信号格式
    if (!formData.微信号?.trim()) {
      message.warning('请先输入微信号')
      return
    }

    // 验证微信号格式
    const wechatPattern = /^[a-zA-Z][a-zA-Z0-9_-]{5,19}$/
    if (!wechatPattern.test(formData.微信号.trim())) {
      message.error('微信号格式不正确（6-20位，字母开头，可包含字母数字下划线）')
      return
    }

    bindingWechat.value = true

    console.log('🔗 开始绑定微信号:', {
      达人id: formData.id,
      微信号: formData.微信号.trim()
    })

    // 调用绑定微信号API
    const result = await talentService.bindWechatNumber({
      达人id: formData.id,
      微信号: formData.微信号.trim(),
      平台: '微信'
    })

    if (result.status === 100) {
      message.success('微信号绑定成功！已建立完整的关联关系')
      console.log('✅ 微信号绑定成功:', result.data)
    } else {
      console.error('❌ 微信号绑定失败:', result)
      message.error(result.message || '微信号绑定失败')
    }

  } catch (error) {
    console.error('💥 微信号绑定异常:', error)
    message.error('微信号绑定失败: ' + (error.message || '网络错误'))
  } finally {
    bindingWechat.value = false
  }
}

/**
 * 事件处理方法
 */
/**
 * 处理表单提交
 * 
 * 功能增强：
 * - 智能数据验证：前端预验证减少无效请求
 * - 用户体验优化：详细的加载状态和错误提示
 * - 重试机制：失败时提供重试选项
 * - 操作记录：记录详细的操作日志便于调试
 * 
 * 调用后端API编辑达人个人信息
 * 只提交有实际内容的字段，避免空值覆盖已有数据
 */
const handleSubmit = async () => {
  try {
    console.log('📤 开始提交表单数据', {
      达人id: formData.id,
      时间戳: new Date().toISOString()
    })
    
    // 表单验证
    await formRef.value.validate()
    console.log('✅ 表单验证通过')
    
    loading.value = true
    
    // 智能构建提交数据 - 只包含有效的非空字段
    const submitData = buildSubmitData()
    
    // 检查是否有实际数据需要提交
    if (Object.keys(submitData).length === 1) { // 只有达人id
      message.warning('请至少填写一项信息再提交')
      loading.value = false
      return
    }
    
    console.log('📊 最终提交数据:', {
      达人id: submitData.达人id,
      字段数量: Object.keys(submitData).length - 1,
      字段列表: Object.keys(submitData).filter(key => key !== '达人id')
    })
    
    // 调用达人服务编辑个人信息（服务层已包含重试机制）
    const result = await talentService.editMyTalentInfo(submitData)
    
    if (result.status === 100) {
      message.success('编辑达人信息成功')
      console.log('✅ 编辑达人信息成功，触发成功事件')
      emit('success', submitData)
      visible.value = false
    } else {
      console.error('❌ 后端返回错误:', result)
      handleBusinessError(result)
    }
    
  } catch (error) {
    console.error('💥 编辑达人信息异常:', {
      错误类型: error.name,
      错误消息: error.message,
      达人id: formData.id
    })
    
    handleSubmitError(error)
    
  } finally {
    loading.value = false
  }
}

/**
 * 构建提交数据
 * 只包含用户实际填写的有效字段
 * @returns {Object} 处理后的提交数据
 */
const buildSubmitData = () => {
  const submitData = {
    达人id: formData.id
  }

  // 联系方式：如果用户输入了内容才提交，否则不影响已有数据
  if (formData.微信号?.trim()) {
    submitData.微信号 = formData.微信号.trim()
  }

  // 联系方式类型：如果用户选择了类型才提交
  if (formData.联系方式类型?.trim()) {
    submitData.联系方式类型 = formData.联系方式类型.trim()
  }

  // 个人备注：如果用户输入了内容才提交
  if (formData.个人备注?.trim()) {
    submitData.个人备注 = formData.个人备注.trim()
  }

  // 个人标签：如果有标签才提交
  if (formData.个人标签?.length > 0) {
    submitData.个人标签 = [...formData.个人标签]
  }

  return submitData
}

/**
 * 处理业务错误
 * @param {Object} result - 后端返回的错误结果
 */
const handleBusinessError = (result) => {
  const errorMessage = result.message || '编辑失败'
  
  // 根据错误类型提供不同的用户提示
  if (errorMessage.includes('权限')) {
    message.error('没有权限编辑此达人信息，请确认是否已认领该达人')
  } else if (errorMessage.includes('不存在')) {
    message.error('达人信息不存在，请刷新页面后重试')
  } else {
    message.error(errorMessage)
  }
}

/**
 * 处理提交错误
 * 包括网络错误、超时错误等，提供重试选项
 * @param {Error} error - 错误对象
 */
const handleSubmitError = (error) => {
  if (error.name === 'validateError') {
    console.log('📝 表单验证失败，用户需要修正输入')
    return
  }
  
  // 超时错误 - 提供重试选项
  if (error.message.includes('超时') || error.message.includes('timeout')) {
    Modal.confirm({
      title: '操作超时',
      content: '编辑操作超时，可能是网络较慢或服务器繁忙。是否重试？',
      okText: '重试',
      cancelText: '取消',
      onOk() {
        console.log('🔄 用户选择重试操作')
        handleSubmit() // 重新执行提交
      },
      onCancel() {
        console.log('❌ 用户取消重试')
        message.info('操作已取消，您可以稍后再试')
      }
    })
    return
  }
  
  // 网络错误 - 提供重试选项
  if (error.message.includes('网络') || error.message.includes('连接')) {
    Modal.confirm({
      title: '网络异常',
      content: '网络连接异常，请检查网络后重试。',
      okText: '重试',
      cancelText: '取消',
      onOk() {
        console.log('🔄 用户选择重试操作（网络错误）')
        handleSubmit() // 重新执行提交
      }
    })
    return
  }
  
  // 其他错误
  message.error(error.message || '编辑失败，请稍后重试')
}

// 处理取消编辑
const handleCancel = () => {
  visible.value = false
  emit('cancel')
}

</script>

<style scoped>
.talent-edit-form {
  max-height: 600px;
  overflow-y: auto;
}

.talent-preview {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.talent-avatar {
  margin-right: 16px;
}

.form-help-text {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.4;
}

.talent-basic-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.talent-basic-info p {
  margin: 0 0 8px 0;
  color: #8c8c8c;
  font-size: 14px;
}

.tags-container {
  min-height: 32px;
}

.existing-tags {
  margin-bottom: 8px;
}

.existing-tags .ant-tag {
  margin-bottom: 8px;
}

.preset-tags {
  max-height: 120px;
  overflow-y: auto;
}

.preset-tags .ant-tag {
  margin-right: 8px;
  transition: all 0.3s;
}

.preset-tags .ant-tag:hover {
  transform: scale(1.05);
}

/* 自定义滚动条 */
.talent-edit-form::-webkit-scrollbar {
  width: 6px;
}

.talent-edit-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.talent-edit-form::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.talent-edit-form::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style> 