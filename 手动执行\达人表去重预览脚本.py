"""
达人表 account_douyin 重复值去重预览脚本

功能：
1. 查找并预览所有重复的 account_douyin 记录
2. 显示每个重复组的详细信息
3. 分析将要进行的数据合并操作
4. 提供交互式确认功能

使用方法：
python 手动执行/达人表去重预览脚本.py
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 应用日志器, 错误日志器


class 达人表去重预览器:
    """达人表去重预览器"""
    
    def __init__(self):
        self.预览统计 = {
            "重复组数": 0,
            "总记录数": 0,
            "将删除记录数": 0,
            "将更新记录数": 0
        }
    
    async def 查找重复的account_douyin(self) -> List[Dict[str, Any]]:
        """查找所有有重复值的 account_douyin"""
        try:
            查询SQL = """
            SELECT account_douyin, COUNT(*) as 重复数量
            FROM 达人表 
            WHERE account_douyin IS NOT NULL 
            AND account_douyin != ''
            GROUP BY account_douyin 
            HAVING COUNT(*) > 1
            ORDER BY COUNT(*) DESC, account_douyin
            """
            
            结果 = await 异步连接池实例.执行查询(查询SQL)
            print(f"\n找到 {len(结果)} 个重复的 account_douyin")
            
            return 结果
            
        except Exception as e:
            错误日志器.error(f"查找重复 account_douyin 失败: {str(e)}")
            raise
    
    async def 获取重复记录详情(self, account_douyin: str) -> List[Dict[str, Any]]:
        """获取某个 account_douyin 的所有重复记录"""
        try:
            查询SQL = """
            SELECT id, uid_number, 昵称, account_douyin, avatar, 粉丝数, 关注数, 
                   introduction, city, '性别', update_time, 
                   cmm_info_update_time, douyin_info_update_time
            FROM 达人表 
            WHERE account_douyin = $1 
            ORDER BY id ASC
            """
            
            结果 = await 异步连接池实例.执行查询(查询SQL, (account_douyin,))
            return 结果
            
        except Exception as e:
            错误日志器.error(f"获取重复记录详情失败: account_douyin={account_douyin}, 错误={str(e)}")
            raise
    
    def 分析需要补充的数据(self, 主记录: Dict[str, Any], 其他记录列表: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析其他记录中有哪些数据是主记录没有的"""
        需要补充的数据 = {}
        
        # 检查主要字段
        检查字段列表 = [
            'uid_number', '昵称', 'avatar', '粉丝数', '关注数', 
            'introduction', 'city', '性别'
        ]
        
        for 字段 in 检查字段列表:
            主记录值 = 主记录.get(字段)
            
            # 如果主记录该字段为空或None，检查其他记录是否有值
            if 主记录值 is None or 主记录值 == '' or 主记录值 == 0:
                for 其他记录 in 其他记录列表:
                    其他记录值 = 其他记录.get(字段)
                    if 其他记录值 is not None and 其他记录值 != '' and 其他记录值 != 0:
                        需要补充的数据[字段] = {
                            "新值": 其他记录值,
                            "来源记录id": 其他记录['id']
                        }
                        break
        
        return 需要补充的数据
    
    def 显示记录详情(self, 记录: Dict[str, Any], 是否为主记录: bool = False):
        """显示单条记录的详情"""
        标记 = "【主记录】" if 是否为主记录 else "【重复记录】"
        print(f"  {标记} ID: {记录['id']}")
        print(f"    - UID: {记录.get('uid_number', 'N/A')}")
        print(f"    - 昵称: {记录.get('昵称', 'N/A')}")
        print(f"    - 粉丝数: {记录.get('粉丝数', 'N/A')}")
        print(f"    - 关注数: {记录.get('关注数', 'N/A')}")
        print(f"    - 简介: {记录.get('introduction', 'N/A')[:50]}{'...' if 记录.get('introduction', '') and len(记录.get('introduction', '')) > 50 else ''}")
        print(f"    - 城市: {记录.get('city', 'N/A')}")
        print(f"    - 性别: {记录.get('性别', 'N/A')}")
        print(f"    - 更新时间: {记录.get('update_time', 'N/A')}")
    
    async def 预览单个重复组(self, account_douyin: str) -> Dict[str, Any]:
        """预览单个重复组的处理情况"""
        try:
            print(f"\n{'='*60}")
            print(f"重复账号: {account_douyin}")
            print(f"{'='*60}")
            
            # 获取所有重复记录
            重复记录列表 = await self.获取重复记录详情(account_douyin)
            
            if len(重复记录列表) < 2:
                print(f"⚠️  账号 {account_douyin} 重复记录数量不足，跳过处理")
                return {"跳过": True}
            
            # 第一条记录作为主记录（id最小）
            主记录 = 重复记录列表[0]
            其他记录列表 = 重复记录列表[1:]
            
            print(f"总记录数: {len(重复记录列表)}")
            print(f"将保留记录: ID {主记录['id']} (最小ID)")
            print(f"将删除记录: {[r['id'] for r in 其他记录列表]}")
            
            # 显示主记录详情
            print(f"\n📋 记录详情:")
            self.显示记录详情(主记录, True)
            
            # 显示其他记录详情
            for 记录 in 其他记录列表:
                self.显示记录详情(记录, False)
            
            # 分析需要补充的数据
            补充数据 = self.分析需要补充的数据(主记录, 其他记录列表)
            
            if 补充数据:
                print(f"\n🔄 将要补充到主记录的数据:")
                for 字段, 信息 in 补充数据.items():
                    print(f"  - {字段}: {信息['新值']} (来自记录 ID {信息['来源记录id']})")
            else:
                print(f"\n✅ 主记录数据完整，无需补充")
            
            return {
                "跳过": False,
                "主记录id": 主记录['id'],
                "删除记录数": len(其他记录列表),
                "需要更新": len(补充数据) > 0,
                "补充数据": 补充数据
            }
            
        except Exception as e:
            错误日志器.error(f"预览重复组失败: account_douyin={account_douyin}, 错误={str(e)}")
            return {"跳过": True, "错误": str(e)}
    
    async def 执行预览(self):
        """执行完整的预览流程"""
        try:
            print("🔍 开始预览达人表 account_douyin 重复记录...")
            
            # 查找所有重复的 account_douyin
            重复账号列表 = await self.查找重复的account_douyin()
            
            if not 重复账号列表:
                print("✅ 没有找到重复的 account_douyin")
                return
            
            print(f"\n📊 重复统计:")
            for 重复信息 in 重复账号列表:
                print(f"  - {重复信息['account_douyin']}: {重复信息['重复数量']} 条记录")
            
            # 询问是否继续详细预览
            print(f"\n是否查看详细预览? (y/n): ", end="")
            用户输入 = input().strip().lower()
            
            if 用户输入 != 'y':
                print("预览结束")
                return
            
            # 逐个预览重复组
            for i, 重复信息 in enumerate(重复账号列表):
                account_douyin = 重复信息['account_douyin']
                重复数量 = 重复信息['重复数量']
                
                print(f"\n📍 预览进度: {i + 1}/{len(重复账号列表)}")
                
                预览结果 = await self.预览单个重复组(account_douyin)
                
                if not 预览结果.get("跳过", False):
                    self.预览统计["重复组数"] += 1
                    self.预览统计["总记录数"] += 重复数量
                    self.预览统计["将删除记录数"] += 预览结果.get("删除记录数", 0)
                    if 预览结果.get("需要更新", False):
                        self.预览统计["将更新记录数"] += 1
                
                # 每预览5个组询问是否继续
                if (i + 1) % 5 == 0 and i + 1 < len(重复账号列表):
                    print(f"\n继续预览剩余 {len(重复账号列表) - i - 1} 个重复组? (y/n): ", end="")
                    继续输入 = input().strip().lower()
                    if 继续输入 != 'y':
                        break
            
            # 显示预览统计
            print(f"\n{'='*60}")
            print("📈 预览统计:")
            print(f"  - 重复组数: {self.预览统计['重复组数']}")
            print(f"  - 总记录数: {self.预览统计['总记录数']}")
            print(f"  - 将删除记录数: {self.预览统计['将删除记录数']}")
            print(f"  - 将更新记录数: {self.预览统计['将更新记录数']}")
            print(f"{'='*60}")
            
            # 询问是否执行去重
            print(f"\n是否执行去重操作? (y/n): ", end="")
            执行输入 = input().strip().lower()
            
            if 执行输入 == 'y':
                print("\n🚀 请运行去重脚本执行实际操作:")
                print("python 脚本/达人表去重脚本.py")
            else:
                print("预览结束，未执行去重操作")
            
        except Exception as e:
            错误日志器.error(f"执行预览失败: {str(e)}")
            raise


async def main():
    """主函数"""
    try:
        # 初始化数据库连接池
        await 异步连接池实例.初始化数据库连接池()
        
        # 创建预览器并执行
        预览器 = 达人表去重预览器()
        await 预览器.执行预览()
        
    except Exception as e:
        错误日志器.error(f"脚本执行失败: {str(e)}")
        raise
    finally:
        # 关闭数据库连接池
        await 异步连接池实例.关闭连接池()


if __name__ == "__main__":
    asyncio.run(main())
