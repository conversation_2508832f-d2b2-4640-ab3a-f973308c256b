<template>
  <div class="performance-ranking">
    <!-- 筛选和操作栏 -->
    <div class="ranking-toolbar">
      <div class="toolbar-left">
        <a-radio-group 
          v-model:value="rankingType" 
          @change="handleRankingTypeChange"
          button-style="solid"
          size="large"
        >
          <a-radio-button value="individual">个人排行</a-radio-button>
          <a-radio-button value="team">团队排行</a-radio-button>
        </a-radio-group>
        
        <a-radio-group 
          v-model:value="selectedTimeRange" 
          @change="handleTimeRangeChange"
          button-style="solid"
          size="large"
        >
          <a-radio-button 
            v-for="option in timeRangeOptions" 
            :key="option.value" 
            :value="option.value"
          >
            {{ option.label }}
          </a-radio-button>
        </a-radio-group>
      </div>
      
      <div class="toolbar-right">
        <a-select
          v-model:value="sortBy"
          placeholder="排序方式"
          style="width: 150px"
          size="large"
          @change="handleSortChange"
        >
          <a-select-option value="sales">销售额</a-select-option>
          <a-select-option value="orders">订单数</a-select-option>
          <a-select-option value="commission">佣金收入</a-select-option>
          <a-select-option value="growth">增长率</a-select-option>
        </a-select>
      </div>
    </div>

    <!-- 排行榜主要内容 -->
    <div class="ranking-content">
      <a-row :gutter="[24, 24]">
        <!-- 左侧：前三名展示 -->
        <a-col :xs="24" :lg="8">
          <a-card title="🏆 排行榜前三名" class="top-three-card">
            <div class="top-three-list">
              <div 
                v-for="(item, index) in topThreeData" 
                :key="item.id"
                class="top-item"
                :class="`rank-${index + 1}`"
              >
                <div class="rank-badge">
                  <component :is="getRankIcon(index + 1)" />
                </div>
                <div class="user-info">
                  <div class="user-name">{{ item.name }}</div>
                  <div class="user-team">{{ item.team }}</div>
                </div>
                <div class="performance-value">
                  {{ formatValue(item.value) }}
                  <div class="growth-rate" :class="item.growth.trend">
                    {{ item.growth.display }}
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        
        <!-- 右侧：完整排行榜 -->
        <a-col :xs="24" :lg="16">
          <a-card title="完整排行榜" class="ranking-table-card">
            <template #extra>
              <a-space>
                <a-input-search
                  v-model:value="searchKeyword"
                  placeholder="搜索用户或团队..."
                  @search="handleSearch"
                  style="width: 200px"
                />
                <a-button 
                  :icon="h(ReloadOutlined)"
                  @click="refreshData"
                  :loading="loading"
                >
                  刷新
                </a-button>
              </a-space>
            </template>
            
            <RankingTable
              :data="rankingTableData"
              :type="rankingType"
              :sort-by="sortBy"
              :loading="loading"
              :pagination="pagination"
              @change="handleTableChange"
              @user-click="handleUserClick"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 统计图表区域 -->
    <div class="ranking-charts-section">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :lg="12">
          <a-card title="业绩分布图" class="chart-card">
            <PerformanceDistributionChart
              :data="distributionData"
              :type="rankingType"
              :loading="loading"
              height="300px"
            />
          </a-card>
        </a-col>
        
        <a-col :xs="24" :lg="12">
          <a-card title="增长趋势对比" class="chart-card">
            <GrowthComparisonChart
              :data="growthComparisonData"
              :type="rankingType"
              :loading="loading"
              height="300px"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import {
    CrownOutlined,
    ReloadOutlined,
    StarOutlined,
    TrophyOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, h, onMounted, reactive, ref } from 'vue'
import GrowthComparisonChart from '../../components/gvm/GrowthComparisonChart.vue'
import PerformanceDistributionChart from '../../components/gvm/PerformanceDistributionChart.vue'
import RankingTable from '../../components/gvm/RankingTable.vue'
import gvmService from '../../services/gvmService'

defineOptions({
  name: 'PerformanceRanking'
})

// 响应式数据
const loading = ref(false)
const rankingType = ref('individual') // 'individual' | 'team'
const selectedTimeRange = ref('30d')
const sortBy = ref('sales')
const searchKeyword = ref('')

// 排行榜数据
const topThreeData = ref([])
const rankingTableData = ref([])
const distributionData = ref([])
const growthComparisonData = ref([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`
})

// 时间范围选项
const timeRangeOptions = computed(() => gvmService.getTimeRangeOptions())

// 获取排名图标
const getRankIcon = (rank) => {
  switch (rank) {
    case 1:
      return CrownOutlined
    case 2:
      return TrophyOutlined
    case 3:
      return StarOutlined
    default:
      return StarOutlined
  }
}

// 格式化数值显示
const formatValue = (value) => {
  return gvmService.formatAmount(value)
}

// 加载排行榜数据
const loadRankingData = async () => {
  loading.value = true
  try {
    const response = await gvmService.getRankingData({
      type: rankingType.value,
      timeRange: selectedTimeRange.value,
      sortBy: sortBy.value,
      page: pagination.current,
      pageSize: pagination.pageSize,
      keyword: searchKeyword.value
    })

    if (response.status === 'success') {
      const data = response.data

      // 更新前三名数据
      topThreeData.value = data.topThree.map(item => ({
        id: item.id,
        name: item.name,
        team: item.team || `${item.memberCount}人团队`,
        value: item.sales,
        growth: {
          rate: item.growth.rate,
          trend: item.growth.trend,
          display: item.growth.rate > 0 ? `+${item.growth.rate.toFixed(1)}%` : `${item.growth.rate.toFixed(1)}%`
        }
      }))

      // 更新完整排行榜数据
      rankingTableData.value = data.list
      pagination.total = data.total

      // 更新图表数据
      distributionData.value = data.list
      growthComparisonData.value = data.list.slice(0, 10)
    }
  } catch (error) {
    message.error('加载排行榜数据失败，请重试')
    console.error('加载排行榜数据失败:', error)
  } finally {
    loading.value = false
  }
}

// generateMockData函数已移除，现在使用真实的API数据

// 事件处理
const handleRankingTypeChange = () => {
  loadRankingData()
}

const handleTimeRangeChange = () => {
  loadRankingData()
}

const handleSortChange = () => {
  loadRankingData()
}

const handleSearch = () => {
  pagination.current = 1
  loadRankingData()
}

const refreshData = () => {
  loadRankingData()
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadRankingData()
}

const handleUserClick = (user) => {
  console.log('点击用户:', user)
  // 可以跳转到用户详情页面
}

// 生命周期
onMounted(() => {
  loadRankingData()
})
</script>

<style scoped>
.performance-ranking {
  padding: 0;
}

.ranking-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ranking-content {
  margin-bottom: 24px;
}

.top-three-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  height: 100%;
}

.top-three-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.top-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.top-item:hover {
  background: #f0f0f0;
  transform: translateY(-2px);
}

.top-item.rank-1 {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #8b4513;
}

.top-item.rank-2 {
  background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
  color: #666;
}

.top-item.rank-3 {
  background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
  color: #654321;
}

.rank-badge {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 12px;
  background: rgba(255, 255, 255, 0.3);
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.user-team {
  font-size: 12px;
  opacity: 0.8;
}

.performance-value {
  text-align: right;
  font-size: 18px;
  font-weight: 600;
}

.growth-rate {
  font-size: 12px;
  margin-top: 4px;
}

.growth-rate.up {
  color: #52c41a;
}

.growth-rate.down {
  color: #f5222d;
}

.ranking-table-card,
.chart-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
}

.ranking-charts-section {
  margin-bottom: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ranking-toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .toolbar-left {
    flex-direction: column;
    gap: 12px;
  }
  
  .toolbar-right {
    justify-content: center;
  }
  
  .top-item {
    padding: 12px;
  }
  
  .rank-badge {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
  
  .user-name {
    font-size: 14px;
  }
  
  .performance-value {
    font-size: 16px;
  }
}
</style>
