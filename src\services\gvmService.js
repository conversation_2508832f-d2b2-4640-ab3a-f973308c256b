/**
 * GVM统计服务
 * 提供销售额统计相关的API接口
 */

import request from '../utils/request'

/**
 * GVM统计服务类
 */
class GvmService {
  /**
   * 获取当前用户销售额统计
   * @param {Object} params - 查询参数
   * @param {string} params.时间范围 - 时间范围：1d, 7d, 30d, 90d, all
   * @param {string} params.开始时间 - 自定义开始时间 YYYY-MM-DD
   * @param {string} params.结束时间 - 自定义结束时间 YYYY-MM-DD
   * @returns {Promise} API响应
   */
  async getMyStats(params = {}) {
    try {
      // 修改为POST请求，参数通过请求体传递
      const response = await request.post('/api/gvm/my/stats', params)
      return this.processStatsResponse(response)
    } catch (error) {
      console.error('获取我的销售额统计失败:', error)
      throw error
    }
  }

  /**
   * 获取指定用户销售额统计
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  async getUserStats(userId, params = {}) {
    try {
      const response = await request.get(`/api/gvm/user/${userId}/stats`, { params })
      return this.processStatsResponse(response)
    } catch (error) {
      console.error('获取用户销售额统计失败:', error)
      throw error
    }
  }

  /**
   * 获取团队销售额统计
   * @param {number} teamId - 团队ID
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  async getTeamStats(teamId, params = {}) {
    try {
      const response = await request.get(`/api/gvm/team/${teamId}/stats`, { params })
      return this.processStatsResponse(response, true) // true表示团队数据
    } catch (error) {
      console.error('获取团队销售额统计失败:', error)
      throw error
    }
  }

  /**
   * 获取GVM仪表板数据
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  async getDashboard(params = {}) {
    try {
      const response = await request.get('/api/gvm/dashboard', { params })

      // 处理后端返回的数据格式
      if (response.data && response.data.status === 'success') {
        return {
          status: 'success',
          data: {
            用户统计: response.data.data.用户统计,
            团队统计: response.data.data.团队统计,
            时间范围: response.data.data.时间范围
          }
        }
      }

      return response.data || response
    } catch (error) {
      console.error('获取GVM仪表板数据失败:', error)
      throw error
    }
  }

  /**
   * 获取统计概览（多时间维度对比）
   * @returns {Promise} API响应
   */
  async getStatsSummary() {
    try {
      const response = await request.get('/api/gvm/stats/summary')

      // 处理后端返回的数据格式
      if (response.data && response.data.status === 100) {
        return {
          status: 'success',
          data: {
            今日: response.data.data.今日,
            本周: response.data.data.本周,
            本月: response.data.data.本月
          }
        }
      }

      return response.data || response
    } catch (error) {
      console.error('获取统计概览失败:', error)
      throw error
    }
  }

  /**
   * 统一处理统计数据响应
   * @param {Object} response - API响应
   * @param {boolean} isTeam - 是否为团队数据
   * @returns {Object} 处理后的响应
   */
  processStatsResponse(response, isTeam = false) {
    // 检查响应是否成功 - 后端返回状态码100
    if (response.data && response.data.status === 100) {
      const data = response.data.data.统计数据 || {}

      const baseStats = {
        订单数量: data.订单数量 || 0,
        总销售额: data.总销售额 || 0,
        预估佣金: data.预估佣金 || 0,
        实际佣金: data.实际佣金 || 0,
        平均客单价: data.平均客单价 || 0,
        合作店铺数: data.合作店铺数 || 0,
        关联达人数: data.关联达人数 || 0,
        佣金率: data.佣金率 || 0
      }

      // 团队数据包含额外字段
      if (isTeam) {
        Object.assign(baseStats, {
          参与成员数: data.参与成员数 || 0,
          人均销售额: data.人均销售额 || 0,
          团队效率: data.团队效率 || 0
        })
      }

      return {
        status: 'success',
        data: {
          统计数据: baseStats,
          ...response.data.data
        }
      }
    }

    return response.data || response
  }

  /**
   * 获取用户团队列表
   * @returns {Promise} API响应
   */
  async getUserTeams() {
    try {
      // 使用正确的后端接口路径
      const response = await request.get('/user/teams')

      if (response.data && response.data.status === 'success') {
        return {
          status: 'success',
          data: response.data.data || []
        }
      }

      return response.data || response
    } catch (error) {
      console.error('获取用户团队列表失败:', error)
      throw error
    }
  }

  /**
   * 获取业绩排行榜数据（模拟接口，实际项目中需要后端实现）
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  async getRankingData(params = {}) {
    try {
      // 注意：这是模拟实现，实际项目中需要后端提供排行榜接口
      // const response = await request.get('/api/gvm/ranking', { params })

      // 模拟排行榜数据
      const mockData = this.generateMockRankingData(params)

      return {
        status: 'success',
        data: mockData
      }
    } catch (error) {
      console.error('获取排行榜数据失败:', error)
      throw error
    }
  }

  /**
   * 生成模拟排行榜数据
   * @param {Object} params - 查询参数
   * @returns {Object} 模拟数据
   */
  generateMockRankingData(params) {
    const { type = 'individual', sortBy = 'sales', page = 1, pageSize = 20 } = params

    // 生成模拟数据
    const totalCount = 50
    const data = []

    for (let i = 1; i <= totalCount; i++) {
      const baseValue = Math.floor(Math.random() * 100000) + 50000
      data.push({
        id: i,
        rank: i,
        name: type === 'individual' ? `用户${i}` : `团队${i}`,
        team: type === 'individual' ? `团队${Math.ceil(i / 10)}` : undefined,
        memberCount: type === 'team' ? Math.floor(Math.random() * 20) + 5 : undefined,
        sales: baseValue,
        orders: Math.floor(baseValue / 300) + Math.floor(Math.random() * 50),
        commission: Math.floor(baseValue * 0.08) + Math.floor(Math.random() * 1000),
        growth: {
          rate: (Math.random() - 0.5) * 50,
          trend: Math.random() > 0.3 ? 'up' : 'down'
        }
      })
    }

    // 根据排序字段排序
    data.sort((a, b) => {
      switch (sortBy) {
        case 'orders':
          return b.orders - a.orders
        case 'commission':
          return b.commission - a.commission
        case 'growth':
          return b.growth.rate - a.growth.rate
        default:
          return b.sales - a.sales
      }
    })

    // 更新排名
    data.forEach((item, index) => {
      item.rank = index + 1
    })

    // 分页
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const pageData = data.slice(startIndex, endIndex)

    return {
      list: pageData,
      total: totalCount,
      page,
      pageSize,
      topThree: data.slice(0, 3)
    }
  }

  /**
   * 计算增长率数据
   * @param {Object} currentData - 当前数据
   * @param {Object} previousData - 对比数据
   * @returns {Object} 增长率信息
   */
  calculateGrowthRates(currentData, previousData) {
    const growthRates = {}

    const fields = ['总销售额', '订单数量', '平均客单价', '实际佣金']

    fields.forEach(field => {
      const current = currentData[field] || 0
      const previous = previousData[field] || 0

      growthRates[field] = this.calculateGrowthRate(current, previous)
    })

    return growthRates
  }

  /**
   * 格式化金额显示
   * @param {number} amount - 金额
   * @returns {string} 格式化后的金额字符串
   */
  formatAmount(amount) {
    if (!amount || amount === 0) return '¥0'

    if (amount >= 10000) {
      return `¥${(amount / 10000).toFixed(1)}万`
    } else if (amount >= 1000) {
      return `¥${(amount / 1000).toFixed(1)}k`
    } else {
      return `¥${amount.toFixed(2)}`
    }
  }

  /**
   * 格式化百分比显示
   * @param {number} percentage - 百分比数值
   * @returns {string} 格式化后的百分比字符串
   */
  formatPercentage(percentage) {
    if (!percentage && percentage !== 0) return '0%'
    return `${percentage.toFixed(1)}%`
  }

  /**
   * 计算增长率
   * @param {number} current - 当前值
   * @param {number} previous - 之前值
   * @returns {Object} 增长率信息
   */
  calculateGrowthRate(current, previous) {
    if (!previous || previous === 0) {
      return {
        rate: current > 0 ? 100 : 0,
        trend: current > 0 ? 'up' : 'stable',
        display: current > 0 ? '+100%' : '0%'
      }
    }

    const rate = ((current - previous) / previous) * 100
    const trend = rate > 0 ? 'up' : rate < 0 ? 'down' : 'stable'
    const display = rate > 0 ? `+${rate.toFixed(1)}%` : `${rate.toFixed(1)}%`

    return { rate, trend, display }
  }

  /**
   * 获取时间范围选项
   * @returns {Array} 时间范围选项数组
   */
  getTimeRangeOptions() {
    return [
      { label: '今日', value: '1d' },
      { label: '本周', value: '7d' },
      { label: '本月', value: '30d' },
      { label: '近3个月', value: '90d' },
      { label: '全部', value: 'all' }
    ]
  }

  /**
   * 获取图表配色方案
   * @returns {Object} 图表配色配置
   */
  getChartColors() {
    return {
      primary: '#1890ff',
      success: '#52c41a',
      warning: '#faad14',
      error: '#f5222d',
      gradient: ['#1890ff', '#36cfc9', '#52c41a'],
      series: [
        '#1890ff', '#36cfc9', '#52c41a', '#faad14',
        '#f759ab', '#9254de', '#ff7a45', '#ffc53d'
      ]
    }
  }

  /**
   * 计算增长率
   * @param {Object} currentData - 当前数据
   * @param {Object} previousData - 对比数据
   * @returns {Object} 增长率数据
   */
  calculateGrowthRates(currentData, previousData) {
    const calculateRate = (current, previous) => {
      if (!previous || previous === 0) {
        return { rate: 0, trend: 'stable', display: '0%' }
      }

      const rate = ((current - previous) / previous) * 100
      const trend = rate > 0 ? 'up' : rate < 0 ? 'down' : 'stable'
      const display = rate > 0 ? `+${rate.toFixed(1)}%` : `${rate.toFixed(1)}%`

      return { rate, trend, display }
    }

    return {
      总销售额: calculateRate(currentData.总销售额 || 0, previousData.总销售额 || 0),
      订单数量: calculateRate(currentData.订单数量 || 0, previousData.订单数量 || 0),
      平均客单价: calculateRate(currentData.平均客单价 || 0, previousData.平均客单价 || 0),
      实际佣金: calculateRate(currentData.实际佣金 || 0, previousData.实际佣金 || 0)
    }
  }
}

// 创建服务实例
const gvmService = new GvmService()

export default gvmService
export { GvmService }

