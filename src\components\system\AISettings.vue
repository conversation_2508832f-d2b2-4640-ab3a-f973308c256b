<template>
  <div class="ai-settings">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>
            <RobotOutlined style="margin-right: 8px; color: #1890ff;" />
            智能体管理
          </h2>
          <p class="subtitle">管理和使用您可用的LangChain智能体</p>
        </div>
        <div class="action-section">
          <a-button
            type="primary"
            ghost
            @click="handleCreateAgent"
            style="margin-right: 8px;"
          >
            基于模板创建
          </a-button>
          <a-button
            type="primary"
            :loading="refreshing"
            @click="refreshAgentsList"
            style="margin-right: 8px;"
          >
            <ReloadOutlined />
            刷新列表
          </a-button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <a-card class="filter-card" style="margin-bottom: 16px;">
      <a-row :gutter="16" align="middle">
        <a-col :span="6">
          <a-input
            v-model:value="searchKeyword"
            placeholder="搜索智能体名称或描述"
            allow-clear
            @change="handleSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="18">
          <div class="stats-info">
            <a-statistic
              title="可用智能体"
              :value="total"
              style="display: inline-block; margin-right: 32px;"
            />
            <a-statistic
              title="当前页"
              :value="`${currentPage}/${Math.ceil(total / pageSize)}`"
              style="display: inline-block;"
            />
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 智能体列表 -->
    <AgentList
      :agents="agentsList"
      :loading="loading"
      :total="total"
      :current-page="currentPage"
      :page-size="pageSize"
      @edit="handleEditAgent"
      @view="handleViewAgent"
      @use="handleUseAgent"
      @page-change="handlePageChange"
      @refresh="refreshAgentsList"
    />

    <!-- 智能体详情Modal -->
    <AgentDetailModal
      v-model:visible="detailModalVisible"
      :agent="selectedAgent"
      @edit="handleEditAgent"
      @use="handleUseAgent"
    />

    <!-- 创建智能体Modal -->
    <AgentCreateModal
      v-model:visible="createModalVisible"
      :template-options="templateOptions"
      :knowledge-base-options="knowledgeBaseOptions"
      :loading-templates="loadingTemplates"
      :loading-k-bs="loadingKBs"
      @success="handleCreateSuccess"
    />

    <!-- 编辑智能体Modal -->
    <AgentEditModal
      v-model:visible="editModalVisible"
      :agent-id="editingAgentId"
      :knowledge-base-options="knowledgeBaseOptions"
      :loading-k-bs="loadingKBs"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { RobotOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import api from '@/services/api'

// 导入子组件
import AgentList from './AISettings/AgentList.vue'
import AgentDetailModal from './AISettings/AgentDetailModal.vue'
import AgentCreateModal from './AISettings/AgentCreateModal.vue'
import AgentEditModal from './AISettings/AgentEditModal.vue'

defineOptions({ name: 'AISettings' })

const router = useRouter()

// 响应式数据
const loading = ref(true)
const refreshing = ref(false)
const agentsList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(12)

// 搜索
const searchKeyword = ref('')

// Modal状态
const detailModalVisible = ref(false)
const createModalVisible = ref(false)
const editModalVisible = ref(false)
const selectedAgent = ref(null)
const editingAgentId = ref(null)

// 创建和编辑相关状态
const loadingTemplates = ref(false)
const loadingKBs = ref(false)
const templateOptions = ref([])
const knowledgeBaseOptions = ref([])

// 页面初始化
onMounted(async () => {
  await loadAgentsList()
})

// 加载智能体列表
const loadAgentsList = async () => {
  try {
    loading.value = true

    const params = {
      页码: currentPage.value,
      每页数量: pageSize.value
    }

    // 添加搜索条件
    if (searchKeyword.value.trim()) {
      params.搜索关键词 = searchKeyword.value.trim()
    }

    console.log('🔍 加载智能体列表，参数:', params)

    const response = await api.post('/user/langchain/agents/available', params)
    console.log('📥 智能体列表响应:', response)

    if (response?.status === 100 && response?.data) {
      const data = response.data
      agentsList.value = data.智能体列表 || []
      total.value = data.总数量 || 0

      console.log(`✅ 成功加载 ${agentsList.value.length} 个智能体`)
    } else {
      console.error('❌ 智能体列表加载失败:', response)
      message.error(response?.message || '加载智能体列表失败')
      agentsList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('💥 加载智能体列表失败:', error)
    message.error('加载智能体列表失败，请稍后重试')
    agentsList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 刷新列表
const refreshAgentsList = async () => {
  refreshing.value = true
  try {
    await loadAgentsList()
    message.success('智能体列表已刷新')
  } finally {
    refreshing.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  loadAgentsList()
}

// 分页处理
const handlePageChange = (page, size) => {
  currentPage.value = page
  if (size !== pageSize.value) {
    pageSize.value = size
  }
  loadAgentsList()
}

// 智能体操作
const handleCreateAgent = async () => {
  createModalVisible.value = true
  await Promise.all([
    loadTemplates(),
    loadKnowledgeBases()
  ])
}

const handleEditAgent = async (agent) => {
  try {
    console.log('🔧 编辑智能体:', agent)
    const agentId = getAgentId(agent)
    console.log('📋 智能体ID:', agentId)

    if (!agentId) {
      message.error('智能体ID缺失，无法编辑')
      return
    }

    editingAgentId.value = agentId
    console.log('📝 设置编辑ID:', editingAgentId.value)

    // 先加载知识库列表
    await loadKnowledgeBases()

    // 然后显示Modal
    editModalVisible.value = true
    console.log('🔓 编辑Modal已打开')

  } catch (error) {
    console.error('❌ 编辑智能体失败:', error)
    message.error('打开编辑页面失败')
  }
}

const handleViewAgent = (agent) => {
  try {
    console.log('👁️ 查看智能体详情:', agent)
    selectedAgent.value = agent
    detailModalVisible.value = true
  } catch (error) {
    console.error('查看智能体详情失败:', error)
    message.error('打开详情页面失败')
  }
}

const handleUseAgent = (agent) => {
  try {
    console.log('🚀 使用智能体:', agent)
    const agentId = getAgentId(agent)
    if (!agentId) {
      message.error('智能体ID缺失，无法启动')
      return
    }

    const chatPath = `/chat?agent_id=${agentId}&agent_name=${encodeURIComponent(agent.智能体名称 || '智能体')}`
    message.success(`正在启动智能体: ${agent.智能体名称 || '智能体'}`)
    router.push(chatPath)
  } catch (error) {
    console.error('启动智能体失败:', error)
    message.error('启动智能体失败')
  }
}

// 辅助函数
const getAgentId = (agent) => {
  return agent?.智能体id || agent?.id || agent?.langchain_智能体配置表id
}

// 加载模板列表
const loadTemplates = async () => {
  try {
    loadingTemplates.value = true
    const res = await api.post('/user/langchain/agents/templates', { 页码: 1, 每页数量: 50 })
    if (res?.status === 100 && res?.data?.智能体列表) {
      const list = res.data.智能体列表
      templateOptions.value = (list || []).map(a => ({
        label: a.智能体名称,
        value: getAgentId(a)
      }))
    }
  } catch (e) {
    console.error(e)
  } finally {
    loadingTemplates.value = false
  }
}

// 加载知识库列表
const loadKnowledgeBases = async () => {
  console.log('📚 开始加载知识库列表')
  try {
    loadingKBs.value = true
    const res = await api.post('/user/langchain/knowledge/list', { 页码: 1, 每页数量: 100 })
    console.log('📥 知识库列表API响应:', res)

    const items = (res?.data?.知识库列表 || res?.知识库列表 || [])
    console.log('📋 原始知识库数据:', items)

    knowledgeBaseOptions.value = (items || []).map(k => ({
      label: k.知识库名称 || k.名称 || `知识库${k.id || ''}`,
      value: k.id || k.知识id
    }))

    console.log('✅ 知识库列表处理完成:', knowledgeBaseOptions.value)
    console.log('📚 加载知识库列表成功:', knowledgeBaseOptions.value.length, '个知识库')
  } catch (e) {
    console.error('❌ 加载知识库列表失败:', e)
    message.error('加载知识库列表失败')
  } finally {
    loadingKBs.value = false
    console.log('🏁 知识库加载操作完成')
  }
}

// 获取智能体关联知识库列表（专用接口）
const getAgentKnowledgeBases = async (agentId) => {
  console.log('🔗 开始获取智能体关联知识库:', agentId)
  try {
    const res = await api.post('/user/langchain/agents/knowledge-bases', { 智能体id: agentId })
    console.log('📥 智能体关联知识库API响应:', res)

    if (res?.status === 100) {
      const 知识库列表 = res?.data?.知识库列表 || []
      console.log('✅ 获取智能体关联知识库成功:', 知识库列表)
      return 知识库列表
    } else {
      console.error('❌ 获取智能体关联知识库失败:', res?.message)
      throw new Error(res?.message || '获取智能体关联知识库失败')
    }
  } catch (e) {
    console.error('❌ 获取智能体关联知识库异常:', e)
    throw e
  }
}

// 成功回调
const handleCreateSuccess = async () => {
  try {
    console.log('✅ 智能体创建成功')
    createModalVisible.value = false
    await refreshAgentsList()
    message.success('智能体创建成功')
  } catch (error) {
    console.error('创建成功后刷新列表失败:', error)
  }
}

const handleEditSuccess = async () => {
  try {
    console.log('✅ 智能体更新成功')
    editModalVisible.value = false
    editingAgentId.value = null
    await refreshAgentsList()
    message.success('智能体更新成功')
  } catch (error) {
    console.error('更新成功后刷新列表失败:', error)
  }
}
</script>

<style scoped>
.ai-settings {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.subtitle {
  margin: 8px 0 0 0;
  color: #8c8c8c;
  font-size: 14px;
}

.action-section {
  display: flex;
  align-items: center;
}

/* 筛选卡片 */
.filter-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stats-info {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 智能体容器 */
.agents-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  color: #8c8c8c;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* 智能体网格 */
.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.agent-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.agent-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.agent-title {
  display: flex;
  align-items: center;
}

.agent-name {
  font-weight: 500;
  font-size: 16px;
}

.agent-tags {
  display: flex;
  gap: 4px;
}

.agent-content {
  padding: 0;
}

.agent-description {
  color: #595959;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  min-height: 42px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.agent-details {
  background: #fafafa;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  font-size: 13px;
  color: #8c8c8c;
  font-weight: 500;
}

.detail-item .value {
  font-size: 13px;
  color: #262626;
}

.agent-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
}

.agent-actions .ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

/* 智能体详情模态框 */
.agent-detail-content {
  padding: 8px 0;
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
}

.detail-tags {
  display: flex;
  gap: 4px;
}

.detail-section h4 {
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.description-text {
  color: #595959;
  line-height: 1.6;
  background: #fafafa;
  padding: 12px;
  border-radius: 6px;
  margin: 0;
}

.detail-actions {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .agents-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .ai-settings {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .agents-grid {
    grid-template-columns: 1fr;
  }

  .agents-container {
    padding: 16px;
  }

  .section-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .detail-actions {
    flex-direction: column;
    gap: 8px;
  }

  .detail-actions .ant-btn {
    width: 100%;
  }
}
</style>


