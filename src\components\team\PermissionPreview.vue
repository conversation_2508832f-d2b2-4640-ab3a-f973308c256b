<template>
  <div class="permission-preview">
    <a-spin :spinning="loading">
      <div v-if="permissionCategories.length > 0" class="permission-content">
        <a-alert
          type="info"
          show-icon
          message="权限预览"
          description="加入该团队后，您将获得以下权限："
          style="margin-bottom: 16px;"
        />
        
        <a-row :gutter="[16, 16]">
          <a-col
            v-for="category in filteredCategories"
            :key="category.分类ID"
            :span="12"
          >
            <a-card 
              :title="category.分类名称" 
              size="small" 
              class="permission-category-card"
            >
              <template #extra>
                <a-tag :color="getCategoryColor(category)">
                  {{ category.权限列表.length }}个权限
                </a-tag>
              </template>
              
              <div class="permission-list">
                <div
                  v-for="permission in category.权限列表"
                  :key="permission.权限代码"
                  class="permission-item"
                >
                  <div class="permission-info">
                    <CheckCircleFilled class="permission-icon" />
                    <div class="permission-details">
                      <span class="permission-name">
                        {{ permission.权限名称 }}
                      </span>
                      <div 
                        v-if="showDescription && permission.权限描述" 
                        class="permission-desc"
                      >
                        {{ permission.权限描述 }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
        
        <!-- 权限统计 -->
        <div class="permission-summary">
          <a-row :gutter="16">
            <a-col :span="8">
            <a-statistic 
              title="权限分类" 
              :value="filteredCategories.length" 
              suffix="类"
            />
            </a-col>
            <a-col :span="8">
            <a-statistic 
              title="总权限数" 
              :value="totalPermissions" 
              suffix="个"
            />
            </a-col>
            <a-col :span="8">
            <a-statistic 
              title="核心权限" 
              :value="corePermissions" 
              suffix="个"
            />
            </a-col>
          </a-row>
        </div>
      </div>
      
      <!-- 无权限时的提示 -->
      <div v-else-if="!loading" class="no-permissions">
        <a-empty
          description="暂无权限信息"
          :image="false"
        >
          <template #description>
            <span>该角色暂未配置任何权限</span>
          </template>
        </a-empty>
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { CheckCircleFilled } from '@ant-design/icons-vue'
import teamService from '../../services/team'

defineOptions({
  name: 'PermissionPreview'
})

const props = defineProps({
  // 权限代码列表
  permissions: {
    type: Array,
    default: () => []
  },
  // 团队信息
  team: {
    type: Object,
    required: true
  },
  // 是否显示权限描述
  showDescription: {
    type: Boolean,
    default: true
  },
  // 布局模式
  layout: {
    type: String,
    default: 'grid', // 'grid' | 'list'
    validator: value => ['grid', 'list'].includes(value)
  }
})

// 响应式数据
const loading = ref(false)
const allPermissions = ref([])

// 计算属性
const teamId = computed(() => props.team?.团队id || props.team?.id)

// 将所有权限按分类分组
const permissionCategories = computed(() => {
  if (!allPermissions.value.length) return []
  
  const categoriesMap = new Map()
  
  allPermissions.value.forEach(permission => {
    const categoryName = permission.权限分类 || '其他权限'
    if (!categoriesMap.has(categoryName)) {
      categoriesMap.set(categoryName, {
        分类ID: categoryName,
        分类名称: categoryName,
        权限列表: []
      })
    }
    categoriesMap.get(categoryName).权限列表.push(permission)
  })
  
  return Array.from(categoriesMap.values())
})

// 过滤用户拥有的权限分类
const filteredCategories = computed(() => {
  if (!props.permissions.length) return []
  
  return permissionCategories.value
    .map(category => ({
      ...category,
      权限列表: category.权限列表.filter(permission => 
        props.permissions.includes(permission.权限代码)
      )
    }))
    .filter(category => category.权限列表.length > 0)
})

// 权限统计
const totalPermissions = computed(() => {
  return filteredCategories.value.reduce((sum, category) => 
    sum + category.权限列表.length, 0
  )
})

const corePermissions = computed(() => {
  const corePermissionCodes = [
    'team_view', 'member_view', 'project_view', 
    'data_view', 'report_view'
  ]
  return props.permissions.filter(code => 
    corePermissionCodes.includes(code)
  ).length
})

// 方法
const getCategoryColor = (category) => {
  const colors = ['blue', 'green', 'orange', 'purple', 'red', 'cyan']
  const index = Math.abs(category.分类名称.length) % colors.length
  return colors[index]
}

const loadAllPermissions = async () => {
  if (!teamId.value) return
  
  try {
    loading.value = true
    const response = await teamService.getAllPermissions()
    
    if (response.status === 100) {
      allPermissions.value = response.data || []
    }
  } catch (error) {
    console.error('加载权限列表失败:', error)
    allPermissions.value = []
  } finally {
    loading.value = false
  }
}

// 监听器
watch(() => teamId.value, (newTeamId) => {
  if (newTeamId) {
    loadAllPermissions()
  }
})

// 生命周期
onMounted(() => {
  if (teamId.value) {
    loadAllPermissions()
  }
})
</script>

<style scoped>
.permission-preview {
  padding: 16px 0;
}

.permission-category-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.permission-category-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.permission-list {
  max-height: 200px;
  overflow-y: auto;
}

.permission-item {
  margin-bottom: 12px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.permission-item:hover {
  background: #e9ecef;
}

.permission-info {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.permission-icon {
  color: #52c41a;
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.permission-details {
  flex: 1;
  min-width: 0;
}

.permission-name {
  font-weight: 500;
  color: #262626;
  display: block;
  line-height: 1.4;
}

.permission-desc {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
  line-height: 1.3;
}

.permission-summary {
  margin-top: 24px;
  padding: 16px;
  background: linear-gradient(135deg, #f0f2ff 0%, #f6ffed 100%);
  border-radius: 8px;
  border: 1px solid #d9d9d9;
}

.no-permissions {
  text-align: center;
  padding: 40px 20px;
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .permission-category-card {
    margin-bottom: 16px;
  }
  
  .permission-item {
    padding: 6px;
  }
  
  .permission-name {
    font-size: 13px;
  }
  
  .permission-desc {
    font-size: 11px;
  }
}
</style> 