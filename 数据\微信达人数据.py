"""
微信达人数据访问层 (重构版)

功能概述：
- 基于实际数据库结构重新设计的微信达人数据访问层
- 与抖音达人数据层保持一致的接口设计
- 支持流式分页查询，提高大数据量下的性能

数据库结构：
- 微信达人表: 微信达人表
  主要字段: id, finderUsername, 昵称, 头像, 粉丝数文本, 有无联系方式, 内容类型, 带货类目等
- 认领表: 用户_微信达人_关联表
  字段: id, 用户id, 微信达人id, 认领时间, 状态, 备注
- 联系方式: 联系方式表 + 微信达人联系方式关联表
  联系方式表字段: id, 联系方式, 类型, 来源, 创建时间, 更新时间

作者: CRM系统开发团队
创建时间: 2024-06-25
更新时间: 2024-06-25 (根据实际数据库结构重构)
"""

import json
from datetime import datetime
from typing import Any, Dict, Optional

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 应用日志器 as 数据库日志器
from 日志 import 错误日志器


async def 异步获取微信达人列表(
    页码: int = 1,
    每页数量: int = 20,  # 优化为每次加载20个达人
    最后id: int = 0,
    当前用户id: Optional[int] = None,
    关键词: Optional[str] = None,
    筛选条件: Optional[Dict[str, Any]] = None,
    有联系方式: Optional[bool] = None,
    当前团队id: Optional[int] = None,
) -> Dict[str, Any]:
    """
    获取微信达人公海列表（支持流式分页）

    参数:
        页码: 当前页码，默认1
        每页数量: 每页显示数量，默认20
        最后id: 流式分页的最后id，默认0
        当前用户id: 当前用户id，用于排除已认领的达人
        关键词: 搜索关键词（昵称、finderUsername）
        筛选条件: 额外的筛选条件
        有联系方式: 是否筛选有联系方式的达人
        当前团队id: 当前团队id，用于团队维度的认领状态判断

    返回:
        包含分页信息和微信达人列表的字典
    """
    try:
        # 构建基础查询SQL
        base_sql = """
        SELECT
            wd.id,
            wd.finderUsername,
            wd.昵称,
            wd.头像,
            wd.内容类型,
            wd.带货类目,
            wd.粉丝数文本,
            wd.GMV文本,
            wd.有无联系方式,
            wd.创建时间,
            wd.更新时间,
            -- 联系方式统计
            CASE WHEN wc.contact_count > 0 THEN 1 ELSE 0 END as 有联系方式_计算,
            -- 添加主要联系方式内容
            wc.主要联系方式,
            wc.主要联系方式类型,
            -- 认领状态
            CASE WHEN wcl.id IS NOT NULL THEN 1 ELSE 0 END as 已认领,
            wcl.用户id as 认领用户id,
            wcl.认领时间,
            -- 当前用户认领状态
            CASE WHEN wcl_current.id IS NOT NULL THEN 1 ELSE 0 END as 当前用户已认领
        FROM 微信达人表 wd
        LEFT JOIN (
            SELECT
                r.微信达人id,
                COUNT(*) as contact_count,
                -- 获取优先级最高的联系方式（微信 > 手机 > 其他）
                SPLIT_PART(STRING_AGG(
                    ct.联系方式,
                    ',' ORDER BY
                        CASE
                            WHEN ct.类型 = '微信' THEN 1
                            WHEN ct.类型 IN ('手机', 'phone') THEN 2
                            ELSE 3
                        END,
                        ct.id
                ), ',', 1) as 主要联系方式,
                SPLIT_PART(STRING_AGG(
                    ct.类型,
                    ',' ORDER BY
                        CASE
                            WHEN ct.类型 = '微信' THEN 1
                            WHEN ct.类型 IN ('手机', 'phone') THEN 2
                            ELSE 3
                        END,
                        ct.id
                ), ',', 1) as 主要联系方式类型
            FROM 微信达人联系方式关联表 r
            JOIN 联系方式表 ct ON r.联系方式id = ct.id
            GROUP BY r.微信达人id
        ) wc ON wd.id = wc.微信达人id
        LEFT JOIN 用户达人关联表 wcl ON wd.id = wcl.达人id AND wcl.状态 = 1 AND wcl.平台 = '微信'
        LEFT JOIN 用户达人关联表 wcl_current ON wd.id = wcl_current.达人id AND wcl_current.用户id = $1 AND wcl_current.状态 = 1 AND wcl_current.平台 = '微信'
        """

        # 构建WHERE条件
        where_conditions = []
        params = [当前用户id]
        参数索引 = 2

        # 流式分页条件
        if 最后id > 0:
            where_conditions.append("wd.id > $1")
            params.append(最后id)
            参数索引 += 1

        # 排除当前用户已认领的达人
        if 当前用户id is not None:
            where_conditions.append("wcl_current.id IS NULL")

        # 关键词搜索
        if 关键词:
            # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
            # {{ Source: context7-mcp on 'PostgreSQL Parameter Syntax' }}
            where_conditions.append(
                f"(wd.昵称 LIKE ${参数索引} OR wd.finderUsername LIKE ${参数索引 + 1})"
            )
            search_term = f"%{关键词}%"
            params.extend([search_term, search_term])
            参数索引 += 2

        # 联系方式筛选
        if 有联系方式 is not None:
            if 有联系方式:
                where_conditions.append("(wd.有无联系方式 = 1 OR wc.contact_count > 0)")
            else:
                where_conditions.append(
                    "(wd.有无联系方式 != 1 AND (wc.contact_count IS NULL OR wc.contact_count = 0))"
                )

        # 构建完整SQL
        if where_conditions:
            where_clause = " WHERE " + " AND ".join(where_conditions)
        else:
            where_clause = ""

        query_sql = base_sql + where_clause + " ORDER BY wd.id ASC LIMIT $4"
        params.append(每页数量)

        数据库日志器.debug(f"执行微信达人查询SQL: {query_sql}, 参数: {params}")
        达人列表 = await 异步连接池实例.执行查询(query_sql, params)

        # 处理JSON字段
        for 达人 in 达人列表:
            # 处理内容类型和带货类目JSON字段
            for 字段 in ["内容类型", "带货类目"]:
                if 达人.get(字段) and isinstance(达人[字段], str):
                    try:
                        达人[字段] = json.loads(达人[字段])
                    except (json.JSONDecodeError, TypeError):
                        达人[字段] = []
                elif not 达人.get(字段):
                    达人[字段] = []

        # 查询团队内认领状态 - 基于团队维度优化
        if 达人列表 and 当前用户id is not None and 当前团队id is not None:
            达人id列表 = [达人["id"] for 达人 in 达人列表]

            # 基于团队维度查询认领状态 - 只显示当前团队内的认领状态
            团队认领状态查询 = """
                SELECT uwda.达人id as 微信达人id, u.昵称 as 认领者昵称
                FROM 用户达人关联表 uwda
                INNER JOIN 用户团队关联表 utg ON uwda.用户id = utg.用户id
                INNER JOIN 用户表 u ON uwda.用户id = u.id
                WHERE uwda.达人id IN ({})
                AND uwda.状态 = 1
                AND uwda.平台 = '微信'
                AND utg.团队id = $1
                AND utg.状态 = '正常'
            """.format(",".join(["$1"] * len(达人id列表)))

            团队查询参数 = 达人id列表 + [当前团队id]
            团队认领状态结果 = await 异步连接池实例.执行查询(
                团队认领状态查询, 团队查询参数
            )

            # 构建团队内认领状态映射
            团队认领状态映射 = {}
            for 状态 in 团队认领状态结果:
                团队认领状态映射[状态["微信达人id"]] = {
                    "已认领": True,
                    "团队内认领": True,
                    "认领者": 状态.get("认领者昵称") or "团队成员",
                }

            # 更新达人列表中的认领状态信息
            for 达人 in 达人列表:
                达人_id = 达人["id"]
                if 达人_id in 团队认领状态映射:
                    达人.update(团队认领状态映射[达人_id])
                else:
                    达人.update({"已认领": False, "团队内认领": False, "认领者": None})

            数据库日志器.info(
                f"基于团队 {当前团队id} 查询微信达人认领状态，发现 {len(团队认领状态结果)} 个团队内认领的达人"
            )

        # 计算下一页的最后id
        下一页最后id = 达人列表[-1]["id"] if 达人列表 else 最后id

        数据库日志器.info(f"微信达人列表查询成功，返回 {len(达人列表)} 条记录")

        return {
            "当前页": 页码,
            "每页数量": 每页数量,
            "最后id": 最后id,  # 当前页的最后id
            "下一页最后id": 下一页最后id,  # 下一页查询用的最后id
            "达人列表": 达人列表,
            "是否还有更多": len(达人列表) == 每页数量,
            "团队模式": 当前团队id is not None,  # 标识是否为团队模式
        }

    except Exception as e:
        错误日志器.error(f"获取微信达人列表失败: {str(e)}")
        raise


async def 异步获取微信达人详情(达人id: int, 用户id: int) -> Optional[Dict[str, Any]]:
    """
    获取微信达人详情信息

    参数:
        达人id: 达人的唯一标识ID
        用户id: 当前用户id

    返回:
        包含达人详情、联系方式和认领状态的字典，如果不存在则返回None
    """
    try:
        # 获取达人基本信息
        基本信息SQL = """
        SELECT 
            id, finderUsername, 昵称, 头像, 内容类型, 带货类目,
            粉丝数文本, GMV文本, 有无联系方式, 创建时间, 更新时间
        FROM 微信达人表 
        WHERE id = $1
        """

        基本信息结果 = await 异步连接池实例.执行查询(基本信息SQL, [达人id])

        if not 基本信息结果:
            数据库日志器.warning(f"微信达人 {达人id} 不存在")
            return None

        基本信息 = 基本信息结果[0]

        # 处理JSON字段
        for 字段 in ["内容类型", "带货类目"]:
            if 基本信息.get(字段) and isinstance(基本信息[字段], str):
                try:
                    基本信息[字段] = json.loads(基本信息[字段])
                except (json.JSONDecodeError, TypeError):
                    基本信息[字段] = []
            elif not 基本信息.get(字段):
                基本信息[字段] = []

        # 获取联系方式列表
        联系方式SQL = """
        SELECT c.id, c.联系方式, c.类型, c.来源, c.创建时间
        FROM 联系方式表 c
        INNER JOIN 微信达人联系方式关联表 wc ON c.id = wc.联系方式id
        WHERE wc.微信达人id = $1
        ORDER BY c.创建时间 DESC
        """

        联系方式结果 = await 异步连接池实例.执行查询(联系方式SQL, [达人id])

        联系方式列表 = []
        for 联系方式 in 联系方式结果:
            联系方式列表.append(
                {
                    "id": 联系方式[0],
                    "联系内容": 联系方式[1],
                    "联系类型": 联系方式[2],
                    "来源": 联系方式[3],
                    "创建时间": 联系方式[4],
                }
            )

        # 获取认领状态
        认领状态SQL = """
        SELECT id, 用户id, 认领时间, 状态, 备注
        FROM 用户达人关联表
        WHERE 达人id = $1 AND 状态 = 1 AND 平台 = '微信'
        """

        认领状态结果 = await 异步连接池实例.执行查询(认领状态SQL, [达人id])

        # 构建返回结果
        达人详情 = {
            **基本信息,
            "联系方式列表": 联系方式列表,
            "已认领": len(认领状态结果) > 0,
            "当前用户认领状态": None,
        }

        # 检查当前用户是否认领了这个达人
        for 认领记录 in 认领状态结果:
            if 认领记录[1] == 用户id:  # 用户id字段
                达人详情["当前用户认领状态"] = {
                    "已认领": True,
                    "认领时间": 认领记录[2],
                    "备注": 认领记录[4],
                }
                break

        if not 达人详情["当前用户认领状态"]:
            达人详情["当前用户认领状态"] = {"已认领": False}

        数据库日志器.info(f"微信达人详情获取成功 - 达人id: {达人id}")
        return 达人详情

    except Exception as e:
        错误日志器.error(f"获取微信达人详情失败: {str(e)}")
        raise


async def 异步认领微信达人(
    达人id: int, 用户id: int, 备注: Optional[str] = None
) -> bool:
    """
    认领微信达人

    参数:
        达人id: 达人的唯一标识ID
        用户id: 用户的唯一标识ID
        备注: 可选的备注信息

    返回:
        认领是否成功
    """
    try:
        # 检查达人是否存在
        检查SQL = "SELECT id FROM 微信达人表 WHERE id = $1"
        达人存在 = await 异步连接池实例.执行查询(检查SQL, [达人id])

        if not 达人存在:
            raise ValueError(f"微信达人 {达人id} 不存在")

        # 检查是否已经认领
        认领检查SQL = """
        SELECT id FROM 用户达人关联表
        WHERE 用户id = $1 AND 达人id = $2 AND 状态 = 1 AND 平台 = '微信'
        """
        已认领 = await 异步连接池实例.执行查询(认领检查SQL, [用户id, 达人id])

        if 已认领:
            raise ValueError(f"用户 {用户id} 已经认领了微信达人 {达人id}")

        # 插入认领记录
        插入SQL = """
        INSERT INTO 用户达人关联表 (用户id, 达人id, 平台, 认领时间, 状态, 备注)
        VALUES ($1, $2, '微信', $3, 1, $4)
        """

        await 异步连接池实例.执行更新(插入SQL, [用户id, 达人id, datetime.now(), 备注])

        数据库日志器.info(f"微信达人认领成功 - 用户id: {用户id}, 达人id: {达人id}")
        return True

    except Exception as e:
        错误日志器.error(f"认领微信达人失败: {str(e)}")
        raise


async def 异步取消认领微信达人(达人id: int, 用户id: int) -> bool:
    """
    取消认领微信达人

    参数:
        达人id: 达人的唯一标识ID
        用户id: 用户的唯一标识ID

    返回:
        取消认领是否成功
    """
    try:
        # 更新认领状态为无效
        更新SQL = """
        UPDATE 用户达人关联表
        SET 状态 = 0
        WHERE 用户id = $1 AND 达人id = $2 AND 平台 = '微信' AND 状态 = 1
        """

        影响行数 = await 异步连接池实例.执行更新(更新SQL, [用户id, 达人id])

        if 影响行数 == 0:
            raise ValueError(f"用户 {用户id} 没有认领微信达人 {达人id}")

        数据库日志器.info(f"取消认领微信达人成功 - 用户id: {用户id}, 达人id: {达人id}")
        return True

    except Exception as e:
        错误日志器.error(f"取消认领微信达人失败: {str(e)}")
        raise


async def 异步获取用户认领微信达人列表(
    用户id: int,
    页码: int = 1,
    每页数量: int = 20,
    排序字段: str = "认领时间",
    排序方式: str = "desc",
    筛选条件: Optional[Dict[str, Any]] = None,
    关键词: Optional[str] = None,
    补充信息筛选: Optional[str] = None,  # 新增：补充信息筛选参数
) -> Dict[str, Any]:
    """
    获取用户认领的微信达人列表，支持分页、排序和筛选

    参数:
        用户id: 用户的ID
        页码: 当前页码，默认为1
        每页数量: 每页显示数量，默认为20
        排序字段: 排序字段，默认为"认领时间"
        排序方式: 排序方式，"asc"升序或"desc"降序，默认为"desc"
        筛选条件: 可选的筛选条件
        关键词: 可选的搜索关键词，搜索达人昵称和finderUsername
        补充信息筛选: 可选的补充信息筛选，"有补充信息"、"无补充信息"或None表示全部

    返回:
        包含分页信息和达人列表的字典
    """
    try:
        # 计算偏移量
        偏移量 = (页码 - 1) * 每页数量

        # 构建查询SQL
        base_sql = """
        SELECT
            wd.id, wd.finderUsername, wd.昵称, wd.头像, wd.内容类型, wd.带货类目,
            wd.粉丝数文本, wd.GMV文本, wd.有无联系方式, wd.创建时间, wd.更新时间,
            wcl.认领时间, wcl.状态, wcl.备注,
            CASE WHEN wc.contact_count > 0 THEN 1 ELSE 0 END as 有联系方式_计算
        FROM 用户达人关联表 wcl
        INNER JOIN 微信达人表 wd ON wcl.达人id = wd.id
        LEFT JOIN (
            SELECT 微信达人id, COUNT(*) as contact_count
            FROM 微信达人联系方式关联表
            GROUP BY 微信达人id
        ) wc ON wd.id = wc.微信达人id
        WHERE wcl.用户id = $1 AND wcl.状态 = 1 AND wcl.平台 = '微信'
        """

        params = [用户id]

        # 添加搜索条件
        if 关键词:
            base_sql += " AND (wd.昵称 LIKE $2 OR wd.finderUsername LIKE $3)"
            search_term = f"%{关键词}%"
            params.extend([search_term, search_term])

        # 添加补充信息筛选条件
        if 补充信息筛选:
            if 补充信息筛选 == "有补充信息":
                # 筛选有补充信息的达人：EXISTS查询用户达人补充信息表
                base_sql += " AND EXISTS (SELECT 1 FROM 用户达人补充信息表 s WHERE s.用户达人关联表id = wcl.id)"
            elif 补充信息筛选 == "无补充信息":
                # 筛选无补充信息的达人：NOT EXISTS查询用户达人补充信息表
                base_sql += " AND NOT EXISTS (SELECT 1 FROM 用户达人补充信息表 s WHERE s.用户达人关联表id = wcl.id)"

        # 处理排序
        排序映射 = {
            "认领时间": "wcl.认领时间",
            "昵称": "wd.昵称",
            "粉丝数": "wd.粉丝数文本",
            "更新时间": "wd.更新时间",
        }

        实际排序字段 = 排序映射.get(排序字段, "wcl.认领时间")
        排序方向 = "DESC" if 排序方式.lower() == "desc" else "ASC"

        # 查询总数 - 构建与主查询相同的WHERE条件
        总数查询SQL = """
            SELECT COUNT(*) as 总数
            FROM 用户达人关联表 wcl
            INNER JOIN 微信达人表 wd ON wcl.达人id = wd.id
            WHERE wcl.用户id = $1 AND wcl.状态 = 1 AND wcl.平台 = '微信'
        """

        if 关键词:
            总数查询SQL += " AND (wd.昵称 LIKE $2 OR wd.finderUsername LIKE $3)"

        # 添加补充信息筛选条件到总数查询
        if 补充信息筛选:
            if 补充信息筛选 == "有补充信息":
                总数查询SQL += " AND EXISTS (SELECT 1 FROM 用户达人补充信息表 s WHERE s.用户达人关联表id = wcl.id)"
            elif 补充信息筛选 == "无补充信息":
                总数查询SQL += " AND NOT EXISTS (SELECT 1 FROM 用户达人补充信息表 s WHERE s.用户达人关联表id = wcl.id)"

        总数结果 = await 异步连接池实例.执行查询(总数查询SQL, params)
        总数 = 总数结果[0]["总数"] if 总数结果 else 0

        # 查询达人列表
        limit_param_index = len(params) + 1
        offset_param_index = len(params) + 2
        达人查询SQL = f"""
            {base_sql}
            ORDER BY {实际排序字段} {排序方向}
            LIMIT ${limit_param_index} OFFSET ${offset_param_index}
        """

        params.extend([每页数量, 偏移量])
        达人列表 = await 异步连接池实例.执行查询(达人查询SQL, params)

        # 处理JSON字段
        for 达人 in 达人列表:
            # 处理微信达人基础JSON字段
            for 字段 in ["内容类型", "带货类目"]:
                if 达人.get(字段) and isinstance(达人[字段], str):
                    try:
                        达人[字段] = json.loads(达人[字段])
                    except (json.JSONDecodeError, TypeError):
                        达人[字段] = []
                elif not 达人.get(字段):
                    达人[字段] = []

            # 处理个人标签JSON字段
            if 达人.get("个人标签") and isinstance(达人["个人标签"], str):
                try:
                    达人["个人标签"] = json.loads(达人["个人标签"])
                except (json.JSONDecodeError, TypeError):
                    达人["个人标签"] = []
            elif not 达人.get("个人标签"):
                达人["个人标签"] = []

        # 计算分页信息
        总页数 = (总数 + 每页数量 - 1) // 每页数量

        数据库日志器.info(
            f"用户认领微信达人列表查询成功 - 用户id: {用户id}, 返回 {len(达人列表)} 条记录"
        )

        return {
            "当前页": 页码,
            "每页数量": 每页数量,
            "总数": 总数,
            "总页数": 总页数,
            "达人列表": 达人列表,
        }

    except Exception as e:
        错误日志器.error(f"获取用户认领微信达人列表失败: {str(e)}")
        raise


async def 异步创建或更新微信达人(finderUsername: str, 达人数据: Dict[str, Any]) -> int:
    """
    通过finderUsername创建或更新微信达人

    参数:
        finderUsername: 微信达人的finderUsername
        达人数据: 达人数据字典

    返回:
        达人id
    """
    try:
        # 检查达人是否已存在
        检查SQL = "SELECT id FROM 微信达人表 WHERE finderUsername = $1"
        现有达人 = await 异步连接池实例.执行查询(检查SQL, [finderUsername])

        if 现有达人:
            # 更新现有达人
            达人id = 现有达人[0]["id"]
            更新SQL = """
            UPDATE 微信达人表
            SET 昵称 = $1, 头像 = $2, 内容类型 = $3, 带货类目 = $4,
                粉丝数文本 = $1, GMV文本 = $2, 更新时间 = $3
            WHERE id = $1
            """

            await 异步连接池实例.执行更新(
                更新SQL,
                [
                    达人数据.get("昵称"),
                    达人数据.get("头像"),
                    json.dumps(达人数据.get("内容类型", [])),
                    json.dumps(达人数据.get("带货类目", [])),
                    达人数据.get("粉丝数文本"),
                    达人数据.get("GMV文本"),
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    达人id,
                ],
            )

            数据库日志器.info(
                f"微信达人更新成功 - finderUsername: {finderUsername}, 达人id: {达人id}"
            )
        else:
            # 创建新达人
            插入SQL = """
            INSERT INTO 微信达人表 (finderUsername, 昵称, 头像, 内容类型, 带货类目,
                                    粉丝数文本, GMV文本, 创建时间, 更新时间)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            """

            达人id = await 异步连接池实例.执行插入(
                插入SQL,
                [
                    finderUsername,
                    达人数据.get("昵称"),
                    达人数据.get("头像"),
                    json.dumps(达人数据.get("内容类型", [])),
                    json.dumps(达人数据.get("带货类目", [])),
                    达人数据.get("粉丝数文本"),
                    达人数据.get("GMV文本"),
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                ],
            )

            数据库日志器.info(
                f"微信达人创建成功 - finderUsername: {finderUsername}, 达人id: {达人id}"
            )

        return 达人id

    except Exception as e:
        错误日志器.error(f"创建或更新微信达人失败: {str(e)}")
        raise
