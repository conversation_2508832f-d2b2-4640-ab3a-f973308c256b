<template>
  <div class="rag-config">
    <div class="section-header">
      <h2><DatabaseOutlined /> RAG配置</h2>
      <p>配置知识库检索增强生成，支持多知识库不同嵌入模型的智能检索</p>
      <div class="feature-highlights">
        <a-space>
          <a-tag color="blue">📚 多知识库支持</a-tag>
          <a-tag color="green">🔍 向量检索</a-tag>
          <a-tag color="orange">🧠 不同嵌入模型</a-tag>
          <a-tag color="purple">⚡ asyncpg优化</a-tag>
        </a-space>
      </div>
    </div>
    
    <a-form layout="vertical" class="config-form">
      <!-- RAG开关 -->
      <a-card title="RAG设置" class="config-card">
        <a-form-item label="启用rag">
          <a-switch
            v-model:checked="localForm.启用rag"
            checked-children="开启"
            un-checked-children="关闭"
            @change="handleRAGToggle"
          />
          <span class="switch-desc">
            {{ localForm.启用rag ? '已启用知识库检索增强' : '使用纯模型对话' }}
          </span>
        </a-form-item>

        <!-- RAG配置内容 -->
        <div v-if="localForm.启用rag" class="rag-settings">
          <!-- 知识库选择 -->
          <a-form-item 
            label="知识库选择" 
            required
            :validate-status="errors.知识库列表 ? 'error' : ''"
            :help="errors.知识库列表"
          >
            <a-select
              v-model:value="localForm.知识库列表"
              mode="multiple"
              placeholder="选择知识库"
              :loading="loading.知识库列表"
              :options="knowledgeBaseOptions"
              show-search
              :filter-option="filterKnowledgeBase"
              @change="handleKnowledgeBaseListChange"
            >
              <template #option="{ label, value, ...kb }">
                <div class="kb-option">
                  <div class="kb-name">{{ label }}</div>
                  <div class="kb-info">
                    <a-space size="small">
                      <a-tag size="small" color="blue">{{ kb.文档数量 || 0 }} 文档</a-tag>
                      <a-tag size="small" :color="kb.状态 === '正常' ? 'green' : 'orange'">
                        {{ kb.状态 }}
                      </a-tag>
                    </a-space>
                  </div>
                </div>
              </template>
            </a-select>
          </a-form-item>

          <!-- 已选知识库信息 -->
          <div v-if="selectedKnowledgeBases.length > 0" class="selected-kb-info">
            <div class="info-title">已选择的知识库：</div>
            <div class="kb-list">
              <div v-for="kb in selectedKnowledgeBases" :key="kb.value" class="kb-item">
                <a-card size="small">
                  <div class="kb-item-content">
                    <div class="kb-item-name">{{ kb.label }}</div>
                    <div class="kb-item-stats">
                      <a-space size="small">
                        <span>{{ kb.文档数量 || 0 }} 文档</span>
                        <a-tag size="small" :color="kb.状态 === '正常' ? 'green' : 'orange'">
                          {{ kb.状态 }}
                        </a-tag>
                      </a-space>
                    </div>
                  </div>
                </a-card>
              </div>
            </div>
          </div>

          <!-- 检索策略 -->
          <a-form-item label="检索策略">
            <a-radio-group v-model:value="localForm.检索策略" @change="handleRAGConfigChange">
              <a-radio value="similarity">语义相似度</a-radio>
              <a-radio value="keyword">关键词匹配</a-radio>
              <a-radio value="mixed">混合检索</a-radio>
            </a-radio-group>
            <div class="strategy-desc">
              {{ getStrategyDesc(localForm.检索策略) }}
            </div>
          </a-form-item>

          <!-- 嵌入模型 -->
          <a-form-item label="嵌入模型">
            <a-select
              v-model:value="localForm.嵌入模型id"
              placeholder="选择嵌入模型"
              :options="embeddingModelOptions"
              allow-clear
              @change="handleFormChange"
            />
          </a-form-item>

          <!-- 检索参数 -->
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="相似度阈值">
                <a-slider
                  v-model:value="localForm.相似度阈值"
                  :min="0"
                  :max="1"
                  :step="0.1"
                  :marks="{ 0: '0', 0.5: '0.5', 1: '1' }"
                  @change="handleRAGConfigChange"
                />
                <div class="param-desc">
                  阈值越高，检索结果越精确
                </div>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="最大检索数量">
                <a-input-number
                  v-model:value="localForm.最大检索数量"
                  :min="1"
                  :max="20"
                  @change="handleRAGConfigChange"
                />
                <div class="param-desc">
                  建议3-10个结果
                </div>
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 查询优化 -->
          <a-collapse ghost>
            <a-collapse-panel key="query-optimization" header="查询优化设置">
              <a-form-item label="启用查询优化">
                <a-switch
                  v-model:checked="localForm.查询优化配置.启用查询优化"
                  @change="handleRAGConfigChange"
                />
                <span class="switch-desc">
                  自动优化用户查询以提高检索效果
                </span>
              </a-form-item>

              <div v-if="localForm.查询优化配置.启用查询优化">
                <a-form-item label="优化策略">
                  <a-select
                    v-model:value="localForm.查询优化配置.查询优化策略"
                    @change="handleRAGConfigChange"
                  >
                    <a-select-option value="rewrite">查询重写</a-select-option>
                    <a-select-option value="expand">查询扩展</a-select-option>
                    <a-select-option value="decompose">查询分解</a-select-option>
                  </a-select>
                </a-form-item>

                <a-form-item label="优化模型">
                  <a-select
                    v-model:value="localForm.查询优化配置.查询优化模型id"
                    placeholder="选择查询优化模型"
                    :options="modelOptions"
                    @change="handleRAGConfigChange"
                  />
                </a-form-item>
              </div>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </a-card>

      <!-- RAG测试 - 已移除，请使用右侧测试面板进行完整的用户对话测试 -->
      <a-card v-if="localForm.启用rag" title="检索配置说明" class="config-card">
        <a-alert
          message="RAG检索测试"
          description="RAG检索功能已集成到右侧的用户对话测试面板中。保存智能体后，可以在右侧测试面板中进行完整的RAG增强对话测试。"
          type="info"
          show-icon
        />
        <div class="config-summary">
          <h4>当前配置摘要：</h4>
          <ul>
            <li><strong>知识库数量：</strong>{{ localForm.知识库列表?.length || 0 }} 个</li>
            <li><strong>检索策略：</strong>{{ localForm.检索策略 || '未设置' }}</li>
            <li><strong>嵌入模型：</strong>{{ 选中的嵌入模型名称 || '未选择' }}</li>
            <li><strong>相似度阈值：</strong>{{ localForm.相似度阈值 || 0.7 }}</li>
            <li><strong>最大检索数量：</strong>{{ localForm.最大检索数量 || 5 }}</li>
          </ul>
        </div>
      </a-card>
    </a-form>
  </div>
</template>

<script setup>
import { DatabaseOutlined } from '@ant-design/icons-vue'
import { computed, watch, ref } from 'vue'
import { message } from 'ant-design-vue'
import adminLangchainService from '@/services/adminLangchainService'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  agentId: {
    type: [Number, String],
    default: null
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  knowledgeBaseOptions: {
    type: Array,
    default: () => []
  },
  embeddingModelOptions: {
    type: Array,
    default: () => []
  },
  modelOptions: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'validate', 'change'])

// 本地表单数据
const localForm = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 已选择的知识库
const selectedKnowledgeBases = computed(() => {
  if (!localForm.value.知识库列表?.length) {
    console.log('🔍 RAG配置 - 没有选中的知识库')
    return []
  }

  console.log('🔍 RAG配置 - 当前选中的知识库ID列表:', localForm.value.知识库列表)
  console.log('🔍 RAG配置 - 可用知识库选项:', props.knowledgeBaseOptions)

  const selected = props.knowledgeBaseOptions.filter(kb => {
    // 确保ID类型匹配
    const kbId = Number(kb.value)
    const isSelected = localForm.value.知识库列表.some(id => Number(id) === kbId)
    console.log(`🔍 RAG配置 - 知识库 ${kb.label}(ID:${kbId}) 是否选中:`, isSelected)
    return isSelected
  })

  console.log('🔍 RAG配置 - 最终选中的知识库:', selected)
  return selected
})

// 选中的嵌入模型名称
const 选中的嵌入模型名称 = computed(() => {
  if (!localForm.value.嵌入模型id) return null

  const 选中的模型 = props.embeddingModelOptions.find(model =>
    model.value === localForm.value.嵌入模型id
  )

  return 选中的模型?.模型名称 || 选中的模型?.label || null
})



// 获取策略描述
const getStrategyDesc = (strategy) => {
  const descriptions = {
    'similarity': '基于语义相似度进行检索，适合概念性查询',
    'keyword': '基于关键词匹配进行检索，适合精确查询',
    'mixed': '结合语义和关键词检索，平衡精确性和召回率'
  }
  return descriptions[strategy] || ''
}

// 过滤知识库
const filterKnowledgeBase = (input, option) => {
  const searchText = input.toLowerCase()
  return option.label.toLowerCase().includes(searchText)
}



// 处理RAG开关
const handleRAGToggle = (enabled) => {
  if (!enabled) {
    // 关闭RAG时清空相关配置
    const newFormData = { ...localForm.value }
    newFormData.知识库列表 = []
    newFormData.嵌入模型id = null
    localForm.value = newFormData
  }
  handleFormChange()
}

// 存储上一次的知识库列表，用于比较变化
const previousKnowledgeBaseList = ref([])

// 防抖定时器
let updateConfigTimer = null

// 处理表单变化
const handleFormChange = () => {
  emit('change', localForm.value)
}

// 处理知识库列表选择变化
const handleKnowledgeBaseListChange = async (newValue) => {
  const oldValue = previousKnowledgeBaseList.value

  // 更新本地状态
  previousKnowledgeBaseList.value = [...(newValue || [])]

  // 如果是初始化加载，不触发API调用
  if (oldValue.length === 0 && newValue && newValue.length > 0) {
    console.log('🔍 RAG配置 - 初始化知识库列表，跳过API调用')
    handleFormChange()
    return
  }

  // 处理知识库关联的增删
  await handleKnowledgeBaseChange(newValue || [], oldValue)

  // 触发表单变化事件
  handleFormChange()
}

// 处理RAG配置参数变化
const handleRAGConfigChange = () => {
  // 触发表单变化事件
  handleFormChange()

  // 如果有关联的知识库，使用防抖机制更新它们的配置
  if (localForm.value.知识库列表?.length > 0) {
    // 清除之前的定时器
    if (updateConfigTimer) {
      clearTimeout(updateConfigTimer)
    }

    // 设置新的定时器，500ms后执行更新
    updateConfigTimer = setTimeout(async () => {
      await updateAllKnowledgeBaseConfigs()
    }, 500)
  }
}

// 处理知识库列表变化
const handleKnowledgeBaseChange = async (newKnowledgeBaseList, oldKnowledgeBaseList) => {
  if (!props.agentId || props.agentId === 'undefined' || !Array.isArray(newKnowledgeBaseList) || !Array.isArray(oldKnowledgeBaseList)) {
    console.log('🔍 RAG配置 - 跳过知识库关联API调用:', { agentId: props.agentId, 原因: '智能体ID无效或为新建模式' })
    return
  }

  console.log('🔍 RAG配置 - 知识库列表变化:', {
    新列表: newKnowledgeBaseList,
    旧列表: oldKnowledgeBaseList,
    智能体ID: props.agentId
  })

  // 找出新增的知识库
  const 新增知识库 = newKnowledgeBaseList.filter(id => !oldKnowledgeBaseList.includes(id))

  // 找出删除的知识库
  const 删除知识库 = oldKnowledgeBaseList.filter(id => !newKnowledgeBaseList.includes(id))

  try {
    // 处理新增的知识库关联
    for (const 知识库id of 新增知识库) {
      console.log('➕ 添加知识库关联:', { 智能体id: props.agentId, 知识库id })

      const 关联数据 = {
        智能体id: Number(props.agentId),
        知识库id: Number(知识库id),
        权重: 1.0,
        检索策略: localForm.value.检索策略 || 'similarity',
        最大检索数量: localForm.value.最大检索数量 || 5,
        相似度阈值: localForm.value.相似度阈值 || 0.7,
        查询优化配置: localForm.value.查询优化配置 || {
          启用: false,
          策略: null,
          模型id: null,
          提示词: null
        }
      }

      await adminLangchainService.addAgentKnowledgeBaseAssociation(关联数据)
      message.success(`成功添加知识库关联`)
    }

    // 处理删除的知识库关联
    for (const 知识库id of 删除知识库) {
      console.log('➖ 删除知识库关联:', { 智能体id: props.agentId, 知识库id })

      const 删除数据 = {
        智能体id: Number(props.agentId),
        知识库id: Number(知识库id)
      }

      await adminLangchainService.removeAgentKnowledgeBaseAssociation(删除数据)
      message.success(`成功删除知识库关联`)
    }

  } catch (error) {
    console.error('❌ 处理知识库关联变化失败:', error)
    message.error(`处理知识库关联失败: ${error.message}`)

    // 发生错误时，恢复到之前的状态
    localForm.value.知识库列表 = [...oldKnowledgeBaseList]
  }
}

// 更新所有关联知识库的RAG配置
const updateAllKnowledgeBaseConfigs = async () => {
  if (!props.agentId || props.agentId === 'undefined' || !localForm.value.知识库列表?.length) {
    console.log('🔍 RAG配置 - 跳过RAG配置更新:', { agentId: props.agentId, 原因: '智能体ID无效或无关联知识库' })
    return
  }

  console.log('🔄 更新所有知识库关联的RAG配置')

  try {
    for (const 知识库id of localForm.value.知识库列表) {
      const 更新数据 = {
        智能体id: Number(props.agentId),
        知识库id: Number(知识库id),
        检索策略: localForm.value.检索策略,
        最大检索数量: localForm.value.最大检索数量,
        相似度阈值: localForm.value.相似度阈值,
        查询优化配置: localForm.value.查询优化配置
      }

      await adminLangchainService.updateAgentKnowledgeBaseAssociation(更新数据)
    }

    console.log('✅ 所有知识库关联的RAG配置更新成功')
  } catch (error) {
    console.error('❌ 更新知识库关联RAG配置失败:', error)
    message.error(`更新RAG配置失败: ${error.message}`)
  }
}



// 监听知识库列表变化，清除验证错误
watch(() => localForm.value.知识库列表, () => {
  if (props.errors.知识库列表) {
    emit('validate', { 知识库列表: null })
  }
}, { immediate: true })

// 初始化previousKnowledgeBaseList
watch(() => localForm.value.知识库列表, (newValue) => {
  if (previousKnowledgeBaseList.value.length === 0 && newValue && newValue.length > 0) {
    previousKnowledgeBaseList.value = [...newValue]
    console.log('🔍 RAG配置 - 初始化previousKnowledgeBaseList:', previousKnowledgeBaseList.value)
  }
}, { immediate: true })

// 监听启用rag状态变化
watch(() => localForm.value.启用rag, (newValue, oldValue) => {
  console.log('🔍 RAGConfig - 启用rag状态变化:', {
    旧值: oldValue,
    新值: newValue,
    类型: typeof newValue
  })
}, { immediate: true })



// 初始化查询优化配置
if (!localForm.value.查询优化配置) {
  localForm.value.查询优化配置 = {
    启用查询优化: false,
    查询优化策略: 'rewrite',
    查询优化模型id: null,
    查询优化提示词: ''
  }
}
</script>

<style scoped>
.rag-config {
  padding: 16px;
}

.section-header {
  margin-bottom: 24px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.config-form {
  max-width: none;
  width: 100%;
}

.config-card {
  margin-bottom: 16px;
}

.switch-desc {
  margin-left: 12px;
  color: #8c8c8c;
  font-size: 12px;
}

.rag-settings {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.kb-option {
  padding: 4px 0;
}

.kb-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.kb-info {
  font-size: 12px;
}

.selected-kb-info {
  margin: 16px 0;
}

.info-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 8px;
}

.kb-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 8px;
}

.kb-item-content {
  padding: 4px;
}

.kb-item-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.kb-item-stats {
  font-size: 12px;
  color: #8c8c8c;
}

.strategy-desc {
  margin-top: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

.param-desc {
  margin-top: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

:deep(.ant-collapse-ghost .ant-collapse-item) {
  border-bottom: none;
}

:deep(.ant-collapse-ghost .ant-collapse-content) {
  background: transparent;
}

.config-summary {
  margin-top: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.config-summary h4 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.config-summary ul {
  margin: 0;
  padding-left: 16px;
}

.config-summary li {
  margin-bottom: 4px;
  color: #595959;
  font-size: 13px;
}
</style>
