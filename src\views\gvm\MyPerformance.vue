<template>
  <div class="my-performance">
    <!-- 时间筛选和操作栏 -->
    <div class="performance-toolbar">
      <div class="toolbar-left">
        <a-radio-group 
          v-model:value="selectedTimeRange" 
          @change="handleTimeRangeChange"
          button-style="solid"
          size="large"
        >
          <a-radio-button 
            v-for="option in timeRangeOptions" 
            :key="option.value" 
            :value="option.value"
          >
            {{ option.label }}
          </a-radio-button>
        </a-radio-group>
        
        <a-range-picker
          v-if="selectedTimeRange === 'custom'"
          v-model:value="customDateRange"
          @change="handleCustomDateChange"
          class="custom-date-picker"
          size="large"
        />
      </div>
      
      <div class="toolbar-right">
        <a-button 
          type="primary" 
          :icon="h(DownloadOutlined)"
          @click="handleExport"
          :loading="exportLoading"
          size="large"
        >
          导出数据
        </a-button>
        
        <a-button 
          :icon="h(SettingOutlined)"
          @click="showSettings = true"
          size="large"
        >
          设置
        </a-button>
      </div>
    </div>

    <!-- 核心指标卡片区域 -->
    <div class="metrics-cards">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :lg="6">
          <MetricCard
            title="总销售额"
            :value="performanceData.总销售额"
            :growth="salesGrowth"
            icon="DollarOutlined"
            color="#1890ff"
            :loading="loading"
            @click="handleMetricClick('sales')"
          />
        </a-col>
        
        <a-col :xs="24" :sm="12" :lg="6">
          <MetricCard
            title="订单数量"
            :value="performanceData.订单数量"
            :growth="orderGrowth"
            icon="ShoppingOutlined"
            color="#52c41a"
            :loading="loading"
            suffix="单"
            @click="handleMetricClick('orders')"
          />
        </a-col>
        
        <a-col :xs="24" :sm="12" :lg="6">
          <MetricCard
            title="平均客单价"
            :value="performanceData.平均客单价"
            :growth="avgOrderGrowth"
            icon="CalculatorOutlined"
            color="#faad14"
            :loading="loading"
            @click="handleMetricClick('avg-order')"
          />
        </a-col>
        
        <a-col :xs="24" :sm="12" :lg="6">
          <MetricCard
            title="佣金收入"
            :value="performanceData.实际佣金"
            :growth="commissionGrowth"
            icon="WalletOutlined"
            color="#722ed1"
            :loading="loading"
            @click="handleMetricClick('commission')"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 图表分析区域 -->
    <div class="charts-section">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :lg="12">
          <a-card title="销售趋势分析" class="chart-card">
            <template #extra>
              <a-radio-group 
                v-model:value="trendChartType" 
                size="small"
                @change="handleTrendChartTypeChange"
              >
                <a-radio-button value="line">折线图</a-radio-button>
                <a-radio-button value="bar">柱状图</a-radio-button>
              </a-radio-group>
            </template>
            
            <SalesTrendChart
              :data="trendData"
              :type="trendChartType"
              :loading="loading"
              height="300px"
            />
          </a-card>
        </a-col>
        
        <a-col :xs="24" :lg="12">
          <a-card title="佣金收益分析" class="chart-card">
            <CommissionAnalysisChart
              :data="commissionData"
              :loading="loading"
              height="300px"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 详细数据表格 -->
    <div class="data-table-section">
      <a-card title="详细业绩数据" class="table-card">
        <template #extra>
          <a-space>
            <a-input-search
              v-model:value="searchKeyword"
              placeholder="搜索数据..."
              @search="handleSearch"
              style="width: 200px"
            />
            <a-button 
              :icon="h(ReloadOutlined)"
              @click="refreshData"
              :loading="loading"
            >
              刷新
            </a-button>
          </a-space>
        </template>
        
        <PerformanceDataTable
          :data="tableData"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
        />
      </a-card>
    </div>

    <!-- 设置弹窗 - 暂时移除 -->
    <!-- <PerformanceSettings
      v-model:visible="showSettings"
      :settings="userSettings"
      @save="handleSettingsSave"
    /> -->
  </div>
</template>

<script setup>
import {
    DownloadOutlined,
    ReloadOutlined,
    SettingOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, h, onMounted, reactive, ref } from 'vue'
import CommissionAnalysisChart from '../../components/gvm/CommissionAnalysisChart.vue'
import MetricCard from '../../components/gvm/MetricCard.vue'
import PerformanceDataTable from '../../components/gvm/PerformanceDataTable.vue'
import SalesTrendChart from '../../components/gvm/SalesTrendChart.vue'
import gvmService from '../../services/gvmService'

defineOptions({
  name: 'MyPerformance'
})

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const showSettings = ref(false)
const selectedTimeRange = ref('30d')
const customDateRange = ref([])
const searchKeyword = ref('')
const trendChartType = ref('line')

// 缓存和防抖
const dataCache = new Map()
let loadDataTimer = null

// 业绩数据
const performanceData = reactive({
  总销售额: 0,
  订单数量: 0,
  平均客单价: 0,
  实际佣金: 0,
  预估佣金: 0,
  合作店铺数: 0,
  关联达人数: 0
})

// 图表数据
const trendData = ref([])
const commissionData = ref([])
const tableData = ref([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`
})

// 用户设置
const userSettings = reactive({
  defaultTimeRange: '30d',
  showGrowthRate: true,
  chartTheme: 'light'
})

// 时间范围选项
const timeRangeOptions = computed(() => gvmService.getTimeRangeOptions())

// 增长率数据
const salesGrowth = ref({ rate: 0, trend: 'stable', display: '0%' })
const orderGrowth = ref({ rate: 0, trend: 'stable', display: '0%' })
const avgOrderGrowth = ref({ rate: 0, trend: 'stable', display: '0%' })
const commissionGrowth = ref({ rate: 0, trend: 'stable', display: '0%' })

// 防抖加载数据
const loadDataDebounced = () => {
  if (loadDataTimer) {
    clearTimeout(loadDataTimer)
  }

  loadDataTimer = setTimeout(() => {
    loadData()
  }, 300)
}

// 加载数据
const loadData = async () => {
  const params = {
    时间范围: selectedTimeRange.value
  }

  if (selectedTimeRange.value === 'custom' && customDateRange.value?.length === 2) {
    params.开始时间 = customDateRange.value[0].format('YYYY-MM-DD')
    params.结束时间 = customDateRange.value[1].format('YYYY-MM-DD')
  }

  // 检查缓存
  const cacheKey = JSON.stringify(params)
  if (dataCache.has(cacheKey)) {
    const cachedData = dataCache.get(cacheKey)
    Object.assign(performanceData, cachedData.统计数据)
    generateChartData()
    return
  }

  loading.value = true
  try {
    const response = await gvmService.getMyStats(params)

    if (response.status === 'success') {
      const data = response.data.统计数据

      if (!data || Object.keys(data).length === 0) {
        message.warning('暂无业绩数据')
        const defaultData = {
          总销售额: 0, 订单数量: 0, 平均客单价: 0, 预估佣金: 0,
          实际佣金: 0, 合作店铺数: 0, 关联达人数: 0, 佣金率: 0
        }
        Object.assign(performanceData, defaultData)
      } else {
        Object.assign(performanceData, data)
        // 缓存数据（5分钟过期）
        dataCache.set(cacheKey, { 统计数据: data, timestamp: Date.now() })
      }

      await loadGrowthData()
      generateChartData()
    } else {
      message.error(response.message || '获取业绩数据失败')
    }
  } catch (error) {
    message.error('加载数据失败，请重试')
    console.error('加载业绩数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载增长率数据
const loadGrowthData = async () => {
  try {
    // 获取统计概览数据来计算增长率
    const summaryResponse = await gvmService.getStatsSummary()

    if (summaryResponse.status === 'success') {
      const summaryData = summaryResponse.data

      // 根据当前时间范围选择对比数据
      let previousData = {}
      if (selectedTimeRange.value === '1d') {
        // 今日对比昨日（使用本周数据作为近似）
        previousData = summaryData.本周 || {}
      } else if (selectedTimeRange.value === '7d') {
        // 本周对比上周（使用本月数据作为近似）
        previousData = summaryData.本月 || {}
      } else {
        // 其他情况使用本周数据作为对比
        previousData = summaryData.本周 || {}
      }

      // 计算增长率
      const growthRates = gvmService.calculateGrowthRates(performanceData, previousData)

      // 更新增长率数据
      salesGrowth.value = growthRates.总销售额 || { rate: 0, trend: 'stable', display: '0%' }
      orderGrowth.value = growthRates.订单数量 || { rate: 0, trend: 'stable', display: '0%' }
      avgOrderGrowth.value = growthRates.平均客单价 || { rate: 0, trend: 'stable', display: '0%' }
      commissionGrowth.value = growthRates.实际佣金 || { rate: 0, trend: 'stable', display: '0%' }
    }
  } catch (error) {
    console.error('加载增长率数据失败:', error)
    // 增长率加载失败不影响主要功能，使用默认值
  }
}

// 生成图表数据
const generateChartData = () => {
  // 设置佣金分析数据
  commissionData.value = {
    预估佣金: performanceData.预估佣金,
    实际佣金: performanceData.实际佣金
  }

  // 模拟趋势数据（实际项目中应该从后端获取时间序列数据）
  const days = 30
  trendData.value = Array.from({ length: days }, (_, i) => ({
    date: new Date(Date.now() - (days - i - 1) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    sales: Math.floor(Math.random() * 20000) + 10000,
    orders: Math.floor(Math.random() * 100) + 20
  }))

  // 模拟表格数据（实际项目中应该从后端获取详细数据）
  tableData.value = trendData.value.map((item, index) => ({
    id: index + 1,
    date: item.date,
    sales: item.sales,
    orders: item.orders,
    avgOrder: Math.floor(item.sales / item.orders),
    estimatedCommission: Math.floor(item.sales * 0.1),
    actualCommission: Math.floor(item.sales * 0.08),
    commissionRate: Math.floor(Math.random() * 20) + 5,
    stores: Math.floor(Math.random() * 5) + 1,
    talents: Math.floor(Math.random() * 3) + 1
  }))

  pagination.total = tableData.value.length
}

// 事件处理
const handleTimeRangeChange = () => {
  loadDataDebounced()
}

const handleCustomDateChange = () => {
  if (customDateRange.value?.length === 2) {
    loadDataDebounced()
  }
}

const handleMetricClick = (type) => {
  console.log('点击指标:', type)
}

const handleTrendChartTypeChange = () => {
  // 图表类型已通过v-model自动更新
}

const handleExport = async () => {
  exportLoading.value = true
  try {
    // 实现数据导出功能
    message.success('数据导出成功')
  } catch (error) {
    message.error('数据导出失败')
  } finally {
    exportLoading.value = false
  }
}

const handleSearch = () => {
  // 实现搜索功能
  loadData()
}

const refreshData = () => {
  // 清除缓存
  dataCache.clear()
  loadData()
}

// 清理过期缓存
const cleanExpiredCache = () => {
  const now = Date.now()
  const expireTime = 5 * 60 * 1000 // 5分钟

  for (const [key, value] of dataCache.entries()) {
    if (now - value.timestamp > expireTime) {
      dataCache.delete(key)
    }
  }
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const handleSettingsSave = (settings) => {
  Object.assign(userSettings, settings)
  message.success('设置保存成功')
}

// 生命周期
onMounted(() => {
  loadData()

  // 定时清理过期缓存
  setInterval(cleanExpiredCache, 60000) // 每分钟清理一次
})
</script>

<style scoped>
.my-performance {
  padding: 0;
}

.performance-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.custom-date-picker {
  width: 240px;
}

.metrics-cards {
  margin-bottom: 24px;
}

.charts-section {
  margin-bottom: 24px;
}

.chart-card,
.table-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
}

.chart-card :deep(.ant-card-head),
.table-card :deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
}

.data-table-section {
  margin-bottom: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .performance-toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
  
  .custom-date-picker {
    width: 100%;
  }
}
</style>
