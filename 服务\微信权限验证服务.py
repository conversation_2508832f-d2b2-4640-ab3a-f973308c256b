"""
微信权限验证服务

专门负责微信相关操作的权限验证，确保用户只能操作属于自己的数据
实现路由层权限验证，支持三层架构分离
"""

from typing import Dict, Any, Optional
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 应用日志器 as 系统日志器, 错误日志器
import 状态


class 微信权限验证服务:
    """
    微信权限验证服务类
    
    负责所有微信相关操作的权限验证
    确保用户只能访问和操作属于自己的数据
    """

    @staticmethod
    async def 验证微信添加记录归属权限(用户id: int, 记录id: int) -> Dict[str, Any]:
        """
        验证微信添加记录是否属于指定用户
        
        Args:
            用户id: 当前登录用户ID
            记录id: 要验证的记录id
            
        Returns:
            Dict[str, Any]: 验证结果
            {
                "status": 状态码,
                "message": 消息,
                "data": {
                    "has_permission": bool,  # 是否有权限
                    "record_exists": bool,   # 记录是否存在
                    "record_info": dict      # 记录基本信息（如果有权限）
                }
            }
        """
        try:
            async with 异步连接池实例.获取连接() as 异步连接:
                # 查询记录归属和基本信息
                验证SQL = """
                    SELECT 
                        r.id,
                        r.用户表id,
                        r.我方添加的微信信息表id,
                        r.好友请求状态,
                        r.创建时间
                    FROM 用户_联系方式_微信添加记录表 r
                    WHERE r.id = $1
                """
                
                记录信息 = await 异步连接.fetchrow(验证SQL, 记录id)
                
                if not 记录信息:
                    return {
                        "status": 状态.通用.成功,
                        "message": "权限验证完成",
                        "data": {
                            "has_permission": False,
                            "record_exists": False,
                            "record_info": None
                        }
                    }
                
                # 检查记录是否属于当前用户
                has_permission = 记录信息["用户表id"] == 用户id
                
                record_info = None
                if has_permission:
                    record_info = {
                        "记录id": 记录信息["id"],
                        "微信信息表id": 记录信息["我方添加的微信信息表id"],
                        "好友请求状态": 记录信息["好友请求状态"],
                        "创建时间": 记录信息["创建时间"]
                    }
                
                系统日志器.info(
                    f"微信添加记录权限验证: 用户id={用户id}, 记录id={记录id}, "
                    f"记录存在={True}, 有权限={has_permission}"
                )
                
                return {
                    "status": 状态.通用.成功,
                    "message": "权限验证完成",
                    "data": {
                        "has_permission": has_permission,
                        "record_exists": True,
                        "record_info": record_info
                    }
                }
                
        except Exception as e:
            错误日志器.error(f"微信添加记录权限验证异常: 用户id={用户id}, 记录id={记录id}, 错误={str(e)}")
            return {
                "status": 状态.通用.服务器错误,
                "message": f"权限验证失败: {str(e)}",
                "data": {
                    "has_permission": False,
                    "record_exists": False,
                    "record_info": None
                }
            }

    @staticmethod
    async def 验证微信配置归属权限(用户id: int, 配置id: int) -> Dict[str, Any]:
        """
        验证微信配置是否属于指定用户
        
        Args:
            用户id: 当前登录用户ID
            配置id: 要验证的配置ID
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            async with 异步连接池实例.获取连接() as 异步连接:
                验证SQL = """
                    SELECT 
                        c.id,
                        c.用户id,
                        c.微信信息表id,
                        c.是否启用
                    FROM 用户微信添加配置表 c
                    WHERE c.id = $1
                """
                
                配置信息 = await 异步连接.fetchrow(验证SQL, 配置id)
                
                if not 配置信息:
                    return {
                        "status": 状态.通用.成功,
                        "message": "权限验证完成",
                        "data": {
                            "has_permission": False,
                            "record_exists": False,
                            "record_info": None
                        }
                    }
                
                has_permission = 配置信息["用户id"] == 用户id
                
                record_info = None
                if has_permission:
                    record_info = {
                        "配置id": 配置信息["id"],
                        "微信信息表id": 配置信息["微信信息表id"],
                        "是否启用": 配置信息["是否启用"]
                    }
                
                系统日志器.info(
                    f"微信配置权限验证: 用户id={用户id}, 配置id={配置id}, "
                    f"配置存在={True}, 有权限={has_permission}"
                )
                
                return {
                    "status": 状态.通用.成功,
                    "message": "权限验证完成",
                    "data": {
                        "has_permission": has_permission,
                        "record_exists": True,
                        "record_info": record_info
                    }
                }
                
        except Exception as e:
            错误日志器.error(f"微信配置权限验证异常: 用户id={用户id}, 配置id={配置id}, 错误={str(e)}")
            return {
                "status": 状态.通用.服务器错误,
                "message": f"权限验证失败: {str(e)}",
                "data": {
                    "has_permission": False,
                    "record_exists": False,
                    "record_info": None
                }
            }

    @staticmethod
    async def 验证微信账号归属权限(用户id: int, 微信信息表id: int) -> Dict[str, Any]:
        """
        验证微信账号是否属于指定用户
        
        Args:
            用户id: 当前登录用户ID
            微信信息表id: 要验证的微信账号ID
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            async with 异步连接池实例.获取连接() as 异步连接:
                验证SQL = """
                    SELECT 
                        w.id,
                        w.用户id,
                        w.微信号,
                        w.昵称
                    FROM 微信信息表 w
                    WHERE w.id = $1
                """
                
                微信信息 = await 异步连接.fetchrow(验证SQL, 微信信息表id)
                
                if not 微信信息:
                    return {
                        "status": 状态.通用.成功,
                        "message": "权限验证完成",
                        "data": {
                            "has_permission": False,
                            "record_exists": False,
                            "record_info": None
                        }
                    }
                
                has_permission = 微信信息["用户id"] == 用户id
                
                record_info = None
                if has_permission:
                    record_info = {
                        "微信信息表id": 微信信息["id"],
                        "微信号": 微信信息["微信号"],
                        "昵称": 微信信息["昵称"]
                    }
                
                系统日志器.info(
                    f"微信账号权限验证: 用户id={用户id}, 微信信息表id={微信信息表id}, "
                    f"账号存在={True}, 有权限={has_permission}"
                )
                
                return {
                    "status": 状态.通用.成功,
                    "message": "权限验证完成",
                    "data": {
                        "has_permission": has_permission,
                        "record_exists": True,
                        "record_info": record_info
                    }
                }
                
        except Exception as e:
            错误日志器.error(f"微信账号权限验证异常: 用户id={用户id}, 微信信息表id={微信信息表id}, 错误={str(e)}")
            return {
                "status": 状态.通用.服务器错误,
                "message": f"权限验证失败: {str(e)}",
                "data": {
                    "has_permission": False,
                    "record_exists": False,
                    "record_info": None
                }
            }


# 创建权限验证服务实例
微信权限验证服务实例 = 微信权限验证服务()
