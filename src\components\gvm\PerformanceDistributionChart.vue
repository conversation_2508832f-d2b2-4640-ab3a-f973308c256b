<template>
  <div class="performance-distribution-chart">
    <div 
      ref="chartContainer" 
      :style="{ height: height }"
      class="chart-container"
    ></div>
    
    <div v-if="loading" class="chart-loading">
      <a-spin size="large" />
    </div>
    
    <div v-if="!loading && (!data || data.length === 0)" class="chart-empty">
      <a-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import gvmService from '../../services/gvmService'

defineOptions({
  name: 'PerformanceDistributionChart'
})

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    default: 'individual', // 'individual' | 'team'
    validator: (value) => ['individual', 'team'].includes(value)
  },
  height: {
    type: String,
    default: '300px'
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const chartContainer = ref(null)
let chartInstance = null

const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || props.loading) return
  
  const colors = gvmService.getChartColors()
  const option = getChartOption(colors)
  
  chartInstance.setOption(option, true)
}

const getChartOption = (colors) => {
  if (!props.data || props.data.length === 0) {
    return {}
  }
  
  // 根据销售额分组统计
  const ranges = [
    { min: 0, max: 10000, label: '0-1万' },
    { min: 10000, max: 50000, label: '1-5万' },
    { min: 50000, max: 100000, label: '5-10万' },
    { min: 100000, max: 200000, label: '10-20万' },
    { min: 200000, max: Infinity, label: '20万+' }
  ]
  
  const distribution = ranges.map(range => {
    const count = props.data.filter(item => 
      item.sales >= range.min && item.sales < range.max
    ).length
    
    return {
      name: range.label,
      value: count,
      range: range
    }
  })
  
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params) => {
        const param = params[0]
        const total = props.data.length
        const percentage = total > 0 ? ((param.value / total) * 100).toFixed(1) : 0
        return `
          <div style="margin-bottom: 4px; font-weight: 600;">${param.name}</div>
          <div style="display: flex; align-items: center; margin-bottom: 2px;">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
            <span>${props.type === 'individual' ? '用户' : '团队'}数量: ${param.value}</span>
          </div>
          <div style="font-size: 12px; color: #666;">
            占比: ${percentage}%
          </div>
        `
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '8%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: distribution.map(item => item.name),
      axisLabel: {
        interval: 0,
        rotate: 0
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: props.type === 'individual' ? '用户数' : '团队数',
      axisLabel: {
        formatter: '{value}'
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [
      {
        name: '分布',
        type: 'bar',
        data: distribution.map((item, index) => ({
          value: item.value,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: colors.primary + '80' },
              { offset: 1, color: colors.primary }
            ])
          }
        })),
        barWidth: '60%',
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          color: '#666',
          fontSize: 12
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: colors.primary + 'CC' },
              { offset: 1, color: colors.primary }
            ])
          }
        },
        animationDuration: 1000,
        animationEasing: 'cubicOut'
      }
    ],
    animation: true
  }
}

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

watch(() => props.data, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

watch(() => props.type, () => {
  nextTick(() => {
    updateChart()
  })
})

watch(() => props.loading, (newLoading) => {
  if (!newLoading) {
    nextTick(() => {
      updateChart()
    })
  }
})

onMounted(() => {
  nextTick(() => {
    initChart()
  })
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})

defineExpose({
  refreshChart: updateChart,
  getChartInstance: () => chartInstance
})
</script>

<style scoped>
.performance-distribution-chart {
  position: relative;
  width: 100%;
}

.chart-container {
  width: 100%;
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.chart-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
</style>
