<template>
  <div class="store-collaboration-chart">
    <div 
      ref="chartContainer" 
      :style="{ height: height }"
      class="chart-container"
    ></div>
    
    <div v-if="loading" class="chart-loading">
      <a-spin size="large" />
    </div>
    
    <div v-if="!loading && (!data || data.length === 0)" class="chart-empty">
      <a-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import gvmService from '../../services/gvmService'

defineOptions({
  name: 'StoreCollaborationChart'
})

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  height: {
    type: String,
    default: '300px'
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['storeClick'])

const chartContainer = ref(null)
let chartInstance = null

const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  updateChart()
  
  chartInstance.on('click', (params) => {
    emit('storeClick', params.data)
  })
}

const updateChart = () => {
  if (!chartInstance || props.loading) return
  
  const colors = gvmService.getChartColors()
  const option = getChartOption(colors)
  
  chartInstance.setOption(option, true)
}

const getChartOption = (colors) => {
  if (!props.data || props.data.length === 0) {
    return {}
  }
  
  // 处理数据：气泡图需要 [x, y, size] 格式
  const bubbleData = props.data.map(store => [
    store.orders || 0,        // x轴：订单数
    store.sales || 0,         // y轴：销售额
    store.collaborationDays || 30, // 气泡大小：合作天数
    store.name,               // 店铺名称
    store
  ])
  
  const maxOrders = Math.max(...props.data.map(item => item.orders || 0))
  const maxSales = Math.max(...props.data.map(item => item.sales || 0))
  
  return {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const store = params.data[4]
        return `
          <div style="margin-bottom: 4px; font-weight: 600;">${store.name}</div>
          <div style="margin-bottom: 2px;">订单数: ${store.orders}单</div>
          <div style="margin-bottom: 2px;">销售额: ${gvmService.formatAmount(store.sales)}</div>
          <div style="margin-bottom: 2px;">合作天数: ${store.collaborationDays}天</div>
          <div style="font-size: 12px; color: #666;">
            佣金率: ${store.commissionRate || 0}% | 状态: ${store.status === 'active' ? '活跃' : '不活跃'}
          </div>
        `
      }
    },
    legend: {
      show: false
    },
    grid: {
      left: '8%',
      right: '4%',
      bottom: '8%',
      top: '8%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '订单数',
      nameLocation: 'middle',
      nameGap: 30,
      max: maxOrders * 1.1,
      axisLabel: {
        formatter: '{value}单'
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '销售额',
      nameLocation: 'middle',
      nameGap: 50,
      max: maxSales * 1.1,
      axisLabel: {
        formatter: (value) => gvmService.formatAmount(value)
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [
      {
        name: '店铺合作',
        type: 'scatter',
        data: bubbleData,
        symbolSize: (data) => {
          // 根据合作天数计算气泡大小，范围在10-50之间
          const days = data[2]
          return Math.max(10, Math.min(50, days / 3))
        },
        itemStyle: {
          color: (params) => {
            // 根据数据索引使用不同颜色
            const colorIndex = params.dataIndex % colors.series.length
            return colors.series[colorIndex]
          },
          opacity: 0.7
        },
        emphasis: {
          itemStyle: {
            opacity: 1,
            borderColor: '#333',
            borderWidth: 2
          }
        },
        label: {
          show: true,
          position: 'top',
          formatter: (params) => params.data[3], // 显示店铺名称
          fontSize: 10,
          color: '#666'
        },
        animationDuration: 1000,
        animationEasing: 'cubicOut'
      }
    ],
    animation: true
  }
}

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

watch(() => props.data, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

watch(() => props.loading, (newLoading) => {
  if (!newLoading) {
    nextTick(() => {
      updateChart()
    })
  }
})

onMounted(() => {
  nextTick(() => {
    initChart()
  })
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})

defineExpose({
  refreshChart: updateChart,
  getChartInstance: () => chartInstance
})
</script>

<style scoped>
.store-collaboration-chart {
  position: relative;
  width: 100%;
}

.chart-container {
  width: 100%;
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.chart-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
</style>
