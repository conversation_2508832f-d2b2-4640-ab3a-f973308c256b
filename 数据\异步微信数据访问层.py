"""
异步微信数据访问层

负责微信相关的数据库操作，包括微信账号权限验证、微信好友信息更新等
遵循三层分离架构中的数据访问层职责
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

import 状态
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 错误日志器


async def 异步数据_验证用户微信账号权限(用户id: int, 微信id: int) -> bool:
    """
    数据层：验证用户对指定微信账号的权限

    Args:
        用户id: 用户标识
        微信id: 微信账号id

    Returns:
        bool: True表示有权限，False表示无权限
    """
    try:
        验证SQL = """
        SELECT 1 FROM 用户微信关联表
        WHERE 用户id = $1 AND 微信id = $2 AND 状态 = 1
        """

        验证结果 = await 异步连接池实例.执行查询(验证SQL, (用户id, 微信id))

        return len(验证结果) > 0

    except Exception as e:
        错误日志器.error(f"数据_验证用户微信账号权限失败: {str(e)}")
        return False


async def 异步数据_查询用户微信关联信息(用户id: int, 微信号id: int) -> Optional[Dict[str, Any]]:
    """
    数据层：查询用户微信关联信息

    Args:
        用户id: 用户id
        微信号id: 微信号id

    Returns:
        Optional[Dict[str, Any]]: 关联信息，如果没有关联则返回None
    """
    try:
        查询SQL = """
        SELECT uwx.id, w.微信号, w.昵称
        FROM 用户微信关联表 uwx
        INNER JOIN 微信信息表 w ON uwx.微信id = w.id
        WHERE uwx.微信id = $1 AND uwx.用户id = $2 AND uwx.状态 = 1
        """

        结果 = await 异步连接池实例.执行查询(查询SQL, (微信号id, 用户id))
        return 结果[0] if 结果 else None

    except Exception as e:
        错误日志器.error(f"查询用户微信关联信息失败: {str(e)}")
        return None


async def 异步数据_查询微信号信息(微信号id: int) -> Optional[Dict[str, Any]]:
    """
    数据层：查询微信号基本信息

    Args:
        微信号id: 微信号id

    Returns:
        Optional[Dict[str, Any]]: 微信号信息，如果不存在则返回None
    """
    try:
        查询SQL = "SELECT id, 微信号, 昵称 FROM 微信信息表 WHERE id = $1"
        结果 = await 异步连接池实例.执行查询(查询SQL, (微信号id,))
        return 结果[0] if 结果 else None

    except Exception as e:
        错误日志器.error(f"查询微信号信息失败: {str(e)}")
        return None


async def 异步数据_获取微信好友下次沟通时间列表(
    我方微信号id: int,
) -> Dict[str, Any]:
    """
    数据层：查询指定微信号的所有好友中下次沟通时间最小的那条记录（包括null值）

    注意：此函数不进行权限验证，权限验证应在服务层完成

    Args:
        我方微信号id: 我方微信号id

    Returns:
        Dict[str, Any]: 包含下次沟通时间最小的好友信息的字典
    """
    try:
        # 构建查询SQL - 查询所有好友中下次沟通时间最小的那条记录（null值优先）
        数据查询 = """
        SELECT
            对方微信号id,
            识别id,
            下次沟通时间
        FROM 微信好友表
        WHERE 我方微信号id = $1
        ORDER BY 下次沟通时间 NULLS FIRST
        LIMIT 1
        """

        # 执行查询
        好友列表 = await 异步连接池实例.执行查询(数据查询, (我方微信号id,))

        if not 好友列表:
            return {
                "status": 状态.通用.不存在,
                "message": "暂无好友记录",
                "data": None,
            }

        return {
            "status": 状态.通用.成功,
            "data": 好友列表[0],  # 返回下次沟通时间最小的好友信息
        }

    except Exception as e:
        错误日志器.error(f"数据_获取微信好友下次沟通时间列表失败: {str(e)}")
        return {
            "status": 状态.通用.数据库错误,
            "message": f"查询微信好友下次沟通时间列表失败: {str(e)}",
            "data": None,
        }


async def 异步数据_检查微信好友记录是否存在(我方微信id: int, 好友识别id: int) -> bool:
    """
    数据层：检查微信好友记录是否存在

    Args:
        我方微信id: 我方微信账号id
        好友识别id: 好友识别id

    Returns:
        bool: True表示记录存在，False表示不存在
    """
    try:
        检查SQL = """
        SELECT COUNT(*) as count FROM 微信好友表
        WHERE 我方微信号id = $1 AND 识别id = $2
        """

        检查结果 = await 异步连接池实例.执行查询(检查SQL, (我方微信id, 好友识别id))

        记录数量 = 检查结果[0]["count"] if 检查结果 else 0

        return 记录数量 > 0

    except Exception as e:
        错误日志器.error(f"数据_检查微信好友记录是否存在失败: {str(e)}")
        return False


async def 异步数据_通过识别id更新微信好友信息(
    我方微信id: int, 好友识别id: int, 更新字段: Dict[str, Any]
) -> Dict[str, Any]:
    """
    数据层：通过识别id更新微信好友信息

    Args:
        我方微信id: 我方微信账号id
        好友识别id: 好友识别id
        更新字段: 需要更新的字段及其值的映射

    Returns:
        Dict[str, Any]: 包含影响行数等操作结果的字典
    """
    try:
        # 1. 先检查记录是否存在
        记录存在 = await 异步数据_检查微信好友记录是否存在(我方微信id, 好友识别id)
        if not 记录存在:
            return {"影响行数": 0, "错误信息": "微信好友记录不存在"}

        # 2. 构建动态更新SQL
        字段映射 = {
            "是否失效": "是否失效",
            "发送请求时间": "发送请求时间",
            "好友入库时间": "好友入库时间",
            "我方最后一条消息发送时间": "我方最后一条消息发送时间",
            "对方最后一条消息发送时间": "对方最后一条消息发送时间",
            "下次沟通时间": "下次沟通时间",
            "备注": "备注",
            "自动对话": "自动对话",
        }

        更新SQL片段 = []
        更新参数 = []
        参数索引 = 1

        for 字段名, 字段值 in 更新字段.items():
            if 字段名 in 字段映射:
                更新SQL片段.append(f"{字段映射[字段名]} = ${参数索引}")
                更新参数.append(字段值)
                参数索引 += 1

        if not 更新SQL片段:
            return {"影响行数": 0, "错误信息": "没有有效的更新字段"}

        # 3. 执行更新操作
        # 添加WHERE条件的参数，使用正确的参数索引
        where参数索引 = len(更新参数) + 1
        更新参数.extend([我方微信id, 好友识别id])
        更新SQL = f"""
        UPDATE 微信好友表
        SET {", ".join(更新SQL片段)}
        WHERE 我方微信号id = ${where参数索引} AND 识别id = ${where参数索引 + 1}
        """

        影响行数 = await 异步连接池实例.执行更新(更新SQL, tuple(更新参数))

        return {"影响行数": 影响行数, "更新字段数": len(更新SQL片段)}

    except Exception as e:
        错误日志器.error(f"数据_通过识别id更新微信好友信息失败: {str(e)}")
        return {"影响行数": 0, "错误信息": f"数据库操作失败: {str(e)}"}


async def 异步数据_获取微信好友详细信息_通过识别id(
    我方微信id: int, 好友识别id: int
) -> Optional[Dict[str, Any]]:
    """
    数据层：通过识别id获取微信好友详细信息

    Args:
        我方微信id: 我方微信账号id
        好友识别id: 好友识别id

    Returns:
        Optional[Dict[str, Any]]: 好友详细信息，如果不存在则返回None
    """
    try:
        查询SQL = """
        SELECT
            我方微信号id,
            对方微信号id,
            识别id,
            是否失效,
            发送请求时间,
            好友入库时间,
            我方最后一条消息发送时间,
            对方最后一条消息发送时间,
            下次沟通时间,
            备注,
            创建时间
        FROM 微信好友表
        WHERE 我方微信号id = $1 AND 识别id = $2
        """

        查询结果 = await 异步连接池实例.执行查询(查询SQL, (我方微信id, 好友识别id))

        if 查询结果:
            return 查询结果[0]

        return None

    except Exception as e:
        错误日志器.error(f"数据_获取微信好友详细信息_通过识别id失败: {str(e)}")
        return None


async def 异步数据_获取用户绑定的微信账号列表(用户id: int) -> list:
    """
    数据层：获取用户绑定的微信账号列表

    Args:
        用户id: 用户标识

    Returns:
        list: 用户绑定的微信账号列表
    """
    try:
        查询SQL = """
        SELECT 
            w.id as 微信id,
            w.微信号,
            w.昵称,
            w.绑定手机号,
            w.微信头像,
            uw.绑定时间,
            uw.状态,
            uw.备注
        FROM 用户微信关联表 uw
        INNER JOIN 微信信息表 w ON uw.微信id = w.id
        WHERE uw.用户id = $1 AND uw.状态 = 1
        ORDER BY uw.绑定时间 DESC
        """

        查询结果 = await 异步连接池实例.执行查询(查询SQL, (用户id,))

        return 查询结果 or []

    except Exception as e:
        错误日志器.error(f"数据_获取用户绑定的微信账号列表失败: {str(e)}")
        return []


async def 异步数据_验证用户微信好友权限(
    用户id: int,
    我方微信号id: int,
    识别id: int,
) -> Dict[str, Any]:
    """
    数据层：验证用户对指定微信好友记录的权限并返回记录信息

    Args:
        用户id: 当前用户id
        我方微信号id: 我方微信号id
        识别id: 识别id，用于定位具体的好友记录

    Returns:
        Dict[str, Any]: 包含权限验证结果和记录信息
    """
    try:
        # 构建查询条件 - 通过我方微信号id和识别id定位记录
        查询条件_列表 = ["uwx.用户id = $1", "uwx.状态 = 1", "f.我方微信号id = $2", "f.识别id = $3"]
        查询参数 = [用户id, 我方微信号id, 识别id]

        查询条件 = " AND ".join(查询条件_列表)

        # 查询记录并验证权限
        查询SQL = f"""
        SELECT
            f.我方微信号id,
            f.对方微信号id,
            f.识别id,
            f.我方最后一条消息发送时间,
            f.对方最后一条消息发送时间,
            f.下次沟通时间,
            f.备注,
            f.创建时间
        FROM 微信好友表 f
        INNER JOIN 用户微信关联表 uwx ON f.我方微信号id = uwx.微信id
        WHERE {查询条件}
        LIMIT 1
        """

        查询结果 = await 异步连接池实例.执行查询(查询SQL, 查询参数)

        if not 查询结果:
            return {
                "status": 状态.用户.权限不足,
                "message": "您没有权限访问此微信好友记录或记录不存在",
                "data": None,
            }

        记录 = 查询结果[0]
        return {
            "status": 状态.通用.成功,
            "message": "权限验证通过",
            "data": {
                "记录信息": dict(记录),
                "查询条件": 查询条件,
                "查询参数": 查询参数,
            },
        }

    except Exception as e:
        错误日志器.error(f"数据_验证用户微信好友权限失败: {str(e)}")
        return {
            "status": 状态.通用.失败,
            "message": f"权限验证失败: {str(e)}",
            "data": None,
        }


async def 异步数据_更新微信好友下次沟通时间(
    查询条件: str, 查询参数: List, 新的下次沟通时间: datetime
) -> Dict[str, Any]:
    """
    数据层：更新微信好友的下次沟通时间

    Args:
        查询条件: SQL查询条件字符串
        查询参数: 查询参数列表
        新的下次沟通时间: 新的下次沟通时间

    Returns:
        Dict[str, Any]: 包含更新结果的响应
    """
    try:
        # 重新构建查询条件，调整参数索引以避免冲突
        # 原查询条件使用 $1, $2, $3...，现在需要调整为 $2, $3, $4...
        # 因为 $1 要用于 SET 下次沟通时间 = $1

        # 将查询条件中的参数索引向后偏移1位
        调整后的查询条件 = 查询条件
        for i in range(len(查询参数), 0, -1):
            调整后的查询条件 = 调整后的查询条件.replace(f"${i}", f"${i + 1}")

        # 执行更新操作
        更新SQL = f"""
        UPDATE 微信好友表 f
        SET 下次沟通时间 = $1
        FROM 用户微信关联表 uwx
        WHERE f.我方微信号id = uwx.微信id
        AND {调整后的查询条件}
        """

        更新参数 = [新的下次沟通时间] + 查询参数
        更新结果 = await 异步连接池实例.执行更新(更新SQL, 更新参数)

        if 更新结果 > 0:
            return {
                "status": 状态.通用.成功,
                "message": "下次沟通时间更新成功",
                "data": {
                    "下次沟通时间": 新的下次沟通时间.strftime("%Y-%m-%d %H:%M:%S")
                    if 新的下次沟通时间
                    else None
                },
            }
        else:
            return {
                "status": 状态.通用.不存在,
                "message": "未找到对应的微信好友记录",
                "data": None,
            }

    except Exception as e:
        错误日志器.error(f"数据_更新微信好友下次沟通时间失败: {str(e)}")
        return {
            "status": 状态.通用.失败,
            "message": f"更新失败: {str(e)}",
            "data": None,
        }


async def 异步数据_查询微信好友消息详情(
    用户id: int, 我方微信号id: int, 识别id: int
) -> Dict[str, Any]:
    """
    数据层：查询微信好友的消息详情信息

    Args:
        用户id: 当前用户id
        我方微信号id: 我方微信号id
        识别id: 微信好友识别id

    Returns:
        Dict[str, Any]: 包含查询结果的响应
    """
    try:
        查询SQL = """
        SELECT
            f.我方微信号id,
            f.对方微信号id,
            f.识别id,
            f.我方最后一条消息发送时间,
            f.对方最后一条消息发送时间,
            f.下次沟通时间,
            f.备注,
            f.创建时间,
            f.好友入库时间,
            f.发送请求时间,
            f.好友通过时间,
            f.自动对话,
            wi_other.微信号 as 对方微信号,
            wi_other.昵称 as 对方微信昵称,
            wi_other.微信头像 as 对方微信头像
        FROM 微信好友表 f
        INNER JOIN 用户微信关联表 uwx ON f.我方微信号id = uwx.微信id
        LEFT JOIN 微信信息表 wi_other ON f.对方微信号id = wi_other.id
        WHERE uwx.用户id = $1
        AND uwx.状态 = 1
        AND f.我方微信号id = $2
        AND f.识别id = $3
        LIMIT 1
        """

        查询结果 = await 异步连接池实例.执行查询(
            查询SQL, (用户id, 我方微信号id, 识别id)
        )

        if not 查询结果:
            return {
                "status": 状态.通用.不存在,
                "message": "未找到对应的微信好友记录",
                "data": None,
            }

        记录 = dict(查询结果[0])
        return {
            "status": 状态.通用.成功,
            "message": "查询微信好友消息详情成功",
            "data": 记录,
        }

    except Exception as e:
        错误日志器.error(f"数据_查询微信好友消息详情失败: {str(e)}")
        return {
            "status": 状态.通用.失败,
            "message": f"查询失败: {str(e)}",
            "data": None,
        }
