<template>
  <div class="talent-detail-panel">
    <!-- 达人基本信息卡片 -->
    <a-card 
      class="talent-basic-card" 
      :bordered="false"
      :loading="loading"
    >
      <!-- 卡片头部：标题栏和更新按钮 -->
      <template #title>
        <div class="card-header-section">
          <div class="header-title">
            <UserOutlined class="title-icon" />
            <span>达人基本信息</span>
          </div>
          <!-- 更新按钮 -->
          <div class="header-actions">
            <a-button
              type="primary"
              size="small"
              :loading="updateLoading"
              @click="handleUpdateTalentData"
              v-if="talent.id || talent.达人id"
            >
              <SyncOutlined />
              更新数据
            </a-button>
          </div>
        </div>
      </template>

      <!-- 达人头部信息 -->
      <div class="talent-header">
        <!-- 达人头像 -->
        <div class="talent-avatar-section">
          <a-avatar 
            :size="80" 
            :src="talent.avatar || talent.头像" 
            :alt="talent.nickname || talent.昵称"
          >
            <template #icon v-if="!talent.avatar && !talent.头像">
              <UserOutlined />
            </template>
          </a-avatar>
          
          <!-- 认领状态标签 -->
          <div class="claim-status-tag">
            <a-tag 
              v-if="talent.已认领 || talent.关联id || talent.当前用户认领状态?.已认领" 
              color="red" 
              size="small"
            >
              <CheckCircleOutlined />
              已认领
            </a-tag>
            <a-tag v-else color="green" size="small">
              <PlusCircleOutlined />
              可认领
            </a-tag>
          </div>
        </div>
        
        <!-- 达人基本信息 -->
        <div class="talent-info-section">
          <div class="talent-name-section">
            <h3 class="talent-name">
              {{ talent.nickname || talent.昵称 || '未知昵称' }}
            </h3>
            <!-- 最近更新时间 -->
            <div class="update-time" v-if="talent.更新时间">
              <ClockCircleOutlined />
              <span>更新于 {{ formatDateTime(talent.更新时间) }}</span>
            </div>
          </div>

          <!-- 个人介绍 -->
          <div class="talent-intro" v-if="talent.introduction || talent.简介">
            <p class="intro-text">{{ talent.introduction || talent.简介 }}</p>
          </div>

          <div class="talent-meta">
            <div class="meta-item">
              <VideoCameraOutlined />
              <span>{{ talent.account_douyin || '抖音号未知' }}</span>
            </div>

            <div class="meta-item" v-if="talent.UID || talent.uid_number">
              <NumberOutlined />
              <span>UID: {{ talent.UID || talent.uid_number }}</span>
            </div>
          </div>
          
          <!-- 快速操作按钮 -->
          <div class="quick-actions">
            <a-button-group size="small">
              <!-- 认领/取消认领按钮 -->
              <a-button 
                v-if="showClaimButton && !isAlreadyClaimed" 
                type="primary" 
                @click="handleClaim"
                :loading="claimLoading"
              >
                <UserAddOutlined />
                认领
              </a-button>
              
              <a-button 
                v-if="isAlreadyClaimed" 
                danger
                @click="handleUnclaim"
                :loading="claimLoading"
              >
                <UserDeleteOutlined />
                取消认领
              </a-button>
              
              <!-- 寄样按钮：仅在同时满足已关联联系人信息和具有有效达人ID时显示 -->
              <a-button
                v-if="canShowSampleButton()"
                type="primary"
                style="background-color: #52c41a; border-color: #52c41a;"
                @click="handleSampleClick"
              >
                <SendOutlined />
                寄样
              </a-button>

              <!-- 分享按钮 -->
              <a-button @click="handleShare">
                <ShareAltOutlined />
                分享
              </a-button>
            </a-button-group>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 达人数据统计卡片 -->
    <a-card 
      title="数据统计" 
      class="talent-stats-card"
      :bordered="false"
    >
      <div class="stats-grid">
        <!-- 粉丝数 -->
        <div class="stat-item">
          <div class="stat-icon">
            <TeamOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">
              {{ formatNumber(talent.粉丝数) }}
            </div>
            <div class="stat-label">粉丝数</div>
          </div>
        </div>
        
        <!-- 关注数 -->
        <div class="stat-item">
          <div class="stat-icon">
            <HeartOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">
              {{ formatNumber(talent.关注数) }}
            </div>
            <div class="stat-label">关注数</div>
          </div>
        </div>
        
        <!-- 获赞数 -->
        <div class="stat-item">
          <div class="stat-icon">
            <LikeOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">
              {{ formatNumber(talent.获赞数) }}
            </div>
            <div class="stat-label">获赞数</div>
          </div>
        </div>
        
        <!-- 账号状态 -->
        <!-- 
          账号状态显示逻辑：
          - 账号状态 = 1: 已注销账号（红色标签）
          - 账号状态 = null 或其他值: 正常账号（绿色标签）
          这与后端数据库字段定义一致：1表示已注销，null表示正常
        -->
        <div class="stat-item">
          <div class="stat-icon">
            <SafetyOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">
              <a-tag :color="talent.账号状态 === 1 ? 'red' : 'green'">
                {{ talent.账号状态 === 1 ? '已注销' : '正常' }}
              </a-tag>
            </div>
            <div class="stat-label">账号状态</div>
          </div>
        </div>
      </div>
    </a-card>



    <!-- 系统联系方式卡片 - 显示原始联系方式数据 -->
    <a-card 
      title="系统联系方式" 
      class="talent-contact-card"
      :bordered="false"
      v-if="hasSystemContactInfo"
    >
      <template #extra>
        <a-tag color="default" size="small">
          <DatabaseOutlined />
          系统数据
        </a-tag>
      </template>
      
      <div class="contact-grid">
        <!-- 微信号 -->
        <div class="contact-item" v-if="getContactInfo('微信号')">
          <div class="contact-icon">
            <WechatOutlined />
          </div>
          <div class="contact-content">
            <div class="contact-label">微信号</div>
            <div class="contact-value">
              {{ getContactInfo('微信号') }}
              <a-button 
                type="link" 
                size="small" 
                @click="copyToClipboard(getContactInfo('微信号'))"
              >
                <CopyOutlined />
              </a-button>
            </div>
          </div>
        </div>
        
        <!-- 手机号 -->
        <div class="contact-item" v-if="getContactInfo('手机号')">
          <div class="contact-icon">
            <PhoneOutlined />
          </div>
          <div class="contact-content">
            <div class="contact-label">手机号</div>
            <div class="contact-value">
              {{ getContactInfo('手机号') }}
              <a-button 
                type="link" 
                size="small" 
                @click="copyToClipboard(getContactInfo('手机号'))"
              >
                <CopyOutlined />
              </a-button>
            </div>
          </div>
        </div>
        
        <!-- 邮箱 -->
        <div class="contact-item" v-if="getContactInfo('邮箱')">
          <div class="contact-icon">
            <MailOutlined />
          </div>
          <div class="contact-content">
            <div class="contact-label">邮箱</div>
            <div class="contact-value">
              {{ getContactInfo('邮箱') }}
              <a-button 
                type="link" 
                size="small" 
                @click="copyToClipboard(getContactInfo('邮箱'))"
              >
                <CopyOutlined />
              </a-button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 如果没有系统联系方式，显示提示 -->
      <a-empty 
        v-if="!hasSystemContactInfo" 
        :image="false" 
        description="暂无系统联系方式" 
        style="margin: 20px 0;"
      />
    </a-card>

    <!-- 我的联系方式卡片 -->
    <a-card
      title="我的联系方式"
      class="my-contact-card"
      :bordered="false"
      v-if="talent.我的联系方式列表 && talent.我的联系方式列表.length > 0"
      :loading="talent.loading"
    >
      <template #extra>
        <a-button
          type="primary"
          size="small"
          @click="showAddContactModal"
        >
          <PlusOutlined />
          添加联系方式
        </a-button>
      </template>

      <div class="my-contact-list">
        <!-- 直接显示所有补充联系方式，不按平台分组 -->
        <div class="contact-grid">
          <div
            v-for="(contact, contactIndex) in getAllContacts(talent.我的联系方式列表)"
            :key="contactIndex"
            class="contact-card"
          >
            <div class="contact-card-header">
              <div class="contact-main-info">
                <div class="contact-info-row">
                  <div class="contact-type-badge">
                    <component :is="getContactIcon(contact.联系方式类型)" class="contact-icon" />
                    <span class="contact-type-text">{{ contact.联系方式类型 || '联系方式' }}</span>
                  </div>
                  <!-- 联系方式值 -->
                  <div class="contact-value">
                    {{ contact.联系方式 }}
                  </div>
                </div>
              </div>
              <div class="contact-actions">
                <a-button
                  type="text"
                  size="small"
                  @click="editContact(contact.关联id, contact)"
                  title="编辑"
                >
                  <EditOutlined />
                </a-button>
              </div>
            </div>

            <div class="contact-card-content">

              <!-- 个人备注 -->
              <div class="contact-note" v-if="contact.个人备注">
                <div class="note-label">备注</div>
                <div class="note-content">{{ contact.个人备注 }}</div>
              </div>

              <!-- 个人标签 -->
              <div class="contact-tags" v-if="contact.个人标签 && contact.个人标签.length > 0">
                <div class="tags-label">标签</div>
                <div class="tags-content">
                  <a-tag
                    v-for="tag in contact.个人标签"
                    :key="tag"
                    size="small"
                    color="blue"
                  >
                    {{ tag }}
                  </a-tag>
                </div>
              </div>

              <!-- 补充信息 -->
              <div class="contact-supplement" v-if="contact.补充信息 && Object.keys(contact.补充信息).length > 0">
                <div class="supplement-label">补充信息</div>
                <div class="supplement-content">
                  <div
                    v-for="(value, key) in contact.补充信息"
                    :key="key"
                    class="supplement-item"
                  >
                    <span class="supplement-key">{{ key }}</span>
                    <span class="supplement-value">{{ value }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 卡片底部信息 -->
            <div class="contact-card-footer" v-if="contact.创建时间">
              <div class="contact-time">
                <ClockCircleOutlined />
                <span>{{ formatDate(contact.创建时间) }}</span>
              </div>
              <div class="contact-operations">
                <a-button
                  type="text"
                  size="small"
                  danger
                  @click="deleteContact(contact.补充信息id)"
                  title="删除"
                >
                  <DeleteOutlined />
                </a-button>
              </div>
            </div>
          </div>
        </div>

        <div v-if="getAllContacts(talent.我的联系方式列表).length === 0" class="no-contacts">
          <a-empty
            :image="false"
            description="暂无联系方式"
            style="margin: 20px 0;"
          >
            <a-button
              type="primary"
              size="small"
              @click="showAddContactModal"
            >
              添加联系方式
            </a-button>
          </a-empty>
        </div>
      </div>
    </a-card>



    <!-- 认领信息卡片 -->
    <a-card 
      title="认领信息" 
      class="talent-claim-card"
      :bordered="false"
      v-if="showClaimInfo"
    >
      <a-descriptions :column="1" size="small">
        <a-descriptions-item label="认领成员" v-if="talent.认领成员">
          <div class="member-info">
            <a-avatar 
              :size="24" 
              :src="talent.认领成员?.avatar" 
            >
              <template #icon>
                <UserOutlined />
              </template>
            </a-avatar>
            <span class="member-name">
              {{ talent.认领成员?.nickname || '未知成员' }}
            </span>
          </div>
        </a-descriptions-item>
        
        <a-descriptions-item label="认领时间">
          {{ formatDateTime(talent.认领时间) }}
        </a-descriptions-item>

        <a-descriptions-item label="备注" v-if="talent.备注">
          {{ talent.备注 }}
        </a-descriptions-item>
      </a-descriptions>
    </a-card>
  </div>

  <!-- 添加/编辑联系方式模态框 -->
  <a-modal
    v-model:open="contactModalVisible"
    :title="contactModalMode === 'add' ? '添加联系方式' : '编辑联系方式'"
    :confirm-loading="contactModalLoading"
    @ok="handleContactSubmit"
    @cancel="handleContactCancel"
  >
    <a-form
      :model="contactForm"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="联系方式类型" required>
        <a-select
          v-model:value="contactForm.联系方式类型"
          placeholder="请选择联系方式类型"
          :disabled="contactModalMode === 'edit'"
        >
          <a-select-option value="微信">微信</a-select-option>
          <a-select-option value="手机">手机</a-select-option>
          <a-select-option value="邮箱">邮箱</a-select-option>
        </a-select>
        <div v-if="contactModalMode === 'edit'" class="form-help-text">
          联系方式类型一旦创建就不能修改，如需更改请删除后重新添加
        </div>
      </a-form-item>

      <a-form-item label="联系方式" required>
        <a-input
          v-model:value="contactForm.联系方式"
          placeholder="请输入联系方式"
          :disabled="contactModalMode === 'edit'"
        />
        <div v-if="contactModalMode === 'edit'" class="form-help-text">
          联系方式一旦创建就不能修改，如需更改请删除后重新添加
        </div>
      </a-form-item>

      <a-form-item label="个人标签">
        <a-select
          v-model:value="contactForm.个人标签"
          mode="tags"
          placeholder="请输入标签，按回车添加"
          :token-separators="[',', '，']"
        />
      </a-form-item>

      <a-form-item label="个人备注">
        <a-textarea
          v-model:value="contactForm.个人备注"
          placeholder="请输入个人备注"
          :rows="3"
        />
      </a-form-item>

      <a-form-item label="补充信息">
        <div class="additional-info-editor">
          <div
            v-for="(item, index) in contactForm.补充信息列表"
            :key="index"
            class="additional-info-item"
          >
            <a-input
              v-model:value="item.key"
              placeholder="字段名"
              style="width: 30%"
            />
            <a-input
              v-model:value="item.value"
              placeholder="字段值"
              style="width: 60%; margin-left: 8px"
            />
            <a-button
              type="link"
              danger
              @click="removeAdditionalInfo(index)"
              style="width: 10%"
            >
              <DeleteOutlined />
            </a-button>
          </div>
          <a-button
            type="dashed"
            @click="addAdditionalInfo"
            style="width: 100%; margin-top: 8px"
          >
            <PlusOutlined />
            添加补充信息
          </a-button>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import request from '@/services/api'
import talentService from '@/services/talentService'
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  ContactsOutlined,
  CopyOutlined,
  DatabaseOutlined,
  DeleteOutlined,
  EditOutlined,
  HeartOutlined,
  LikeOutlined,
  MailOutlined,
  NumberOutlined,
  PhoneOutlined,
  PlusCircleOutlined,
  PlusOutlined,
  SafetyOutlined,
  SendOutlined,
  ShareAltOutlined,
  SyncOutlined,
  TeamOutlined,
  UserAddOutlined,
  UserDeleteOutlined,
  UserOutlined,
  VideoCameraOutlined,
  WechatOutlined
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { computed, ref, watch } from 'vue'

/**
 * 组件属性定义
 * 接收父组件传入的达人数据和控制参数
 */
const props = defineProps({
  // 达人数据对象，包含所有达人信息
  talent: {
    type: Object,
    required: true,
    default: () => ({})
  },
  // 是否显示认领按钮
  showClaimButton: {
    type: Boolean,
    default: false
  },
  // 是否显示团队信息
  showTeamInfo: {
    type: Boolean,
    default: false
  },
  // 组件加载状态
  loading: {
    type: Boolean,
    default: false
  }
})

/**
 * 组件事件定义
 * 向父组件发送用户操作事件
 */
const emit = defineEmits([
  'claim',      // 认领达人事件
  'unclaim',    // 取消认领事件
  'update',     // 更新达人事件
  'share',      // 分享达人事件
  'openSample'  // 打开寄样申请事件
])

// 响应式数据
const claimLoading = ref(false)  // 认领操作加载状态
const updateLoading = ref(false) // 更新数据操作加载状态

// 联系方式管理相关状态
const contactModalVisible = ref(false)
const contactModalLoading = ref(false)
const contactModalMode = ref('add') // 'add' | 'edit'
const editingContactRelationId = ref(null)

const contactForm = ref({
  联系方式类型: '',
  联系方式: '',
  个人标签: [],
  个人备注: '',
  补充信息列表: []
})

/**
 * 计算属性：判断达人是否已被认领
 * 支持多种数据格式的兼容性判断
 */
const isAlreadyClaimed = computed(() => {
  return props.talent.已认领 || 
         props.talent.关联id || 
         props.talent.当前用户认领状态?.已认领 ||
         false
})



/**
 * 计算属性：判断是否有系统联系方式信息
 * 检查原始系统数据中的联系方式字段
 */
const hasSystemContactInfo = computed(() => {
  return !!(
    props.talent.联系方式列表?.length ||
    props.talent.联系方式 ||
    props.talent.wechat_id ||
    props.talent.phone ||
    props.talent.email ||
    getContactInfo('微信号') ||
    getContactInfo('手机号') ||
    getContactInfo('邮箱')
  )
})

/**
 * 计算属性：判断是否显示认领信息
 * 当达人已被认领且有相关信息时显示
 */
const showClaimInfo = computed(() => {
  return (
    isAlreadyClaimed.value && 
    (props.talent.认领时间 || props.talent.认领成员 || props.talent.关联id)
  )
})

/**
 * 获取合作状态对应的颜色
 * 不同状态使用不同颜色便于快速识别
 * 
 * @param {string} status - 合作状态
 * @returns {string} 对应的颜色
 */
const getCooperationStatusColor = (status) => {
  const statusColors = {
    '待联系': 'default',
    '已联系': 'blue',
    '洽谈中': 'orange',
    '合作中': 'green',
    '已结束': 'purple',
    '暂停合作': 'red'
  }
  return statusColors[status] || 'default'
}

/**
 * 获取特定类型的联系方式信息
 * 支持多种数据结构的兼容性处理
 * @param {string} type - 联系方式类型：'微信号'、'手机号'、'邮箱'
 * @returns {string} 联系方式值
 */
const getContactInfo = (type) => {
  // 映射前端显示类型到数据库存储类型
  const typeMapping = {
    '微信号': ['微信', 'wechat'],
    '手机号': ['手机', 'phone'],
    '邮箱': ['邮箱', 'email']
  }

  // 优先从联系方式列表中查找
  if (props.talent.联系方式列表?.length) {
    const dbTypes = typeMapping[type] || [type]
    for (const dbType of dbTypes) {
      // 兼容抖音达人和微信达人的不同字段名
      const contact = props.talent.联系方式列表.find(item =>
        (item.类型 === dbType) || (item.联系类型 === dbType)
      )
      if (contact) {
        // 返回联系方式值，兼容不同的字段名
        return contact.值 || contact.联系内容
      }
    }
  }

  // 从联系方式对象中查找
  if (props.talent.联系方式) {
    return props.talent.联系方式[type]
  }

  // 从直接字段中查找（兼容性处理）
  switch (type) {
    case '微信号':
      return props.talent.wechat_id
    case '手机号':
      return props.talent.phone
    case '邮箱':
      return props.talent.email
    default:
      return null
  }
}

/**
 * 格式化数字显示
 * 将大数字转换为易读格式（如：10000 -> 1.0w）
 * @param {number} num - 需要格式化的数字
 * @returns {string} 格式化后的字符串
 */
const formatNumber = (num) => {
  if (!num || num === 0) return '0'
  
  if (num >= 100000000) {
    return (num / 100000000).toFixed(1) + '亿'
  } else if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  
  return num.toString()
}

/**
 * 格式化日期时间显示
 * 将日期字符串格式化为易读格式
 * @param {string} dateStr - 日期字符串
 * @returns {string} 格式化后的日期字符串
 */
const formatDateTime = (dateStr) => {
  if (!dateStr) return '未知'
  
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    console.warn('日期格式化失败:', error)
    return dateStr
  }
}

/**
 * 复制文本到剪贴板
 * 提供一键复制联系方式的功能
 * @param {string} text - 要复制的文本
 */
const copyToClipboard = async (text) => {
  if (!text) return

  try {
    await navigator.clipboard.writeText(text)
    message.success('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    // 降级处理：使用传统方法复制
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()

    try {
      document.execCommand('copy')
      message.success('已复制到剪贴板')
    } catch (fallbackError) {
      message.error('复制失败，请手动复制')
    }

    document.body.removeChild(textArea)
  }
}

/**
 * 获取所有联系方式的扁平化列表
 * @param {Array} relationList - 关联列表
 * @returns {Array} 扁平化的联系方式列表
 */
const getAllContacts = (relationList) => {
  if (!relationList || !Array.isArray(relationList)) return []

  const allContacts = []
  relationList.forEach(relation => {
    if (relation.补充联系方式列表 && Array.isArray(relation.补充联系方式列表)) {
      relation.补充联系方式列表.forEach(contact => {
        allContacts.push({
          ...contact,
          关联id: relation.关联id || relation.id
        })
      })
    }
  })

  return allContacts
}

/**
 * 根据联系方式类型获取对应图标
 * @param {string} type - 联系方式类型
 * @returns {Component} 对应的图标组件
 */
const getContactIcon = (type) => {
  const iconMap = {
    '手机': PhoneOutlined,
    '电话': PhoneOutlined,
    '微信': WechatOutlined,
    '邮箱': MailOutlined,
    'QQ': ContactsOutlined,
    '默认': ContactsOutlined
  }
  return iconMap[type] || iconMap['默认']
}

/**
 * 处理认领达人操作
 * 向父组件发送认领事件
 */
const handleClaim = async () => {
  if (claimLoading.value) return
  
  claimLoading.value = true
  try {
    emit('claim', props.talent)
  } catch (error) {
    console.error('认领操作失败:', error)
    message.error('认领失败，请重试')
  } finally {
    claimLoading.value = false
  }
}

/**
 * 处理取消认领达人操作
 * 向父组件发送取消认领事件
 */
const handleUnclaim = async () => {
  if (claimLoading.value) return
  
  claimLoading.value = true
  try {
    emit('unclaim', props.talent)
  } catch (error) {
    console.error('取消认领操作失败:', error)
    message.error('取消认领失败，请重试')
  } finally {
    claimLoading.value = false
  }
}

/**
 * 处理分享达人操作
 * 向父组件发送分享事件
 */
const handleShare = () => {
  emit('share', props.talent)
}

/**
 * 处理更新达人数据操作
 * 调用后端接口从第三方API获取最新数据并更新
 */
const handleUpdateTalentData = async () => {
  if (updateLoading.value) return
  
  const uid = props.talent.uid_number || props.talent.UID
  const talentId = props.talent.id || props.talent.达人id
  
  if (!talentId) {
    message.warning('缺少达人id，无法更新数据')
    return
  }
  
  // 显示确认对话框
  Modal.confirm({
    title: '确认更新达人数据',
    content: `将获取 ${props.talent.nickname || props.talent.昵称 || '该达人'} 的信息。此操作可能需要几秒钟时间。`,
    okText: '确认更新',
    cancelText: '取消',
    onOk: async () => {
      updateLoading.value = true
      try {
        console.log('🔄 开始更新达人数据:', { talentId, uid: uid || '使用数据库UID' })
        
        // 构建请求数据，如果有UID就传入，没有就让后端从数据库获取
        const requestData = { 达人id: talentId }
        const response = await talentService.updateTalentData(requestData)
        
        if (response.status === 100) {
          message.success('达人数据更新成功！')
          console.log('✅ 达人数据更新成功:', response.data)
          
          // 触发刷新详情事件，让父组件重新加载达人数据
          emit('update', props.talent)
          
          // 如果有刷新函数，也调用一下
          if (typeof refreshTalentDetail === 'function') {
            await refreshTalentDetail()
          }
        } else {
          throw new Error(response.message || '更新失败')
        }
      } catch (error) {
        console.error('❌ 更新达人数据失败:', error)
        const errorMessage = error.response?.data?.message || error.message || '更新达人数据失败'
        message.error(`更新失败: ${errorMessage}`)
      } finally {
        updateLoading.value = false
      }
    }
  })
}

/**
 * 格式化日期显示
 */
const formatDate = (dateString) => {
  if (!dateString) return ''
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateString
  }
}

/**
 * 显示添加联系方式模态框
 */
const showAddContactModal = () => {
  contactModalMode.value = 'add'
  contactForm.value = {
    联系方式类型: '',
    联系方式: '',
    个人标签: [],
    个人备注: '',
    补充信息列表: []
  }
  contactModalVisible.value = true
}

/**
 * 编辑联系方式
 */
const editContact = (relationId, contact) => {
  contactModalMode.value = 'edit'
  editingContactRelationId.value = contact.补充信息id  // 使用补充信息id而不是关联id

  // 转换补充信息为编辑格式
  const 补充信息列表 = []
  if (contact.补充信息 && typeof contact.补充信息 === 'object') {
    Object.entries(contact.补充信息).forEach(([key, value]) => {
      补充信息列表.push({ key, value })
    })
  }

  contactForm.value = {
    联系方式类型: contact.联系方式类型 || '',
    联系方式: contact.联系方式 || '',
    个人标签: contact.个人标签 || [],
    个人备注: contact.个人备注 || '',
    补充信息列表
  }

  contactModalVisible.value = true
}

/**
 * 删除联系方式
 */
const deleteContact = async (补充信息id) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这个联系方式吗？此操作不可恢复。',
    onOk: async () => {
      try {
        // 调用删除接口
        const response = await request.post('/kol/delete-contact', {
          补充信息id: 补充信息id
        })

        if (response.status === 100) {
          message.success('删除联系方式成功')
          // 重新获取达人详情数据，但不关闭详情弹框
          await refreshTalentDetail()
        } else {
          message.error(response.message || '删除联系方式失败')
        }
      } catch (error) {
        console.error('删除联系方式失败:', error)
        message.error('删除联系方式失败，请重试')
      }
    },
    onCancel: () => {
      // 用户取消删除，不需要做任何操作
    }
  })
}

/**
 * 添加补充信息项
 */
const addAdditionalInfo = () => {
  contactForm.value.补充信息列表.push({ key: '', value: '' })
}

/**
 * 移除补充信息项
 */
const removeAdditionalInfo = (index) => {
  contactForm.value.补充信息列表.splice(index, 1)
}

/**
 * 处理联系方式提交
 */
const handleContactSubmit = async () => {
  try {
    // 表单验证
    // 添加模式下需要验证联系方式信息
    if (contactModalMode.value === 'add') {
      if (!contactForm.value.联系方式类型) {
        message.error('请选择联系方式类型')
        return
      }
      if (!contactForm.value.联系方式) {
        message.error('请输入联系方式')
        return
      }
    }

    contactModalLoading.value = true

    // 转换补充信息格式
    const 补充信息 = {}
    contactForm.value.补充信息列表.forEach(item => {
      if (item.key && item.value) {
        补充信息[item.key] = item.value
      }
    })

    // 构建请求数据
    const requestData = {
      个人备注: contactForm.value.个人备注 || null,
      个人标签: contactForm.value.个人标签.length > 0 ? contactForm.value.个人标签 : null,
      补充信息: Object.keys(补充信息).length > 0 ? 补充信息 : null
    }

    // 添加模式下需要传递联系方式信息
    if (contactModalMode.value === 'add') {
      requestData.联系方式 = contactForm.value.联系方式
      requestData.联系方式类型 = contactForm.value.联系方式类型
    }

    let response
    if (contactModalMode.value === 'add') {
      // 添加联系方式
      response = await request.post('/kol/add-contact', {
        达人id: props.talent.id,
        ...requestData
      })
    } else {
      // 更新联系方式
      response = await request.post('/kol/update-contact', {
        补充信息id: editingContactRelationId.value,
        ...requestData
      })
    }

    if (response.status === 100) {
      message.success(contactModalMode.value === 'add' ? '添加联系方式成功' : '更新联系方式成功')
      contactModalVisible.value = false
      // 重新获取达人详情数据，但不关闭详情弹框
      await refreshTalentDetail()
    } else {
      message.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('联系方式操作失败:', error)
    message.error('操作失败，请重试')
  } finally {
    contactModalLoading.value = false
  }
}

/**
 * 取消联系方式操作
 */
const handleContactCancel = () => {
  contactModalVisible.value = false
  editingContactRelationId.value = null
}

/**
 * 刷新达人详情数据
 */
const refreshTalentDetail = async () => {
  try {
    // 调用父组件的刷新方法，重新获取达人详情
    const response = await request.post('/kol/detail', {
      达人id: props.talent.id
    })

    if (response.status === 100 && response.data) {
      // 更新当前组件的talent数据
      Object.assign(props.talent, response.data)
    }
  } catch (error) {
    console.error('刷新达人详情失败:', error)
  }
}

/**
 * 寄样按钮显示条件判断
 * 按照需求：达人必须同时满足以下两个条件
 * 1. 达人关联的达人补充信息表的id字段不为null
 * 2. 达人补充信息表中的用户联系人表id字段不为null
 *
 * 实际上，如果用户联系人表id存在，说明达人补充信息表记录也存在
 * 所以只需要检查用户联系人表id即可
 */
const canShowSampleButton = () => {
  // 检查达人补充信息表中的用户联系人表id字段是否不为null
  const hasContactInfo = props.talent.用户联系人表id && props.talent.用户联系人表id !== null

  // 调试日志
  console.log('🔍 寄样按钮显示条件检查:', {
    达人: props.talent.昵称 || props.talent.nickname,
    用户联系人表id: props.talent.用户联系人表id,
    可显示寄样按钮: hasContactInfo
  })

  return hasContactInfo
}

/**
 * 处理寄样按钮点击事件
 */
const handleSampleClick = () => {
  console.log('🔥 寄样按钮被点击了!')
  openSampleModal()
}

/**
 * 打开寄样申请弹窗
 * 复用现有的寄样功能逻辑
 */
const openSampleModal = () => {
  console.log('🎯 打开寄样申请弹窗 - 开始:', {
    达人信息: props.talent,
    用户联系人表id: props.talent.用户联系人表id
  })

  // 再次检查显示条件
  if (!canShowSampleButton()) {
    console.warn('⚠️ 寄样按钮不应该显示，但被点击了')
    message.warning('该达人暂无联系方式信息，无法申请寄样')
    return
  }

  // 从我的联系方式列表中提取联系方式信息
  const getMyContactInfo = () => {
    if (props.talent.我的联系方式列表?.length > 0) {
      const firstRelation = props.talent.我的联系方式列表[0]
      if (firstRelation.补充联系方式列表?.length > 0) {
        const firstContact = firstRelation.补充联系方式列表[0]
        return {
          联系方式: firstContact.联系方式 || '',
          用户联系人表id: firstContact.用户联系人表id
        }
      }
    }
    return { 联系方式: '', 用户联系人表id: null }
  }

  const myContactInfo = getMyContactInfo()

  // 获取联系方式信息（优先级：我的联系方式 > 原有联系方式 > 系统联系方式）
  const contactValue = myContactInfo.联系方式 || props.talent.联系方式 ||
                      getContactInfo('微信号') || getContactInfo('手机号') || ''

  // 构建达人信息对象
  const talentInfo = {
    ...props.talent,
    // 统一字段映射
    平台: props.talent.平台 || '抖音',
    platform: props.talent.平台 || '抖音',
    联系方式: contactValue,
    contact: contactValue,
    contactInfo: {
      用户联系人表id: myContactInfo.用户联系人表id || props.talent.用户联系人表id
    }
  }

  console.log('🎯 准备发出寄样事件:', talentInfo)

  // 触发父组件的寄样事件，传递达人信息
  emit('openSample', talentInfo)

  console.log('✅ 寄样事件已发出')
}

// 监听达人数据变化，用于调试和优化
watch(
  () => props.talent,
  (newTalent) => {
    console.log('达人详情数据更新:', newTalent)
  },
  { deep: true }
)
</script>

<style scoped>
/**
 * 达人详情面板样式
 * 采用现代化的卡片设计，提供良好的视觉体验
 */
.talent-detail-panel {
  width: 100%;
  padding: 0;
}

/* 卡片通用样式 */
.talent-detail-panel .ant-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.talent-detail-panel .ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.talent-detail-panel .ant-card:last-child {
  margin-bottom: 0;
}

/* 卡片头部样式 */
.card-header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1f2937;
  font-weight: 600;
}

.title-icon {
  color: #3b82f6;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 达人头部信息样式 */
.talent-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 0;
}

.talent-avatar-section {
  position: relative;
  flex-shrink: 0;
}

.claim-status-tag {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 1;
}

.talent-info-section {
  flex: 1;
  min-width: 0;
}

.talent-name-section {
  margin-bottom: 16px;
}

.talent-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  word-break: break-word;
}

.update-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #8b949e;
  margin-top: 4px;
}

.talent-intro {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.intro-text {
  margin: 0;
  line-height: 1.6;
  color: #595959;
  font-size: 14px;
  font-style: italic;
}

.talent-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #666;
}

.meta-item .anticon {
  color: #1890ff;
}

.quick-actions {
  margin-top: 12px;
}

/* 数据统计网格样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fafafa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: #f0f0f0;
}

.stat-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e6f7ff;
  border-radius: 50%;
  color: #1890ff;
  font-size: 16px;
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

/* 个人管理信息样式 */
.talent-personal-card {
  background: linear-gradient(135deg, #f6f9ff 0%, #e6f7ff 100%);
  border: 1px solid #91d5ff;
}

.personal-info-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.personal-info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.personal-info-item.full-width {
  grid-column: 1 / -1;
}

.info-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  font-size: 14px;
}

.info-label {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.info-content {
  padding-left: 28px;
}

.personal-contact-section {
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e8f4fd;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.tags-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.note-content {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.note-content p {
  margin: 0;
  color: #595959;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 个人管理信息空状态样式 */
.talent-personal-empty-card {
  background: linear-gradient(135deg, #fff7e6 0%, #fff2e8 100%);
  border: 1px solid #ffd591;
}

.empty-description {
  text-align: left;
  max-width: 300px;
  margin: 0 auto;
}

.empty-description p {
  margin-bottom: 12px;
  color: #666;
  font-size: 14px;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  padding: 4px 0;
  color: #666;
  font-size: 13px;
  line-height: 1.4;
}

/* 联系方式网格样式 */
.contact-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px;
  background: #fafafa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.contact-item:hover {
  background: #f0f0f0;
}

.contact-icon {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 50%;
  color: #52c41a;
  font-size: 14px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.contact-content {
  flex: 1;
  min-width: 0;
}

.contact-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.contact-value {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}



/* 认领信息样式 */
.member-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.member-name {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .talent-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 12px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .talent-name {
    font-size: 16px;
  }

  .stat-value {
    font-size: 14px;
  }

  /* 移动端联系方式网格优化 */
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .contact-card-header {
    padding: 10px 12px;
    min-height: 50px;
    gap: 8px;
  }

  .contact-info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .contact-type-badge {
    min-width: auto;
  }

  .contact-value {
    font-size: 14px;
    line-height: 1.2;
  }

  .contact-type-text {
    font-size: 10px;
  }

  .contact-actions .ant-btn {
    height: 28px;
    width: 28px;
  }

  .contact-card-content {
    padding: 0 12px 12px 12px;
  }
}

@media (max-width: 1200px) {
  /* 中等屏幕优化 */
  .contact-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

/* 加载状态样式 */
.talent-detail-panel .ant-card.ant-card-loading {
  min-height: 200px;
}

/* 空状态样式优化 */
.talent-detail-panel .ant-empty {
  padding: 20px 0;
}

.talent-detail-panel .ant-empty-description {
  color: #999;
  font-size: 13px;
}

/* 我的联系方式卡片样式 */
.my-contact-card {
  margin-bottom: 16px;
}

.my-contact-list {
  width: 100%;
}

/* 联系方式网格布局 */
.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 12px;
}

/* 联系方式卡片样式 */
.contact-card {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background: #ffffff;
  transition: all 0.3s ease;
  overflow: hidden;
}

.contact-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.contact-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e8e8e8;
  gap: 12px;
  min-height: 60px;
}

.contact-main-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.contact-info-row {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.contact-type-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
  min-width: 80px;
}

.contact-icon {
  font-size: 12px;
  color: #1890ff;
}

.contact-type-text {
  font-size: 11px;
  font-weight: 500;
  color: #8c8c8c;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.contact-value {
  font-size: 15px;
  font-weight: 600;
  color: #262626;
  word-break: break-all;
  line-height: 1.3;
  flex: 1;
}

.contact-actions {
  display: flex;
  flex-shrink: 0;
  align-items: center;
}

.contact-actions .ant-btn {
  height: 32px;
  width: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.contact-card-content {
  padding: 0 16px 16px 16px;
}

.contact-note {
  margin-bottom: 12px;
}

.note-label {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.note-content {
  font-size: 13px;
  color: #595959;
  background: #f6f6f6;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

.contact-tags {
  margin-bottom: 12px;
}

.tags-label {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 6px;
}

.tags-content {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.contact-supplement {
  margin-bottom: 12px;
}

.supplement-label {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 6px;
}

.supplement-content {
  background: #f0f2f5;
  padding: 8px 12px;
  border-radius: 4px;
}

.supplement-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.supplement-item:last-child {
  margin-bottom: 0;
}

.supplement-key {
  font-size: 12px;
  color: #8c8c8c;
  flex-shrink: 0;
  margin-right: 8px;
}

.supplement-value {
  font-size: 12px;
  color: #262626;
  text-align: right;
  word-break: break-all;
}

.contact-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #fafafa;
  border-top: 1px solid #e8e8e8;
}

.contact-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #8c8c8c;
}

.contact-operations {
  display: flex;
  gap: 4px;
}

.no-contacts {
  text-align: center;
  padding: 40px 20px;
}

.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}



.contact-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.detail-label {
  font-size: 13px;
  color: #666;
  min-width: 80px;
  flex-shrink: 0;
}

.detail-value {
  font-size: 13px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 4px;
}

.detail-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.detail-additional {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.additional-item {
  display: flex;
  gap: 8px;
  font-size: 12px;
}

.additional-key {
  color: #1890ff;
  font-weight: 500;
  min-width: 60px;
}

.additional-value {
  color: #333;
  flex: 1;
}

.detail-note {
  font-size: 13px;
  color: #333;
  line-height: 1.5;
  background: #f9f9f9;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}



.contact-list {
  padding: 0;
}

.contact-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.contact-item:last-child {
  border-bottom: none;
  border-radius: 0 0 8px 8px;
}

.contact-content {
  flex: 1;
}

.contact-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-left: 16px;
}

.contact-meta {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.no-contacts {
  padding: 16px;
  text-align: center;
  background: #fff;
  border-radius: 0 0 8px 8px;
}

.additional-info-editor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
}

.form-help-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

.additional-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.additional-info-item:last-child {
  margin-bottom: 0;
}
</style>