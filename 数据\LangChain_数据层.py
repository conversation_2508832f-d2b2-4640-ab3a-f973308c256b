"""
LangChain智能体数据层

功能：
1. 智能体配置数据操作
2. 提示词配置数据操作
3. 用户智能体关联数据操作
4. 对话记录数据操作
5. 知识库关联数据操作
"""

import json
import logging
from typing import Any, Dict, List, Optional, Tuple

from 数据.Postgre_异步连接池 import Postgre_异步数据库连接池

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例



# 配置日志
数据日志器 = logging.getLogger("LangChain.数据层")


class 轻量级事务包装器:
    """轻量级事务包装器 - 最小内存开销"""

    def __init__(self, 连接, 事务):
        self.连接 = 连接
        self.事务 = 事务
        self.已提交 = False
        self.已回滚 = False

    async def 创建知识库文档(self, 文档数据: Dict[str, Any]) -> int:
        """在事务中创建知识库文档"""
        插入SQL = """
        INSERT INTO langchain_知识库文档表
        (uuid, langchain_知识库表id, 用户文件路径, 文档名称, 文档类型, 文档大小,
         文档内容, 元数据, 文档状态, 向量状态, 向量分块数量, md5)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        RETURNING uuid
        """

        参数 = (
            文档数据.get("uuid") or 文档数据.get("文档uuid"),
            文档数据.get("langchain_知识库表id"),
            文档数据.get("文档路径", ""),
            文档数据.get("文档名称"),
            文档数据.get("文档类型"),
            文档数据.get("文档大小", 0),
            文档数据.get("文档内容", ""),
            json.dumps(文档数据.get("元数据", {}), ensure_ascii=False),
            文档数据.get("文档状态", "处理中"),
            文档数据.get("向量状态", "待处理"),
            文档数据.get("向量分块数量", 0),
            文档数据.get("md5", ""),
        )

        结果 = await self.连接.fetchval(插入SQL, *参数)
        return 结果



    async def 提交(self):
        """提交事务"""
        if not self.已提交 and not self.已回滚:
            await self.事务.commit()
            self.已提交 = True

    async def 回滚(self):
        """回滚事务"""
        if not self.已提交 and not self.已回滚:
            await self.事务.rollback()
            self.已回滚 = True


class LangChain数据层:
    """LangChain智能体数据层 - 优雅架构设计

    架构原则：
    1. 依赖注入 - 构造时注入所有依赖
    2. 类型安全 - 完全的类型保证
    3. 单一职责 - 专注于数据层协调
    4. 优雅初始化 - 避免复杂的异步初始化
    """

    def __init__(self, 数据库连接池: Optional[Postgre_异步数据库连接池] = None):
        """构造函数 - 依赖注入模式

        Args:
            数据库连接池: PostgreSQL连接池实例，如果为None则使用默认实例
        """
        # 依赖注入 - 确保数据库连接池永远不为None
        self.数据库连接池: Postgre_异步数据库连接池 = 数据库连接池 or 异步连接池实例
        self.已初始化: bool = True  # 简化初始化逻辑

        # 延迟初始化子数据层 - 避免循环导入
        self.模型数据层: Optional[Any] = None
        self.智能体数据层: Optional[Any] = None

        数据日志器.info("LangChain数据层创建成功")

    @classmethod
    async def 创建实例(cls) -> "LangChain数据层":
        """异步工厂方法 - 确保所有依赖都已初始化"""
        # 确保连接池已初始化
        if not 异步连接池实例.已初始化:
            await 异步连接池实例.初始化数据库连接池()

        # 创建数据层实例
        实例 = cls(异步连接池实例)

        # 执行异步初始化逻辑
        await 实例._异步初始化()

        return 实例

    async def _异步初始化(self):
        """内部异步初始化方法"""
        try:
            from 数据.LangChain_智能体数据层 import LangChain智能体数据层实例
            from 数据.LangChain_模型数据层 import LangChain模型数据层实例

            # 初始化子数据层
            self.模型数据层 = LangChain模型数据层实例
            self.智能体数据层 = LangChain智能体数据层实例

            # 确保子数据层已初始化
            if not self.模型数据层.已初始化:
                await self.模型数据层.初始化()

            if not self.智能体数据层.已初始化:
                await self.智能体数据层.初始化()

            数据日志器.info("LangChain数据层异步初始化成功")
        except Exception as e:
            数据日志器.error(f"LangChain数据层异步初始化失败: {str(e)}")
            raise

    # ==================== 轻量级事务支持 ====================

    async def 开始事务(self):
        """开始数据库事务 - 轻量级实现"""
        from contextlib import asynccontextmanager

        @asynccontextmanager
        async def 事务上下文():
            连接 = None
            事务 = None
            try:
                # 获取数据库连接
                连接 = await self.数据库连接池.获取连接()
                # 开始事务
                事务 = 连接.transaction()
                await 事务.start()

                # 创建事务包装器
                事务包装器 = 轻量级事务包装器(连接, 事务)
                yield 事务包装器

                # 如果没有异常，提交事务
                await 事务.commit()
                数据日志器.debug("事务提交成功")

            except Exception as e:
                # 发生异常时回滚事务
                if 事务:
                    try:
                        await 事务.rollback()
                        数据日志器.debug("事务回滚成功")
                    except Exception as rollback_error:
                        数据日志器.error(f"事务回滚失败: {rollback_error}")
                raise e
            finally:
                # 释放连接
                if 连接:
                    await self.数据库连接池.释放连接(连接)

        return 事务上下文()

    # ==================== 智能体配置相关操作 ====================

    # ==================== 提示词配置相关操作 ====================

    async def 创建提示词配置(self, 配置数据: Dict[str, Any]) -> Optional[int]:
        """创建提示词配置"""
        try:
            插入SQL = """
            INSERT INTO langchain_提示词配置表
            (langchain_智能体配置表id, 系统提示词, 用户提示词, 角色设定, 行为规范)
            VALUES ($1, $2, $3, $4, $5)
            """

            参数 = (
                配置数据.get("langchain_智能体配置表id"),
                配置数据.get("系统提示词", ""),
                配置数据.get("用户提示词", ""),
                配置数据.get("角色设定", ""),
                配置数据.get("行为规范", ""),
            )

            结果 = await self.数据库连接池.执行插入(插入SQL, 参数)
            数据日志器.info(
                f"提示词配置创建成功: 智能体id {配置数据.get('langchain_智能体配置表id')}"
            )
            return 结果

        except Exception as e:
            数据日志器.error(f"创建提示词配置失败: {str(e)}")
            raise

    # ==================== 对话记录相关操作 ====================

    async def 创建对话记录(self, 记录数据: Dict[str, Any]) -> Optional[int]:
        """创建对话记录"""
        try:
            插入SQL = """
            INSERT INTO langchain_对话记录表
            (用户表id, langchain_智能体配置表id, 会话id, 用户消息, 智能体回复, 模型名称, 处理时长, 算力消耗, 令牌消耗, 元数据,
             调用来源, 实际输入token, 实际输出token, 实际token总数, 估算输入token, 估算输出token, 估算token总数, 创建时间)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, NOW())
            """

            参数 = (
                记录数据.get("用户表id"),
                记录数据.get("langchain_智能体配置表id"),
                记录数据.get("会话id"),
                记录数据.get("用户消息"),
                记录数据.get("智能体回复"),
                记录数据.get("模型名称"),
                记录数据.get("处理时长"),
                记录数据.get("算力消耗"),
                记录数据.get("令牌消耗"),
                记录数据.get("元数据"),
                记录数据.get("调用来源", "用户"),
                记录数据.get("实际输入token", 0),
                记录数据.get("实际输出token", 0),
                记录数据.get("实际token总数", 0),
                记录数据.get("估算输入token", 0),
                记录数据.get("估算输出token", 0),
                记录数据.get("估算token总数", 0),
            )

            结果 = await self.数据库连接池.执行插入(插入SQL, 参数)
            数据日志器.debug(
                f"对话记录创建成功: 用户{记录数据.get('用户表id')} -> 智能体{记录数据.get('langchain_智能体配置表id')}"
            )
            return 结果

        except Exception as e:
            数据日志器.error(f"创建对话记录失败: {str(e)}")
            raise

    # ==================== 管理端专用数据操作 ====================

    async def 创建知识库(self, 知识库数据: Dict[str, Any]) -> Optional[int]:
        """创建LangChain知识库"""
        try:
            插入SQL = """
            INSERT INTO langchain_知识库表
            (知识库名称, 知识库描述, 用户表id, 组织id, 嵌入模型, 是否公开, 向量存储类型, 知识库状态,
             文档数量, 向量维度, 默认启用查询优化, 默认查询优化策略, 创建时间, 更新时间)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW(), NOW())
            """

            嵌入模型id = 知识库数据.get("嵌入模型id")
            if 嵌入模型id is not None:
                try:
                    嵌入模型id = int(嵌入模型id)
                except (ValueError, TypeError):
                    嵌入模型id = None

            参数 = (
                知识库数据["知识库名称"],
                知识库数据.get("知识库描述", ""),
                知识库数据["用户表id"],
                1,  # 组织id固定为1
                嵌入模型id,
                知识库数据.get("是否公开", False),
                "postgresql",
                知识库数据.get("知识库状态", "正常"),  # 默认状态为"正常"
                知识库数据.get("文档数量", 0),  # 默认文档数量为0
                知识库数据.get("向量维度", 1024),  # 默认向量维度为1024
                知识库数据.get("默认启用查询优化", 0),  # 默认不启用查询优化
                知识库数据.get("默认查询优化策略", "rewrite"),  # 默认查询优化策略
            )

            知识id = await self.数据库连接池.执行插入并返回id(插入SQL, 参数)

            if 知识id:
                数据日志器.info(
                    f"知识库创建成功: {知识库数据['知识库名称']} (ID: {知识id})"
                )
                return 知识id
            else:
                数据日志器.error("知识库创建失败: 未返回有效ID")
                return None

        except Exception as e:
            数据日志器.error(f"创建知识库失败: {str(e)}")
            raise

    async def 获取知识库详情(self, 知识id: int) -> Optional[Dict[str, Any]]:
        """获取知识库详情"""
        try:
            查询SQL = """
            SELECT
                kb.id, kb.知识库名称, kb.知识库描述, kb.用户表id, kb.组织id, kb.知识库状态,
                kb.文档数量, kb.嵌入模型, kb.是否公开, kb.向量维度,
                kb.向量存储类型, kb.存储类型,
                kb.默认启用查询优化, kb.默认查询优化策略, kb.默认查询优化模型id, kb.默认查询优化提示词,
                kb.创建时间, kb.更新时间,
                em.显示名称 as 嵌入模型名称,
                em.提供商 as 嵌入模型提供商,
                u.昵称 as 创建者昵称
            FROM langchain_知识库表 kb
            LEFT JOIN langchain_模型配置表 em ON kb.嵌入模型 = em.id
            LEFT JOIN 用户表 u ON kb.用户表id = u.id
            WHERE kb.id = $1
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (知识id,))

            if 结果:
                知识库信息 = 结果[0]
                数据日志器.info(f"获取知识库详情成功: {知识id}")
                return 知识库信息
            else:
                数据日志器.warning(f"知识库不存在: {知识id}")
                return None

        except Exception as e:
            数据日志器.error(f"获取知识库详情失败: {str(e)}")
            raise

    async def 更新知识库(self, 知识id: int, 更新数据: Dict[str, Any]) -> bool:
        """更新知识库信息"""
        try:
            # 构建动态更新SQL
            更新字段 = []
            参数值 = []

            # 支持的更新字段（仅包含数据库表中实际存在的字段）
            字段映射 = {
                "知识库名称": "知识库名称",
                "知识库描述": "知识库描述",
                "知识库状态": "知识库状态",
                "嵌入模型": "嵌入模型",
                "向量维度": "向量维度",
                "是否公开": "是否公开",
                "向量存储类型": "向量存储类型",
                "存储类型": "存储类型",
                # 查询优化配置字段
                "默认启用查询优化": "默认启用查询优化",
                "默认查询优化策略": "默认查询优化策略",
                "默认查询优化模型id": "默认查询优化模型id",
                "默认查询优化提示词": "默认查询优化提示词",
            }

            for 字段名, 数据库字段 in 字段映射.items():
                if 字段名 in 更新数据:
                    值 = 更新数据[字段名]
                    # 特殊处理嵌入模型字段：验证模型id是否存在
                    if 字段名 == "嵌入模型" and isinstance(值, int):
                        模型验证SQL = """
                        SELECT id FROM langchain_模型配置表
                        WHERE id = $1 AND 模型类型 LIKE '%embedding%'
                        """
                        模型结果 = await self.数据库连接池.执行查询(模型验证SQL, (值,))
                        if not 模型结果:
                            数据日志器.warning(f"嵌入模型id不存在: {值}")
                            continue  # 跳过无效的模型id
                        数据日志器.info(f"使用嵌入模型id: {值}")
                    # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
                    # {{ Source: PostgreSQL参数占位符文档 }}
                    更新字段.append(f"{数据库字段} = ${len(参数值) + 1}")
                    参数值.append(值)

            if not 更新字段:
                数据日志器.warning("没有需要更新的字段")
                return False

            更新字段.append("更新时间 = NOW()")
            # 注意：NOW() 不需要参数，所以不要添加到参数值列表中

            更新SQL = f"""
            UPDATE langchain_知识库表
            SET {", ".join(更新字段)}
            WHERE id = ${len(参数值) + 1}
            """

            # 添加知识id作为WHERE条件的参数
            参数值.append(知识id)

            await self.数据库连接池.执行更新(更新SQL, tuple(参数值))

            数据日志器.info(f"更新知识库成功: {知识id}")
            return True

        except Exception as e:
            数据日志器.error(f"更新知识库失败: {str(e)}")
            raise

    async def 删除知识库(self, 知识id: int) -> bool:
        """删除知识库（软删除）"""
        try:
            # 先检查是否有关联的智能体
            检查SQL = """
            SELECT COUNT(*) as count
            FROM langchain_智能体知识库关联表
            WHERE langchain_知识库表id = $1
            """

            关联结果 = await self.数据库连接池.执行查询(检查SQL, (知识id,))
            关联数量 = 关联结果[0]["count"] if 关联结果 else 0

            if 关联数量 > 0:
                数据日志器.warning(
                    f"知识库 {知识id} 仍有 {关联数量} 个智能体关联，无法删除"
                )
                return False

            # 软删除知识库
            删除SQL = """
            UPDATE langchain_知识库表
            SET 知识库状态 = '已删除', 更新时间 = NOW()
            WHERE id = $1
            """

            await self.数据库连接池.执行更新(删除SQL, (知识id,))

            数据日志器.info(f"删除知识库成功: {知识id}")
            return True

        except Exception as e:
            数据日志器.error(f"删除知识库失败: {str(e)}")
            raise

    async def _验证查询优化模型id(self, 模型id: int) -> bool:
        """验证查询优化模型id是否存在"""
        try:
            模型验证SQL = """
            SELECT id FROM langchain_模型配置表
            WHERE id = $1
            """
            模型结果 = await self.数据库连接池.执行查询(模型验证SQL, (模型id,))
            return bool(模型结果)
        except Exception:
            return False

    async def 更新知识库查询优化配置(
        self, 知识id: int, 查询优化配置: Dict[str, Any]
    ) -> bool:
        """更新知识库的查询优化配置"""
        try:
            # 构建动态更新SQL
            更新字段 = []
            参数值 = []
            参数索引 = 1

            # 查询优化配置字段映射
            配置字段映射 = {
                "默认启用查询优化": "默认启用查询优化",
                "默认查询优化策略": "默认查询优化策略",
                "默认查询优化模型id": "默认查询优化模型id",
                "默认查询优化提示词": "默认查询优化提示词",
            }

            for 字段名, 数据库字段 in 配置字段映射.items():
                if 字段名 in 查询优化配置:
                    值 = 查询优化配置[字段名]

                    # 特殊处理优化模型id字段：验证模型id是否存在
                    if 字段名 == "默认查询优化模型id" and 值 is not None:
                        try:
                            值 = int(值)
                            if not await self._验证查询优化模型id(值):
                                数据日志器.warning(
                                    f"查询优化模型id不存在或未启用: {值}"
                                )
                                continue  # 跳过无效的模型id
                        except (ValueError, TypeError):
                            数据日志器.warning(f"无效的查询优化模型id: {值}")
                            continue

                    更新字段.append(f"{数据库字段} = ${参数索引}")
                    参数值.append(值)
                    参数索引 += 1

            if not 更新字段:
                数据日志器.warning("没有有效的查询优化配置字段需要更新")
                return False

            # 添加知识id到参数
            参数值.append(知识id)

            # 执行更新
            更新SQL = f"""
            UPDATE langchain_知识库表
            SET {", ".join(更新字段)}
            WHERE id = $1
            """

            影响行数 = await self.数据库连接池.执行数据库更新(更新SQL, tuple(参数值))

            if 影响行数 > 0:
                数据日志器.info(f"更新知识库查询优化配置成功: {知识id}")
                return True
            else:
                数据日志器.warning(f"知识库不存在或无需更新: {知识id}")
                return False

        except Exception as e:
            数据日志器.error(f"更新知识库查询优化配置失败: {str(e)}")
            raise

    async def 克隆知识库(
        self, 源知识id: int, 新知识库名称: str, 创建者ID: int
    ) -> Optional[int]:
        """克隆知识库"""
        try:
            # 获取源知识库信息
            源知识库 = await self.获取知识库详情(源知识id)
            if not 源知识库:
                数据日志器.error(f"源知识库不存在: {源知识id}")
                return None

            # 创建新知识库
            新知识库数据 = {
                "知识库名称": 新知识库名称,
                "知识库描述": f"克隆自: {源知识库['知识库名称']}",
                "创建者id": 创建者ID,
                "组织id": 源知识库.get("组织id", 1),
                "嵌入模型": 源知识库["嵌入模型"],
                "配置信息": 源知识库["配置信息"],
            }

            新知识id = await self.创建知识库(新知识库数据)

            if 新知识id:
                数据日志器.info(f"克隆知识库成功: {源知识id} -> {新知识id}")
                return 新知识id
            else:
                数据日志器.error("克隆知识库失败: 创建新知识库返回空ID")
                return None

        except Exception as e:
            数据日志器.error(f"克隆知识库失败: {str(e)}")
            raise

    # ==================== 智能体分配管理数据操作 ====================

    # ==================== 用户搜索数据操作 ====================

    # 已移除重复的搜索用户函数，现在使用统一的 Postgre_搜索用户 函数

    # ==================== 智能体配置更新数据操作 ====================

    # ==================== 冗余的配置信息相关方法已删除 ====================
    # 现在使用独立字段：系统提示词、用户提示词模板、是否公开、自动编译、版本控制等

    # ==================== 知识库管理数据操作 ====================

    async def 获取知识库列表(
        self,
        筛选条件: Optional[Dict[str, Any]] = None,
        分页参数: Optional[Dict[str, Any]] = None,
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取知识库列表（支持筛选和分页）"""
        try:
            # 构建查询条件
            查询条件 = []
            查询参数 = []

            # 默认过滤掉已删除的知识库
            查询条件.append("kb.知识库状态 != $1")
            查询参数.append("已删除")

            # 环境过滤已移除 - 显示所有知识库

            if 筛选条件:
                if 筛选条件.get("搜索关键词"):
                    # {{ AURA-X: Modify - 修复PostgreSQL参数占位符索引错误. Approval: 寸止(ID:1735372800). }}
                    # {{ Source: PostgreSQL参数占位符最佳实践 }}
                    base_index = len(查询参数)
                    查询条件.append(
                        f"(kb.知识库名称 LIKE ${base_index + 1} OR kb.知识库描述 LIKE ${base_index + 2})"
                    )
                    关键词模式 = f"%{筛选条件['搜索关键词']}%"
                    查询参数.extend([关键词模式, 关键词模式])

                if 筛选条件.get("知识库类型"):
                    # 如果指定了知识库类型，则覆盖默认的状态过滤
                    # 找到并移除状态过滤条件
                    新查询条件 = []
                    新查询参数 = []
                    for i, 条件 in enumerate(查询条件):
                        if "kb.知识库状态 !=" not in 条件:
                            新查询条件.append(条件)
                            if i < len(查询参数):
                                新查询参数.append(查询参数[i])

                    查询条件 = 新查询条件
                    查询参数 = 新查询参数
                    查询条件.append(f"kb.知识库状态 = ${len(查询参数) + 1}")
                    查询参数.append(筛选条件["知识库类型"])

                if 筛选条件.get("创建者id"):
                    查询条件.append(f"kb.用户表id = ${len(查询参数) + 1}")
                    查询参数.append(筛选条件["创建者id"])

                if 筛选条件.get("是否公开") is not None:
                    下一个参数位置 = len(查询参数) + 1
                    查询条件.append(f"kb.是否公开 = ${下一个参数位置}")
                    查询参数.append(筛选条件["是否公开"])

            # 构建WHERE子句
            where_clause = "WHERE " + " AND ".join(查询条件) if 查询条件 else ""

            # 查询总数
            计数SQL = f"""
            SELECT COUNT(*) as total
            FROM langchain_知识库表 kb
            LEFT JOIN 用户表 u ON kb.用户表id = u.id
            {where_clause}
            """

            计数结果 = await self.数据库连接池.执行查询(计数SQL, tuple(查询参数))
            总数量 = 计数结果[0]["total"] if 计数结果 else 0

            # 查询列表数据
            if 分页参数:
                页码 = 分页参数.get("页码", 1)
                每页数量 = 分页参数.get("每页数量", 20)
                偏移量 = (页码 - 1) * 每页数量

                # 动态计算分页参数位置
                limit_参数位置 = len(查询参数) + 1
                offset_参数位置 = len(查询参数) + 2

                列表SQL = f"""
                SELECT
                    kb.id,
                    kb.知识库名称,
                    kb.知识库描述,
                    kb.知识库状态,
                    kb.文档数量,
                    kb.嵌入模型,
                    em.显示名称 as 嵌入模型名称,
                    em.提供商 as 嵌入模型提供商,
                    kb.是否公开,
                    kb.向量存储类型,
                    kb.存储类型,
                    kb.创建时间,
                    kb.更新时间,
                    kb.用户表id,
                    u.昵称
                FROM langchain_知识库表 kb
                LEFT JOIN 用户表 u ON kb.用户表id = u.id
                LEFT JOIN langchain_模型配置表 em ON kb.嵌入模型 = em.id
                {where_clause}
                ORDER BY kb.创建时间 DESC
                LIMIT ${limit_参数位置} OFFSET ${offset_参数位置}
                """
                查询参数.extend([每页数量, 偏移量])
            else:
                列表SQL = f"""
                SELECT
                    kb.id,
                    kb.知识库名称,
                    kb.知识库描述,
                    kb.知识库状态,
                    kb.文档数量,
                    kb.嵌入模型,
                    em.显示名称 as 嵌入模型名称,
                    em.提供商 as 嵌入模型提供商,
                    kb.是否公开,
                    kb.向量存储类型,
                    kb.存储类型,
                    kb.创建时间,
                    kb.更新时间,
                    kb.用户表id,
                    u.昵称
                FROM langchain_知识库表 kb
                LEFT JOIN 用户表 u ON kb.用户表id = u.id
                LEFT JOIN langchain_模型配置表 em ON kb.嵌入模型 = em.id
                {where_clause}
                ORDER BY kb.创建时间 DESC
                """

            知识库列表 = await self.数据库连接池.执行查询(列表SQL, tuple(查询参数))

            数据日志器.info(
                f"获取知识库列表成功，总数: {总数量}，返回: {len(知识库列表)}"
            )
            return 知识库列表, 总数量

        except Exception as e:
            数据日志器.error(f"获取知识库列表失败: {str(e)}")
            raise

    async def 获取用户可访问知识库列表(
        self,
        用户id: int,
        分页参数: Optional[Dict[str, Any]] = None,
        搜索关键词: Optional[str] = None,
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取用户可访问的知识库列表（我创建的 OR 公开的）

        仅返回必要字段供下拉选择，避免冗余。
        """
        try:
            查询条件 = ["kb.知识库状态 != $1", "(kb.用户表id = $2 OR kb.是否公开 = true)"]
            查询参数: List[Any] = ["已删除", 用户id]

            if 搜索关键词 and 搜索关键词.strip():
                下一个 = len(查询参数) + 1
                查询条件.append(
                    f"(kb.知识库名称 LIKE ${下一个} OR kb.知识库描述 LIKE ${下一个 + 1})"
                )
                关键词 = f"%{搜索关键词.strip()}%"
                查询参数.extend([关键词, 关键词])

            where_clause = "WHERE " + " AND ".join(查询条件)

            计数SQL = f"""
            SELECT COUNT(*) as total
            FROM langchain_知识库表 kb
            {where_clause}
            """
            计数结果 = await self.数据库连接池.执行查询(计数SQL, tuple(查询参数))
            总数量 = 计数结果[0]["total"] if 计数结果 else 0

            # 分页
            if 分页参数:
                页码 = 分页参数.get("页码", 1)
                每页数量 = 分页参数.get("每页数量", 20)
                偏移量 = (页码 - 1) * 每页数量
                limit_pos = len(查询参数) + 1
                offset_pos = len(查询参数) + 2
                列表SQL = f"""
                SELECT kb.id, kb.知识库名称, kb.知识库描述
                FROM langchain_知识库表 kb
                {where_clause}
                ORDER BY kb.创建时间 DESC
                LIMIT ${limit_pos} OFFSET ${offset_pos}
                """
                查询参数.extend([每页数量, 偏移量])
            else:
                列表SQL = f"""
                SELECT kb.id, kb.知识库名称, kb.知识库描述
                FROM langchain_知识库表 kb
                {where_clause}
                ORDER BY kb.创建时间 DESC
                """

            列表 = await self.数据库连接池.执行查询(列表SQL, tuple(查询参数))
            return list(列表) if 列表 else [], 总数量

        except Exception as e:
            数据日志器.error(f"获取用户可访问知识库列表失败: {str(e)}")
            raise

    # ==================== 文档管理数据操作 ====================

    async def 检查文档MD5重复(
        self, 知识库id: int, md5值: str
    ) -> Optional[Dict[str, Any]]:
        """检查知识库中是否存在相同MD5的文档"""
        try:
            查询SQL = """
            SELECT uuid, 文档名称, 文档类型, 文档大小, 文档状态, 向量状态, 创建时间
            FROM langchain_知识库文档表
            WHERE langchain_知识库表id = $1 AND md5 = $2
            LIMIT 1
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (知识库id, md5值))

            if 结果:
                文档信息 = 结果[0]
                数据日志器.info(
                    f"发现重复文档: MD5={md5值[:8]}..., 文档名称={文档信息['文档名称']}"
                )
                return 文档信息
            else:
                return None

        except Exception as e:
            数据日志器.error(f"检查MD5重复失败: {str(e)}")
            return None

    async def 创建知识库文档(self, 文档数据: Dict[str, Any]) -> Optional[int]:
        """创建知识库文档记录"""
        try:
            插入SQL = """
            INSERT INTO langchain_知识库文档表
            (文档uuid, langchain_知识库表id, 用户文件路径, 文档名称, 文档类型, 文档大小,
             文档内容, 向量分块数量, 向量状态, 元数据, 文档状态, md5, 创建时间, 更新时间)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW(), NOW())
            RETURNING id
            """

            参数 = (
                文档数据.get("文档uuid", ""),
                文档数据.get("langchain_知识库表id", 0),
                文档数据.get("文档路径", ""),
                文档数据.get("文档名称", ""),
                文档数据.get("文档类型", ""),
                文档数据.get("文档大小", 0),
                文档数据.get("文档内容", ""),
                文档数据.get("向量分块数量", 0),  # 直接使用表字段
                文档数据.get("向量状态", "待处理"),  # 直接使用表字段
                json.dumps(文档数据.get("元数据", {}), ensure_ascii=False),
                文档数据.get("文档状态", "已处理"),
                文档数据.get("md5", ""),  # 添加MD5字段
            )

            数据日志器.debug(f"执行插入SQL: {插入SQL}")
            数据日志器.debug(f"插入参数: {参数}")
            文档记录id = await self.数据库连接池.执行插入并返回id(插入SQL, 参数)
            数据日志器.debug(f"获取到的文档记录id: {文档记录id}")

            if not 文档记录id:
                数据日志器.error("创建知识库文档失败：未返回有效的文档记录id")
                raise Exception("创建知识库文档失败：数据库插入未返回有效ID")

            # 更新知识库文档数量统计
            知识id = 文档数据.get("langchain_知识库表id", 0)
            if 知识id:
                await self._更新知识库统计(知识id)

            数据日志器.info(
                f"知识库文档创建成功: {文档数据.get('文档名称', '')} (ID: {文档记录id})"
            )
            return 文档记录id

        except Exception as e:
            数据日志器.error(f"创建知识库文档失败: {str(e)}")
            raise

    async def 批量创建向量记录占位(self, 文档记录id: int, 分块数量: int) -> List[int]:
        """批量创建向量记录占位符 - 优化批量插入"""
        try:
            if 分块数量 == 0:
                return []

            # 构建批量插入SQL
            占位符元数据 = json.dumps({"状态": "占位符"}, ensure_ascii=False)

            # 生成VALUES子句
            values_子句 = []
            参数列表 = []
            参数索引 = 1

            for i in range(分块数量):
                values_子句.append(
                    f"(${参数索引}, ${参数索引 + 1}, ${参数索引 + 2}, ${参数索引 + 3}, ${参数索引 + 4}, NOW())"
                )
                参数列表.extend([文档记录id, i, "", 0, 占位符元数据])
                参数索引 += 5

            批量插入SQL = f"""
            INSERT INTO langchain_文档向量表
            (langchain_知识库文档表id, 分块序号, 分块内容, 向量维度, 元数据, 创建时间)
            VALUES {", ".join(values_子句)}
            RETURNING id
            """

            结果 = await self.数据库连接池.执行查询(批量插入SQL, 参数列表)
            向量ID列表 = [记录["id"] for 记录 in 结果] if 结果 else []

            数据日志器.info(f"批量创建向量记录占位符成功: {len(向量ID列表)} 个")
            return 向量ID列表

        except Exception as e:
            数据日志器.error(f"批量创建向量记录占位符失败: {str(e)}")
            raise

    async def 获取知识库文档列表(
        self,
        知识id: int,
        分页参数: Optional[Dict[str, Any]] = None,
        筛选条件: Optional[Dict[str, Any]] = None,
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取知识库的文档列表"""
        try:
            # 构建查询条件
            查询条件 = "WHERE langchain_知识库表id = $1"
            查询参数: List[Any] = [知识id]

            # 添加筛选条件
            if 筛选条件:
                if 筛选条件.get("搜索关键字"):
                    下一个参数位置 = len(查询参数) + 1
                    查询条件 += f" AND 文档名称 LIKE ${下一个参数位置}"
                    查询参数.append(f"%{筛选条件['搜索关键字']}%")

                if 筛选条件.get("文件类型"):
                    下一个参数位置 = len(查询参数) + 1
                    查询条件 += f" AND 文档类型 = ${下一个参数位置}"
                    查询参数.append(筛选条件["文件类型"])

                if 筛选条件.get("处理状态"):
                    下一个参数位置 = len(查询参数) + 1
                    查询条件 += f" AND 文档状态 = ${下一个参数位置}"
                    查询参数.append(筛选条件["处理状态"])

            # 查询总数
            计数SQL = f"""
            SELECT COUNT(*) as total
            FROM langchain_知识库文档表
            {查询条件}
            """

            计数结果 = await self.数据库连接池.执行查询(计数SQL, tuple(查询参数))
            总数量 = 计数结果[0]["total"] if 计数结果 else 0

            # 查询列表数据
            if 分页参数:
                页码 = 分页参数.get("页码", 1)
                每页数量 = 分页参数.get("每页数量", 20)
                偏移量 = (页码 - 1) * 每页数量

                数据日志器.debug(
                    f"分页参数: 页码={页码}, 每页数量={每页数量}, 偏移量={偏移量}"
                )

                # 动态计算分页参数位置
                limit_参数位置 = len(查询参数) + 1
                offset_参数位置 = len(查询参数) + 2

                列表SQL = f"""
                SELECT
                    uuid as 文档uuid, langchain_知识库表id, 用户文件路径, 文档名称, 文档类型,
                    文档大小, 文档内容, 元数据, 文档状态, 向量状态, 向量分块数量, 最后向量化时间, 错误信息, 创建时间, 更新时间
                FROM langchain_知识库文档表
                {查询条件}
                ORDER BY 创建时间 DESC
                LIMIT ${limit_参数位置} OFFSET ${offset_参数位置}
                """
                查询参数.extend([每页数量, 偏移量])
            else:
                列表SQL = f"""
                SELECT
                    uuid as 文档uuid, langchain_知识库表id, 用户文件路径, 文档名称, 文档类型,
                    文档大小, 文档内容, 元数据, 文档状态, 向量状态, 向量分块数量, 最后向量化时间, 错误信息, 创建时间, 更新时间
                FROM langchain_知识库文档表
                {查询条件}
                ORDER BY 创建时间 DESC
                """

            数据日志器.debug(f"执行SQL查询: {列表SQL}")
            数据日志器.debug(f"查询参数: {查询参数}")

            文档列表 = await self.数据库连接池.执行查询(列表SQL, tuple(查询参数))

            数据日志器.info(f"获取知识库文档列表成功，知识id: {知识id}，总数: {总数量}")
            return 文档列表, 总数量

        except Exception as e:
            数据日志器.error(f"获取知识库文档列表失败: {str(e)}")
            raise

    async def 更新文档向量状态(self, 文档记录id: int, 向量状态: str) -> bool:
        """更新文档的向量状态"""
        try:
            # 直接更新向量状态字段和相关时间字段
            更新SQL = """
            UPDATE langchain_知识库文档表
            SET 向量状态 = $1, 最后向量化时间 = NOW(), 更新时间 = NOW()
            WHERE id = $2
            """

            影响行数 = await self.数据库连接池.执行更新(
                更新SQL, (向量状态, 文档记录id)
            )

            if 影响行数 > 0:
                数据日志器.info(
                    f"更新文档向量状态成功: ID={文档记录id}, 状态={向量状态}"
                )
                return True
            else:
                数据日志器.warning(f"更新文档向量状态失败: ID={文档记录id}")
                return False

        except Exception as e:
            数据日志器.error(f"更新文档向量状态失败: {str(e)}")
            return False

    async def 创建文档向量(self, 向量数据: Dict[str, Any]) -> Optional[int]:
        """创建文档向量记录 - 优化后的结构"""
        try:
            插入SQL = """
            INSERT INTO langchain_文档向量表
            (langchain_知识库文档表id, 分块序号, 分块内容, 向量维度, 元数据, 向量数据, 创建时间)
            VALUES ($1, $2, $3, $4, $5, $6, NOW())
            """

            # 处理向量数据
            向量数组 = 向量数据.get("向量数据")
            if 向量数组 and isinstance(向量数组, list):
                # 转换为PostgreSQL向量格式
                向量字符串 = f"[{','.join(map(str, 向量数组))}]"
            else:
                向量字符串 = None

            参数 = (
                向量数据.get("langchain_知识库文档表id"),
                向量数据.get("分块序号", 0),
                向量数据.get("分块内容", ""),
                向量数据.get("向量维度", 0),
                json.dumps(向量数据.get("元数据", {}), ensure_ascii=False),
                向量字符串,
            )

            向量记录id = await self.数据库连接池.执行插入(插入SQL, 参数)

            if 向量记录id:
                数据日志器.debug(f"创建文档向量成功: ID={向量记录id}")
                return 向量记录id
            else:
                数据日志器.error("创建文档向量失败: 未返回有效ID")
                return None

        except Exception as e:
            数据日志器.error(f"创建文档向量失败: {str(e)}")
            return None

    async def 批量创建文档向量(self, 向量数据列表: List[Dict[str, Any]]) -> List[int]:
        """批量创建文档向量记录 - 性能优化版本"""
        try:
            if not 向量数据列表:
                return []

            # 构建批量插入SQL
            values_子句 = []
            参数列表 = []
            参数索引 = 1

            for 向量数据 in 向量数据列表:
                # 处理向量数据
                向量数组 = 向量数据.get("向量数据")
                if 向量数组 and isinstance(向量数组, list):
                    向量字符串 = f"[{','.join(map(str, 向量数组))}]"
                else:
                    向量字符串 = None

                values_子句.append(
                    f"(${参数索引}, ${参数索引 + 1}, ${参数索引 + 2}, ${参数索引 + 3}, ${参数索引 + 4}, ${参数索引 + 5}, NOW())"
                )
                参数列表.extend(
                    [
                        向量数据.get(
                            "langchain_知识库文档表id", 向量数据.get("文档id", 0)
                        ),
                        向量数据.get("分块序号", 0),
                        向量数据.get("分块内容", ""),
                        向量数据.get("向量维度", 0),
                        json.dumps(向量数据.get("元数据", {}), ensure_ascii=False),
                        向量字符串,
                    ]
                )
                参数索引 += 6

            批量插入SQL = f"""
            INSERT INTO langchain_文档向量表
            (langchain_知识库文档表id, 分块序号, 分块内容, 向量维度, 元数据, 向量数据, 创建时间)
            VALUES {", ".join(values_子句)}
            RETURNING id
            """

            结果 = await self.数据库连接池.执行查询(批量插入SQL, 参数列表)
            向量ID列表 = [记录["id"] for 记录 in 结果] if 结果 else []

            数据日志器.info(
                f"批量创建文档向量成功: {len(向量ID列表)}/{len(向量数据列表)} 个"
            )
            return 向量ID列表

        except Exception as e:
            数据日志器.error(f"批量创建文档向量失败: {str(e)}")
            return []

    async def 通过UUID删除知识库文档(self, 文档UUID: str) -> bool:
        """通过UUID删除知识库文档 - 先删除向量记录再删除文档记录"""
        try:
            # 先获取文档id
            查询文档id_SQL = "SELECT uuid FROM langchain_知识库文档表 WHERE uuid = $1"
            文档记录 = await self.数据库连接池.执行查询(查询文档id_SQL, (文档UUID,))

            if not 文档记录:
                数据日志器.warning(f"文档不存在: {文档UUID}")
                return False

            文档UUID值 = 文档记录[0]["uuid"]

            # 使用事务确保数据一致性
            async with self.数据库连接池.获取连接() as 连接:
                async with 连接.transaction():
                    # 先删除向量化任务记录
                    删除任务SQL = """
                    DELETE FROM langchain_向量化任务表
                    WHERE 知识库文档表id = (SELECT id FROM langchain_知识库文档表 WHERE uuid = $1)
                    """
                    任务删除结果 = await 连接.execute(删除任务SQL, 文档UUID值)

                    # 再删除向量数据
                    删除向量SQL = """
                    DELETE FROM langchain_文档向量表
                    WHERE langchain_知识库文档表id = (SELECT id FROM langchain_知识库文档表 WHERE uuid = $1)
                    """
                    await 连接.execute(删除向量SQL, 文档UUID值)

                    # 最后删除文档记录
                    删除文档SQL = "DELETE FROM langchain_知识库文档表 WHERE uuid = $1"
                    await 连接.execute(删除文档SQL, 文档UUID)

            数据日志器.info(f"通过UUID删除知识库文档成功: {文档UUID}")
            return True

        except Exception as e:
            数据日志器.error(f"通过UUID删除知识库文档失败: {str(e)}")
            raise

    async def 验证文档向量状态一致性(self, 文档记录id: int) -> Dict[str, Any]:
        """验证文档向量状态一致性 - 检查文档状态与实际向量数据的一致性"""
        try:
            验证SQL = """
            SELECT
                d.id as 文档id,
                d.文档名称,
                d.向量状态 as 文档向量状态,
                d.向量分块数量 as 文档分块数量,
                d.文档状态,
                COUNT(v.id) as 实际向量数量,
                COUNT(CASE WHEN v.向量数据 IS NOT NULL THEN 1 END) as 有效向量数量,
                t.任务状态,
                t.已完成分块数,
                t.失败分块数
            FROM langchain_知识库文档表 d
            LEFT JOIN langchain_文档向量表 v ON d.id = v.langchain_知识库文档表id
            LEFT JOIN langchain_向量化任务表 t ON d.id = t.知识库文档表id
            WHERE d.id = $1
            GROUP BY d.id, d.文档名称, d.向量状态, d.向量分块数量, d.文档状态, t.任务状态, t.已完成分块数, t.失败分块数
            """

            结果 = await self.数据库连接池.执行查询(验证SQL, (文档记录id,))

            if not 结果:
                return {"success": False, "error": "文档不存在"}

            文档信息 = 结果[0]

            # 检查一致性
            一致性检查 = {
                "文档id": 文档信息["文档id"],
                "文档名称": 文档信息["文档名称"],
                "文档向量状态": 文档信息["文档向量状态"],
                "文档分块数量": 文档信息["文档分块数量"],
                "实际向量数量": 文档信息["实际向量数量"],
                "有效向量数量": 文档信息["有效向量数量"],
                "任务状态": 文档信息["任务状态"],
                "任务完成数": 文档信息["已完成分块数"],
                "任务失败数": 文档信息["失败分块数"],
                "状态一致": True,
                "不一致项": [],
            }

            # 检查文档分块数量与实际向量数量
            if 文档信息["文档分块数量"] != 文档信息["实际向量数量"]:
                一致性检查["状态一致"] = False
                一致性检查["不一致项"].append(
                    f"分块数量不匹配: 文档记录={文档信息['文档分块数量']}, 实际向量={文档信息['实际向量数量']}"
                )

            # 检查任务状态与文档状态
            if 文档信息["任务状态"] and 文档信息["任务完成数"]:
                if 文档信息["任务完成数"] != 文档信息["有效向量数量"]:
                    一致性检查["状态一致"] = False
                    一致性检查["不一致项"].append(
                        f"任务完成数与有效向量数不匹配: 任务={文档信息['任务完成数']}, 有效向量={文档信息['有效向量数量']}"
                    )

            # 根据实际情况建议正确状态
            if 文档信息["有效向量数量"] == 0:
                建议状态 = "失败"
            elif 文档信息["有效向量数量"] == 文档信息["实际向量数量"]:
                建议状态 = "已完成"
            else:
                成功率 = 文档信息["有效向量数量"] / 文档信息["实际向量数量"]
                if 成功率 >= 0.8:
                    建议状态 = "基本完成"
                else:
                    建议状态 = "部分失败"

            一致性检查["建议状态"] = 建议状态
            一致性检查["当前状态正确"] = 文档信息["文档向量状态"] == 建议状态

            数据日志器.debug(f"文档向量状态一致性检查完成: {一致性检查}")
            return {"success": True, "data": 一致性检查}

        except Exception as e:
            数据日志器.error(f"验证文档向量状态一致性失败: {str(e)}")
            return {"success": False, "error": str(e)}

    async def 通过ID删除知识库文档(self, 文档记录id: int) -> bool:
        """通过数字ID删除知识库文档 - 先删除向量记录再删除文档记录"""
        try:
            # 使用事务确保数据一致性
            async with self.数据库连接池.获取连接() as 连接:
                async with 连接.transaction():
                    # 先删除向量化任务记录
                    删除任务SQL = """
                    DELETE FROM langchain_向量化任务表
                    WHERE 知识库文档表id = $1
                    """
                    任务删除结果 = await 连接.execute(删除任务SQL, 文档记录id)

                    # 再删除向量数据
                    删除向量SQL = """
                    DELETE FROM langchain_文档向量表
                    WHERE langchain_知识库文档表id = $1
                    """
                    向量删除结果 = await 连接.execute(删除向量SQL, 文档记录id)

                    # 最后删除文档记录
                    删除文档SQL = "DELETE FROM langchain_知识库文档表 WHERE id = $1"
                    文档删除结果 = await 连接.execute(删除文档SQL, 文档记录id)

            数据日志器.info(f"通过ID删除知识库文档成功: {文档记录id}")
            return True

        except Exception as e:
            数据日志器.error(f"通过ID删除知识库文档失败: {str(e)}")
            raise

    async def 更新文档状态(
        self, 文档UUID: str, 状态: str, 错误信息: Optional[str] = None
    ) -> bool:
        """更新文档状态"""
        try:
            if 错误信息:
                更新SQL = """
            UPDATE langchain_知识库文档表
            SET 文档状态 = $1, 错误信息 = $2, 更新时间 = NOW()
            WHERE uuid = $3
                """
                参数 = (状态, 错误信息, 文档UUID)
            else:
                更新SQL = """
            UPDATE langchain_知识库文档表
            SET 文档状态 = $1, 更新时间 = NOW()
            WHERE uuid = $2
                """
                参数 = (状态, 文档UUID)

            await self.数据库连接池.执行更新(更新SQL, 参数)

            数据日志器.info(f"更新文档状态成功: {文档UUID} -> {状态}")
            return True

        except Exception as e:
            数据日志器.error(f"更新文档状态失败: {str(e)}")
            raise

    async def 通过ID更新文档状态(
        self, 文档id: int, 状态: str, 错误信息: Optional[str] = None
    ) -> bool:
        """通过文档id更新文档状态"""
        try:
            if 错误信息:
                更新SQL = """
                UPDATE langchain_知识库文档表
                SET 文档状态 = $1, 错误信息 = $2, 更新时间 = NOW()
                WHERE id = $3
                """
                参数 = (状态, 错误信息, 文档id)
            else:
                更新SQL = """
                UPDATE langchain_知识库文档表
                SET 文档状态 = $1, 更新时间 = NOW()
                WHERE id = $2
                """
                参数 = (状态, 文档id)

            影响行数 = await self.数据库连接池.执行更新(更新SQL, 参数)

            if 影响行数 > 0:
                数据日志器.info(f"通过ID更新文档状态成功: ID={文档id} -> {状态}")
                return True
            else:
                数据日志器.warning(f"通过ID更新文档状态失败，文档不存在: ID={文档id}")
                return False

        except Exception as e:
            数据日志器.error(f"通过ID更新文档状态失败: {str(e)}")
            return False

    async def 删除知识库文档(self, 文档id: int) -> bool:
        """删除知识库文档记录（用于清理失败的上传）- 先删除向量记录再删除文档记录"""
        try:
            # 使用事务确保数据一致性
            async with self.数据库连接池.获取连接() as 连接:
                async with 连接.transaction():
                    # 先删除向量化任务记录
                    删除任务SQL = """
                    DELETE FROM langchain_向量化任务表
                    WHERE 知识库文档表id = $1
                    """
                    任务删除结果 = await 连接.execute(删除任务SQL, 文档id)

                    # 再删除向量数据
                    删除向量SQL = """
                    DELETE FROM langchain_文档向量表
                    WHERE langchain_知识库文档表id = $1
                    """
                    向量删除结果 = await 连接.execute(删除向量SQL, 文档id)

                    # 最后删除文档记录
                    删除文档SQL = """
                    DELETE FROM langchain_知识库文档表
                    WHERE id = $1
                    """
                    文档删除结果 = await 连接.execute(删除文档SQL, 文档id)

            # 检查文档删除结果（PostgreSQL execute返回类似"DELETE 1"的字符串）
            删除行数 = int(文档删除结果.split()[-1]) if 文档删除结果 else 0
            if 删除行数 > 0:
                数据日志器.info(f"删除知识库文档成功: ID={文档id}")
                return True
            else:
                数据日志器.warning(f"删除知识库文档失败，文档不存在: ID={文档id}")
                return False

        except Exception as e:
            数据日志器.error(f"删除知识库文档失败: {str(e)}")
            return False

    async def 通过UUID获取文档详情(self, 文档UUID: str) -> Optional[Dict[str, Any]]:
        """获取文档详情（通过UUID）"""
        try:
            查询SQL = """
            SELECT
                uuid, langchain_知识库表id, 文档名称, 用户文件路径 as 文档路径, 文档类型, 文档大小,
                文档内容, 向量分块数量, 文档状态, 错误信息, 元数据,
                创建时间, 更新时间
            FROM langchain_知识库文档表
            WHERE uuid = $1
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (文档UUID,))

            if 结果:
                文档信息 = 结果[0]
                # 解析JSON元数据
                if 文档信息.get("元数据"):
                    try:
                        文档信息["元数据"] = json.loads(文档信息["元数据"])
                    except (json.JSONDecodeError, TypeError):
                        文档信息["元数据"] = {}

                数据日志器.info(f"获取文档详情成功: {文档UUID}")
                return 文档信息
            else:
                数据日志器.warning(f"文档不存在: {文档UUID}")
                return None

        except Exception as e:
            数据日志器.error(f"获取文档详情失败: {str(e)}")
            raise

    async def 更新文档内容(self, 文档UUID: str, 更新数据: Dict[str, Any]) -> bool:
        """更新文档内容"""
        try:
            # 构建动态更新SQL
            更新字段 = []
            参数值 = []

            字段映射 = {
                "文档名称": "文档名称",
                "文档内容": "文档内容",
                "分块数量": "分块数量",
                "状态": "状态",
                "错误信息": "错误信息",
                "元数据": "元数据",
            }

            for 字段名, 数据库字段 in 字段映射.items():
                if 字段名 in 更新数据:
                    # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
                    # {{ Source: PostgreSQL参数占位符文档 }}
                    更新字段.append(f"{数据库字段} = ${len(参数值) + 1}")
                    if 字段名 == "元数据":
                        参数值.append(json.dumps(更新数据[字段名], ensure_ascii=False))
                    else:
                        参数值.append(更新数据[字段名])

            if not 更新字段:
                数据日志器.warning("没有需要更新的字段")
                return False

            更新字段.append("更新时间 = NOW()")
            # 注意：NOW() 不需要参数，所以不要添加到参数值列表中

            更新SQL = f"""
            UPDATE langchain_知识库文档表
            SET {", ".join(更新字段)}
            WHERE uuid = ${len(参数值) + 1}
            """

            # 添加文档UUID作为WHERE条件的参数
            参数值.append(文档UUID)

            await self.数据库连接池.执行更新(更新SQL, tuple(参数值))

            数据日志器.info(f"更新文档内容成功: {文档UUID}")
            return True

        except Exception as e:
            数据日志器.error(f"更新文档内容失败: {str(e)}")
            raise

    async def 通过UUID批量删除文档(self, 文档UUID列表: List[str]) -> Tuple[int, int]:
        """通过UUID批量删除文档 - 调用单个删除方法确保外键约束处理"""
        try:
            成功数量 = 0
            失败数量 = 0

            for 文档UUID in 文档UUID列表:
                try:
                    # 调用已修复的单个删除方法
                    删除成功 = await self.通过UUID删除知识库文档(文档UUID)
                    if 删除成功:
                        成功数量 += 1
                        数据日志器.info(f"通过UUID删除文档成功: {文档UUID}")
                    else:
                        失败数量 += 1
                        数据日志器.warning(
                            f"通过UUID删除文档失败，文档不存在: {文档UUID}"
                        )
                except Exception as e:
                    失败数量 += 1
                    数据日志器.error(
                        f"通过UUID删除文档失败: {文档UUID}, 错误: {str(e)}"
                    )

            数据日志器.info(
                f"通过UUID批量删除文档完成，成功: {成功数量}, 失败: {失败数量}"
            )
            return 成功数量, 失败数量

        except Exception as e:
            数据日志器.error(f"通过UUID批量删除文档失败: {str(e)}")
            raise

    async def 通过ID批量删除文档(self, 文档id列表: List[int]) -> Tuple[int, int]:
        """通过数字ID批量删除文档 - 调用单个删除方法确保外键约束处理"""
        try:
            成功数量 = 0
            失败数量 = 0

            for 文档id in 文档id列表:
                try:
                    # 调用已修复的单个删除方法
                    删除成功 = await self.通过ID删除知识库文档(文档id)
                    if 删除成功:
                        成功数量 += 1
                        数据日志器.info(f"通过ID删除文档成功: {文档id}")
                    else:
                        失败数量 += 1
                        数据日志器.warning(f"通过ID删除文档失败，文档不存在: {文档id}")
                except Exception as e:
                    失败数量 += 1
                    数据日志器.error(f"通过ID删除文档失败: {文档id}, 错误: {str(e)}")

            数据日志器.info(
                f"通过ID批量删除文档完成，成功: {成功数量}, 失败: {失败数量}"
            )
            return 成功数量, 失败数量

        except Exception as e:
            数据日志器.error(f"通过ID批量删除文档失败: {str(e)}")
            raise

    async def 获取知识库统计信息(self, 知识id: int) -> Dict[str, Any]:
        """获取知识库统计信息"""
        try:
            统计SQL = """
            SELECT
                COUNT(*) as 文档总数,
                SUM(CASE WHEN 文档状态 = '已处理' THEN 1 ELSE 0 END) as 已处理文档数,
                SUM(CASE WHEN 文档状态 = '处理中' THEN 1 ELSE 0 END) as 处理中文档数,
                SUM(CASE WHEN 文档状态 = '失败' THEN 1 ELSE 0 END) as 失败文档数,
                SUM(文档大小) as 总文件大小,
                SUM(向量分块数量) as 总分块数量,
                AVG(文档大小) as 平均文件大小
            FROM langchain_知识库文档表
            WHERE langchain_知识库表id = $1
            """

            结果 = await self.数据库连接池.执行查询(统计SQL, (知识id,))

            if 结果:
                统计信息 = 结果[0]
                # 处理None值
                for key, value in 统计信息.items():
                    if value is None:
                        统计信息[key] = 0

                数据日志器.info(f"获取知识库统计信息成功: {知识id}")
                return 统计信息
            else:
                return {
                    "文档总数": 0,
                    "已处理文档数": 0,
                    "处理中文档数": 0,
                    "失败文档数": 0,
                    "总文件大小": 0,
                    "总分块数量": 0,
                    "平均文件大小": 0,
                }

        except Exception as e:
            数据日志器.error(f"获取知识库统计信息失败: {str(e)}")
            raise

    async def 通过ID获取文档详情(self, 文档记录id: int) -> Optional[Dict[str, Any]]:
        """获取文档详情（通过记录id）"""
        try:
            查询SQL = """
            SELECT
                id, 文档uuid, langchain_知识库表id, 用户文件路径, 文档名称, 文档类型,
                文档大小, 文档内容, 向量分块数量, 元数据, 文档状态,
                创建时间, 更新时间
            FROM langchain_知识库文档表
            WHERE id = $1
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (文档记录id,))

            if 结果:
                文档信息 = 结果[0]
                # 解析JSON元数据
                if 文档信息.get("元数据"):
                    try:
                        文档信息["元数据"] = json.loads(文档信息["元数据"])
                    except (json.JSONDecodeError, TypeError):
                        文档信息["元数据"] = {}

                数据日志器.info(f"获取文档详情成功: {文档记录id}")
                return 文档信息
            else:
                数据日志器.warning(f"文档不存在: {文档记录id}")
                return None

        except Exception as e:
            数据日志器.error(f"获取文档详情失败: {str(e)}")
            raise

    async def 搜索文档(
        self, 知识id: int, 搜索关键词: str, 分页参数: Optional[Dict[str, Any]] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """在知识库中搜索文档"""
        try:
            # 构建搜索条件
            搜索条件 = "WHERE langchain_知识库表id = $1 AND (文档名称 LIKE $2 OR 文档内容 LIKE $3)"
            搜索参数 = [知识id, f"%{搜索关键词}%", f"%{搜索关键词}%"]

            # 查询总数
            计数SQL = f"""
            SELECT COUNT(*) as total
            FROM langchain_知识库文档表
            {搜索条件}
            """

            计数结果 = await self.数据库连接池.执行查询(计数SQL, tuple(搜索参数))
            总数量 = 计数结果[0]["total"] if 计数结果 else 0

            # 查询列表数据
            if 分页参数:
                页码 = 分页参数.get("页码", 1)
                每页数量 = 分页参数.get("每页数量", 20)
                偏移量 = (页码 - 1) * 每页数量

                列表SQL = f"""
                SELECT
                    id, 文档uuid, langchain_知识库表id as 知识id, 文档名称, 文档类型,
                    文档大小, 向量分块数量 as 分块数量, 文档状态 as 状态, 创建时间, 更新时间
                FROM langchain_知识库文档表
                {搜索条件}
                ORDER BY 创建时间 DESC
                LIMIT $4 OFFSET $5
                """
                搜索参数.extend([每页数量, 偏移量])
            else:
                列表SQL = f"""
                SELECT
                    id, 文档uuid, langchain_知识库表id as 知识id, 文档名称, 文档类型,
                    文档大小, 向量分块数量 as 分块数量, 文档状态 as 状态, 创建时间, 更新时间
                FROM langchain_知识库文档表
                {搜索条件}
                ORDER BY 创建时间 DESC
                """

            文档列表 = await self.数据库连接池.执行查询(列表SQL, tuple(搜索参数))

            数据日志器.info(
                f"搜索文档成功，知识id: {知识id}，关键词: {搜索关键词}，总数: {总数量}"
            )
            return 文档列表, 总数量

        except Exception as e:
            数据日志器.error(f"搜索文档失败: {str(e)}")
            raise

    async def 获取知识库文档分块(
        self, 知识id: int, 限制数量: int = 50
    ) -> List[Dict[str, Any]]:
        """获取知识库的文档分块用于检索测试"""
        try:
            # 正确的关联查询：langchain_文档向量表.langchain_知识库文档表id = langchain_知识库文档表.id
            查询SQL = """
            SELECT
                v.id,
                v.langchain_知识库文档表id as 文档记录id,
                v.分块内容,
                v.分块序号 as 分块位置,
                v.元数据,
                d.文档名称,
                d.文档uuid as 文档UUID
            FROM langchain_文档向量表 v
            INNER JOIN langchain_知识库文档表 d ON v.langchain_知识库文档表id = d.id
            WHERE d.langchain_知识库表id = $1
            AND v.分块内容 IS NOT NULL
            AND v.分块内容 != ''
            ORDER BY d.创建时间 DESC, v.分块序号 ASC
            LIMIT $2
            """

            分块列表 = await self.数据库连接池.执行查询(查询SQL, (知识id, 限制数量))

            # 处理元数据字段
            for 分块 in 分块列表:
                if 分块.get("元数据"):
                    try:
                        分块["元数据"] = json.loads(分块["元数据"])
                    except (json.JSONDecodeError, TypeError):
                        分块["元数据"] = {}
                else:
                    分块["元数据"] = {}

            数据日志器.info(
                f"获取知识库文档分块成功: 知识id {知识id}, 分块数量: {len(分块列表)}"
            )
            return 分块列表

        except Exception as e:
            数据日志器.error(f"获取知识库文档分块失败: {str(e)}")
            raise

    # 删除废弃的向量相似度检索方法和余弦相似度计算方法
    # 现在使用PostgreSQL向量存储进行向量检索，性能更好

    # ==================== PostgreSQL向量存储管理数据操作 ====================

    async def 更新知识库向量存储状态(self, 知识id: int, 向量存储状态: str) -> bool:
        """更新知识库状态"""
        try:
            更新SQL = """
            UPDATE langchain_知识库表
            SET 知识库状态 = $1, 更新时间 = NOW()
            WHERE id = $2
            """
            await self.数据库连接池.执行更新(更新SQL, (向量存储状态, 知识id))
            return True

        except Exception as e:
            数据日志器.error(f"更新知识库状态失败: {str(e)}")
            return False

    async def 获取知识库向量化状态(self, 知识id: int) -> Dict[str, Any]:
        """获取知识库向量化状态"""
        try:
            统计SQL = """
            SELECT
                COUNT(*) as 总文档数,
                SUM(CASE WHEN 文档状态 = '已处理' THEN 1 ELSE 0 END) as 已处理文档数,
                SUM(CASE WHEN 文档状态 = '处理中' THEN 1 ELSE 0 END) as 处理中文档数,
                SUM(CASE WHEN 文档状态 = '失败' THEN 1 ELSE 0 END) as 失败文档数,
                SUM(CASE WHEN 向量状态 = '已完成' THEN 1 ELSE 0 END) as 已向量化文档数,
                SUM(CASE WHEN 向量状态 = '处理中' THEN 1 ELSE 0 END) as 向量化处理中文档数,
                SUM(CASE WHEN 向量状态 = '失败' THEN 1 ELSE 0 END) as 向量化失败文档数,
                SUM(向量分块数量) as 总向量分块数
            FROM langchain_知识库文档表
            WHERE langchain_知识库表id = $1
            """

            结果 = await self.数据库连接池.执行查询(统计SQL, (知识id,))

            if 结果:
                统计数据 = 结果[0]
                return {
                    "总文档数": 统计数据.get("总文档数", 0),
                    "已处理文档数": 统计数据.get("已处理文档数", 0),
                    "处理中文档数": 统计数据.get("处理中文档数", 0),
                    "失败文档数": 统计数据.get("失败文档数", 0),
                    "已向量化文档数": 统计数据.get("已向量化文档数", 0),
                    "向量化处理中文档数": 统计数据.get("向量化处理中文档数", 0),
                    "向量化失败文档数": 统计数据.get("向量化失败文档数", 0),
                    "总向量分块数": 统计数据.get("总向量分块数", 0),
                }
            else:
                return {
                    "总文档数": 0,
                    "已处理文档数": 0,
                    "处理中文档数": 0,
                    "失败文档数": 0,
                    "已向量化文档数": 0,
                    "向量化处理中文档数": 0,
                    "向量化失败文档数": 0,
                    "总向量分块数": 0,
                }

        except Exception as e:
            数据日志器.error(f"获取知识库向量化状态失败: {str(e)}")
            raise

    async def 获取知识库向量存储信息(self, 知识id: int) -> Optional[Dict[str, Any]]:
        """获取知识库向量存储信息"""
        try:
            查询SQL = """
            SELECT id, 知识库名称, 向量存储类型, 知识库状态
            FROM langchain_知识库表
            WHERE id = $1
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (知识id,))
            return 结果[0] if 结果 else None

        except Exception as e:
            数据日志器.error(f"获取知识库信息失败: {str(e)}")
            return None

    async def 获取知识库向量存储状态(self, 知识id: int) -> Dict[str, Any]:
        """获取知识库向量存储状态"""
        try:
            向量存储信息 = await self.获取知识库向量存储信息(知识id)
            if not 向量存储信息:
                return {"状态": "不存在", "message": "知识库不存在"}

            # 获取文档统计
            统计SQL = """
            SELECT
                COUNT(*) as 总文档数,
                SUM(CASE WHEN 文档状态 = '已处理' THEN 1 ELSE 0 END) as 已处理文档数,
                SUM(CASE WHEN 文档状态 = '处理中' THEN 1 ELSE 0 END) as 处理中文档数,
                SUM(CASE WHEN 文档状态 = '失败' THEN 1 ELSE 0 END) as 失败文档数
            FROM langchain_知识库文档表
            WHERE langchain_知识库表id = $1
            """

            统计结果 = await self.数据库连接池.执行查询(统计SQL, (知识id,))
            统计信息 = 统计结果[0] if 统计结果 else {}

            # 获取向量数据统计
            向量统计SQL = """
            SELECT COUNT(*) as 向量数据总数
            FROM langchain_文档向量表 v
            JOIN langchain_知识库文档表 d ON v.langchain_知识库文档表id = d.id
            WHERE d.langchain_知识库表id = $1
            """

            向量统计结果 = await self.数据库连接池.执行查询(向量统计SQL, (知识id,))
            向量统计信息 = 向量统计结果[0] if 向量统计结果 else {}

            return {
                "知识id": 知识id,
                "知识库名称": 向量存储信息.get("知识库名称"),
                "向量存储类型": 向量存储信息.get("向量存储类型"),
                "知识库状态": 向量存储信息.get("知识库状态"),
                "文档统计": {
                    "总文档数": 统计信息.get("总文档数", 0),
                    "已处理文档数": 统计信息.get("已处理文档数", 0),
                    "处理中文档数": 统计信息.get("处理中文档数", 0),
                    "失败文档数": 统计信息.get("失败文档数", 0),
                },
                "向量数据统计": {
                    "向量数据总数": 向量统计信息.get("向量数据总数", 0),
                },
            }

        except Exception as e:
            数据日志器.error(f"获取知识库状态失败: {str(e)}")
            return {"状态": "错误", "message": f"获取状态失败: {str(e)}"}

    # ==================== 私有统计更新方法 ====================

    async def _更新知识库统计(self, 知识id: int) -> bool:
        """更新知识库的文档数量统计"""
        try:
            更新SQL = """
            UPDATE langchain_知识库表
            SET
                文档数量 = (
                    SELECT COUNT(*)
                    FROM langchain_知识库文档表
                    WHERE langchain_知识库表id = $1
                ),

                更新时间 = NOW()
            WHERE id = $2
            """

            await self.数据库连接池.执行更新(更新SQL, (知识id, 知识id))
            数据日志器.debug(f"更新知识库统计成功: {知识id}")
            return True

        except Exception as e:
            数据日志器.error(f"更新知识库统计失败: {str(e)}")
            return False

    async def _更新文档分块统计(self, 文档id: int) -> bool:
        """更新文档的分块数量统计"""
        try:
            更新SQL = """
            UPDATE langchain_知识库文档表
            SET
                向量分块数量 = (
                    SELECT COUNT(*)
                    FROM langchain_文档向量表
                    WHERE langchain_知识库文档表id = $1
                ),
                更新时间 = NOW()
            WHERE id = $2
            """

            await self.数据库连接池.执行更新(更新SQL, (文档id, 文档id))

            数据日志器.debug(f"更新文档分块统计成功: {文档id}")
            return True

        except Exception as e:
            数据日志器.error(f"更新文档分块统计失败: {str(e)}")
            return False

    async def 验证用户存在(self, 用户id列表: List[int]) -> Tuple[List[int], List[int]]:
        """验证用户是否存在"""
        try:
            用户验证SQL = f"SELECT id, 昵称 FROM 用户表 WHERE id IN ({','.join([f'${i + 1}' for i in range(len(用户id列表))])})"
            用户结果 = await self.数据库连接池.执行查询(用户验证SQL, tuple(用户id列表))

            存在的用户ids = [用户["id"] for 用户 in 用户结果]
            不存在的用户ids = [uid for uid in 用户id列表 if uid not in 存在的用户ids]

            return 存在的用户ids, 不存在的用户ids

        except Exception as e:
            数据日志器.error(f"验证用户存在失败: {str(e)}")
            raise

    async def 获取用户信息(self, 用户id列表: List[int]) -> List[Dict[str, Any]]:
        """获取用户信息"""
        try:
            if not 用户id列表:
                return []

            用户查询SQL = f"SELECT id, 昵称, 手机号, 邮箱 FROM 用户表 WHERE id IN ({','.join([f'${i + 1}' for i in range(len(用户id列表))])})"
            用户结果 = await self.数据库连接池.执行查询(用户查询SQL, tuple(用户id列表))

            return 用户结果 if 用户结果 else []

        except Exception as e:
            数据日志器.error(f"获取用户信息失败: {str(e)}")
            raise

    # ==================== 向量模型管理专用数据操作 ====================
    # 注意：模型配置相关操作已迁移到 LangChain_模型数据层.py

    async def 执行查询(
        self, 查询SQL: str, 查询参数: Optional[List] = None
    ) -> List[Dict[str, Any]]:
        """执行SQL查询并返回结果"""
        try:
            if 查询参数:
                结果 = await self.数据库连接池.执行查询(查询SQL, tuple(查询参数))
            else:
                结果 = await self.数据库连接池.执行查询(查询SQL)

            数据日志器.debug(f"执行查询成功，返回 {len(结果)} 条记录")
            return 结果

        except Exception as e:
            数据日志器.error(f"执行查询失败: {str(e)}")
            return []

    # ==================== 用户智能体服务相关数据操作 ====================

    async def 获取用户对话历史(
        self, 用户id: int, 查询参数: Dict[str, Any]
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取用户的对话历史记录"""
        try:
            # 构建查询条件
            查询条件 = ["lr.用户表id = $1"]
            查询参数_值 = [用户id]

            if 查询参数.get("智能体id"):
                查询条件.append("lr.langchain_智能体配置表id = $2")
                查询参数_值.append(查询参数["智能体id"])

            if 查询参数.get("会话id"):
                查询条件.append("lr.会话id = $3")
                查询参数_值.append(查询参数["会话id"])

            if 查询参数.get("开始时间"):
                查询条件.append("lr.创建时间 >= $4")
                查询参数_值.append(查询参数["开始时间"])

            if 查询参数.get("结束时间"):
                查询条件.append("lr.创建时间 <= $5")
                查询参数_值.append(查询参数["结束时间"])

            where_clause = "WHERE " + " AND ".join(查询条件)

            # 查询总数
            计数SQL = f"""
            SELECT COUNT(*) as total
            FROM langchain_对话记录表 lr
            {where_clause}
            """

            计数结果 = await self.数据库连接池.执行查询(计数SQL, tuple(查询参数_值))
            总数量 = 计数结果[0]["total"] if 计数结果 else 0

            # 查询对话历史
            页码 = 查询参数.get("页码", 1)
            每页数量 = 查询参数.get("每页数量", 10)
            偏移量 = (页码 - 1) * 每页数量

            历史SQL = f"""
            SELECT lr.id, lr.会话id, lr.用户消息, lr.智能体回复, lr.令牌消耗, lr.创建时间,
                   lc.智能体名称
            FROM langchain_对话记录表 lr
            LEFT JOIN langchain_智能体配置表 lc ON lr.langchain_智能体配置表id = lc.id
            {where_clause}
            ORDER BY lr.创建时间 DESC
            LIMIT $6 OFFSET $7
            """

            查询参数_值.extend([每页数量, 偏移量])
            对话历史 = await self.数据库连接池.执行查询(历史SQL, tuple(查询参数_值))

            return 对话历史 if 对话历史 else [], 总数量

        except Exception as e:
            数据日志器.error(f"获取用户对话历史失败: {str(e)}")
            return [], 0

    # 注意：智能体相关数据操作已迁移到 LangChain_智能体数据层.py
    # 注意：模型相关数据操作已迁移到 LangChain_模型数据层.py


# 创建全局数据层实例
LangChain数据层实例 = LangChain数据层()
