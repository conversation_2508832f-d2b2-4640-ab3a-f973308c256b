---
description: 
globs: 
alwaysApply: true
---
# Cursor智能助手配置

## 🤖 AI助手工作模式

### 项目理解优先级
1. **首要任务**：阅读相关项目文档和任务文件，了解项目历史和错误记录
2. **架构理解**：分析项目结构，识别代码模式和设计原则
3. **技术栈适配**：根据当前文件路径自动切换对应的技术栈规范
4. **避免冗余**：检查现有代码，避免重复实现

### 自动环境识别机制

#### 前端环境识别 (limob-crm-front-end/)
当工作在前端项目时，AI将：
- 优先提供Vue 3 + Composition API代码建议
- 自动识别Ant Design Vue组件使用
- 提供Vite开发环境相关建议
- 强化前端API调用规范检查

#### 后端环境识别 (invitation-system-backend/)
当工作在后端项目时，AI将：
- 优先提供FastAPI异步编程建议
- 自动识别Python类型提示和Pydantic模型
- 提供数据库操作最佳实践
- 强化中文命名规范检查

#### 前后端协作识别
当涉及API接口开发时，AI将：
- 自动检查接口路径一致性
- 提供统一响应格式建议
- 验证认证和权限流程
- 检查响应数据字段名称是否使用中文，确保前后端数据格式一致性

## 🎯 智能代码生成规范

### 前端代码生成（limob-crm-front-end/）


## 🧠 智能提示配置

### 代码补全优先级
1. **项目内已有代码**：优先提示项目中已存在的函数、组件、方法
2. **规范化模板**：基于项目规范生成标准化代码结构
3. **最佳实践**：提供符合项目架构的实现方案
4. **错误预防**：基于历史错误记录避免重复错误

### 自动导入规则
```javascript
// 前端自动导入优先级
1. 项目内组件和工具函数
2. Ant Design Vue 组件
3. Vue 3 核心 API
4. 第三方库

// 后端自动导入优先级
1. 项目内模块和函数
2. FastAPI 核心组件
3. Pydantic 数据模型
4. 数据库操作工具
```

## 🔍 智能分析能力

### 代码审查要点
- **命名规范检查**：自动检查是否符合中英文命名规范
- **响应数据格式检查**：确保后端API响应数据字段使用中文命名，避免英文中文混用
- **重复代码检测**：识别可能的代码冗余和重复逻辑
- **性能优化建议**：提供性能改进建议
- **安全漏洞扫描**：识别潜在的安全问题
- **前后端协作**：检查API接口一致性

### 重构建议
- **组件拆分**：当组件过大时建议拆分
- **函数抽取**：识别可复用的代码片段
- **状态管理优化**：建议更好的状态管理方案
- **API设计改进**：提供RESTful API设计建议

## 📚 学习和适应机制

### 项目特定模式学习
- **分析现有代码**：学习项目的编码风格和模式
- **错误模式识别**：从历史记录学习常见错误模式
- **最佳实践总结**：提取项目中的最佳实践
- **持续优化**：根据使用反馈不断改进建议质量

### 上下文感知
- **文件类型识别**：根据文件扩展名和路径应用对应规范
- **功能模块理解**：理解当前开发的功能模块
- **依赖关系分析**：理解模块间的依赖关系
- **业务逻辑理解**：根据注释和代码理解业务需求

## ⚠️ 常见错误预防

### 前端开发错误预防
- **模板引用命名一致性**：确保ref定义的引用名与JavaScript中使用的变量名完全一致，避免"ReferenceError: 变量名 is not defined"错误
- **移除模拟数据**：当用户要求对接真实API时，彻底删除所有模拟数据逻辑，不要保留任何假数据生成代码
- **API参数匹配**：修改前端API调用时，确保参数名称和格式与后端接口完全匹配
- **防御性编程**：在处理用户输入和API响应时，添加参数验证和空值检查，避免运行时错误

### 后端开发错误预防
- **重复接口避免**：检查是否已有相同功能的接口，避免在多个路由文件中创建重复的API端点
- **初始化逻辑完整性**：确保服务实例在被调用前已正确初始化，特别是智能体、数据库连接等核心组件

### 代码修改通用原则
- **修改前全面分析**：修改任何代码前，先查看相关的前后端文件，理解完整的调用链路
- **保持变量命名一致**：在同一个组件或函数中，确保变量命名在模板、脚本、样式中保持完全一致
- **渐进式修复**：一次只修复一个问题，避免同时修改多个不相关的代码片段
- **错误信息分析**：仔细分析错误信息，定位具体的错误原因，而不是盲目猜测和修改

