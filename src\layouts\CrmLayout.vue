<template>
  <a-layout class="crm-layout">
    <!-- 顶部导航栏 -->
    <a-layout-header class="layout-header">
      <div class="header-container">
        <!-- Logo 区域 -->
        <div class="logo-container">
          <div class="logo">
            <span class="logo-text">灵邀AI达人管家</span>
          </div>
        </div>

        <!-- 自定义主导航菜单 - 避免Ant Design Vue的ResizeObserver问题 -->
        <div class="custom-nav-menu">
          <div
            v-for="navItem in navItems"
            :key="navItem.key"
            :class="[
              'custom-nav-item',
              { 'active': selectedKeys.includes(navItem.key) }
            ]"
            @click="handleCustomNavClick(navItem.key)"
          >
            <div class="nav-item-content">
              <span class="nav-icon">
                <component :is="getNavIconComponent(navItem.icon)" />
              </span>
              <span class="nav-label">{{ navItem.label }}</span>
            </div>
          </div>
        </div>

        <!-- 右侧用户信息 -->
        <div class="header-right">
          <!-- 通知图标 -->
          <NotificationIcon />

          <!-- 用户信息下拉菜单 -->
          <a-dropdown placement="bottomRight" :trigger="['click']">
            <div class="user-info">
              <a-avatar 
                :src="userStore.userAvatar || undefined"
                :style="{ backgroundColor: '#1890ff' }"
              >
                {{ userStore.userAvatar ? '' : userStore.userName.charAt(0) }}
              </a-avatar>
              <span class="username">{{ userStore.userName }}</span>
              <DownOutlined class="dropdown-icon" />
            </div>
            
            <template #overlay>
              <a-menu @click="handleUserMenuClick">
                <a-menu-item key="settings">
                  <SettingOutlined />
                  <span>系统设置</span>
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout">
                  <LogoutOutlined />
                  <span>退出登录</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </a-layout-header>

    <!-- 主内容区域 -->
    <a-layout class="main-layout">
      <!-- 面包屑导航 -->
      <div class="breadcrumb-container">
        <a-breadcrumb class="breadcrumb">
          <a-breadcrumb-item>
            <router-link to="/dashboard">
              <home-outlined />
              工作台
            </router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item v-for="(breadcrumb, index) in breadcrumbs" :key="index">
            <router-link v-if="breadcrumb.path && index < breadcrumbs.length - 1" :to="breadcrumb.path">
              {{ breadcrumb.title }}
            </router-link>
            <span v-else>{{ breadcrumb.title }}</span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>

      <!-- 内容区域 -->
      <a-layout-content class="layout-content">
        <div class="content-wrapper">
          <router-view v-slot="{ Component }">
            <transition name="page" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </a-layout-content>


    </a-layout>

    <!-- 活动状态指示器已移除 -->
  </a-layout>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import NotificationIcon from '../components/NotificationIcon.vue'
import { useUserStore } from '../store/user'
// 活动监控相关导入已移除
import {
  ApartmentOutlined,
  DashboardOutlined,
  DownOutlined,
  HomeOutlined,
  LogoutOutlined,
  SettingOutlined,
  ShopOutlined,
  TeamOutlined,
  UserOutlined
} from '@ant-design/icons-vue'

// CRM 主布局组件
defineOptions({
  name: 'CrmLayout'
})

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const selectedKeys = ref([])

// 导航项配置
const navItems = ref([
  {
    key: '/dashboard',
    icon: 'DashboardOutlined',
    label: '工作台'
  },
  {
    key: '/gvm',
    icon: 'BarChartOutlined',
    label: '业绩中心'
  },
  {
    key: '/talent',
    icon: 'UserOutlined',
    label: '达人管理'
  },
  {
    key: '/friend',
    icon: 'TeamOutlined',
    label: '好友管理'
  },
  {
    key: '/store',
    icon: 'ShopOutlined',
    label: '店铺管理'
  },
  {
    key: '/team',
    icon: 'ApartmentOutlined',
    label: '团队管理'
  }
])

// 导航图标组件映射
const navIconComponents = {
  DashboardOutlined,
  UserOutlined,
  TeamOutlined,
  ShopOutlined,
  ApartmentOutlined
}

// 获取导航图标组件
const getNavIconComponent = (iconName) => {
  return navIconComponents[iconName] || DashboardOutlined
}

// 计算属性
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(r => r.meta && r.meta.title)
  const crumbs = []

  // 处理嵌套路由的面包屑
  for (let i = 0; i < matched.length; i++) {
    const routeRecord = matched[i]
    const meta = routeRecord.meta

    // 跳过根路径
    if (routeRecord.path === '/') continue

    // 添加面包屑项
    crumbs.push({
      title: meta.title,
      path: i < matched.length - 1 ? routeRecord.path : null // 最后一项不需要链接
    })
  }

  return crumbs
})

// 开发环境检查已移除（用于活动监控）

// 监听路由变化，更新选中的菜单
watch(
  () => route.path,
  (newPath) => {
    selectedKeys.value = [newPath]
  },
  { immediate: true }
)

// 处理菜单点击
const handleMenuClick = ({ key }) => {
  if (key !== route.path) {
    router.push(key)
  }
}

// 自定义导航点击处理
const handleCustomNavClick = (key) => {
  selectedKeys.value = [key]
  if (key !== route.path) {
    router.push(key)
  }
}

// 处理用户菜单点击
const handleUserMenuClick = ({ key }) => {
  switch (key) {
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 退出登录
const handleLogout = () => {
  Modal.confirm({
    title: '确认退出',
    content: '您确定要退出登录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await userStore.logout()
        message.success('已安全退出登录')
        router.push('/login')
      } catch (error) {
        console.error('退出登录失败:', error)
        message.error('退出登录失败，请重试')
      }
    }
  })
}

// 组件生命周期钩子
onMounted(() => {
  console.log('🚀 CRM布局加载完成')
})

onUnmounted(() => {
  console.log('🛑 CRM布局卸载')
})
</script>

<style scoped>
.crm-layout {
  min-height: 100vh;
}

.layout-header {
  background: #001529;
  padding: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.header-container {
  max-width: 1600px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  height: 64px;
  padding: 0 24px;
  min-width: 0; /* 允许容器收缩 */
  overflow: hidden; /* 防止内容溢出 */
}

.logo-container {
  margin-right: 24px; /* 减少右边距 */
  flex-shrink: 0; /* Logo区域不压缩 */
}

.logo {
  display: flex;
  align-items: center;
}

.logo-text {
  color: white;
  font-size: 18px; /* 稍微减小字体 */
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden; /* 防止溢出 */
  text-overflow: ellipsis; /* 超长显示省略号 */
  max-width: 200px; /* 限制最大宽度 */
}

/* 自定义导航菜单样式 - 替代Ant Design Menu避免ResizeObserver问题 */
.custom-nav-menu {
  flex: 1;
  display: flex;
  align-items: center;
  height: 64px;
  padding: 0 16px;
  overflow: hidden; /* 防止内容溢出 */
  max-width: calc(100% - 200px); /* 为右侧用户信息预留空间 */
}

.custom-nav-item {
  position: relative;
  margin: 0 2px; /* 减少间距，避免挤压 */
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 6px;
  user-select: none;
  flex-shrink: 0; /* 防止压缩 */
  min-width: 0; /* 允许内容收缩 */
}

.custom-nav-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.custom-nav-item.active {
  background-color: rgba(255, 255, 255, 0.15); /* 增强活跃状态的视觉效果 */
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2); /* 添加轻微阴影 */
}

.custom-nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 3px; /* 增加高度，更明显 */
  background: linear-gradient(90deg, #1890ff, #40a9ff); /* 渐变效果 */
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3); /* 添加发光效果 */
}

.nav-item-content {
  display: flex;
  align-items: center;
  padding: 10px 14px; /* 优化内边距 */
  color: white;
  font-size: 14px;
  font-weight: 500;
  min-width: 0; /* 允许内容收缩 */
  overflow: hidden; /* 防止文字溢出 */
}

.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: 6px; /* 减少间距 */
  font-size: 16px;
  flex-shrink: 0; /* 图标不压缩 */
}

.nav-label {
  white-space: nowrap;
  overflow: hidden; /* 防止文字溢出 */
  text-overflow: ellipsis; /* 超长文字显示省略号 */
  min-width: 0; /* 允许收缩 */
}

.header-right {
  margin-left: auto;
  flex-shrink: 0; /* 右侧区域不压缩 */
  min-width: 160px; /* 确保最小宽度，容纳通知图标和用户信息 */
  display: flex;
  align-items: center;
  gap: 16px; /* 通知图标和用户信息之间的间距 */
}

.user-info {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  min-width: 0; /* 允许内容收缩 */
}

.user-info:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px); /* 轻微上移效果 */
}

.username {
  margin: 0 8px;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px; /* 限制用户名最大宽度 */
}

.dropdown-icon {
  font-size: 12px;
  opacity: 0.8;
  flex-shrink: 0; /* 图标不压缩 */
}

.main-layout {
  margin-top: 64px; /* 为固定顶部导航留出空间 */
}

.breadcrumb-container {
  background: #fafafa;
  padding: 12px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.breadcrumb {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 24px;
}

.layout-content {
  margin: 0;
  width: 100%;
}

.content-wrapper {
  width: 100%;
  margin: 0;
  background: white;
  padding: 0;
}



/* 页面过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: opacity 0.15s ease;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .custom-nav-menu {
    max-width: calc(100% - 180px); /* 中等屏幕调整 */
  }

  .logo-text {
    max-width: 150px; /* 减少Logo宽度 */
  }
}

@media (max-width: 768px) {
  .header-container {
    padding: 0 16px;
  }

  .logo-container {
    margin-right: 12px;
  }

  .logo-text {
    font-size: 16px;
    max-width: 120px;
  }

  .custom-nav-menu {
    padding: 0 8px;
    max-width: calc(100% - 140px);
  }

  .custom-nav-item {
    margin: 0 1px; /* 进一步减少间距 */
  }

  .nav-item-content {
    padding: 8px 10px; /* 减少内边距 */
  }

  .nav-icon {
    margin-right: 4px;
  }

  .breadcrumb-container {
    padding: 8px 16px;
  }

  .layout-content {
    margin: 0;
  }

  .content-wrapper {
    padding: 0;
  }
}

@media (max-width: 576px) {
  .username {
    display: none; /* 小屏幕隐藏用户名 */
  }

  .custom-nav-menu {
    overflow-x: auto; /* 允许水平滚动 */
    max-width: calc(100% - 80px);
    padding: 0 4px;
  }

  .nav-label {
    display: none; /* 小屏幕只显示图标 */
  }

  .nav-icon {
    margin-right: 0;
  }

  .nav-item-content {
    padding: 8px;
    min-width: 40px;
    justify-content: center;
  }

  .header-right {
    min-width: 60px;
  }
}
</style> 