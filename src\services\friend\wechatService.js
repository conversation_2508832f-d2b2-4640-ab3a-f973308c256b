 import api from '../api'

/**
 * 微信账号管理服务
 * 提供微信账号的CRUD操作和关联管理
 */
class WeChatService {
  /**
   * 获取用户绑定的微信账号列表
   * @returns {Promise<Object>} 微信账号列表数据
   */
  async getUserWeChatAccounts() {
    try {
      const response = await api.post('/wechat/user-accounts')
      

      
      if (response.status === 100) {
        // 后端返回格式：data: {绑定微信列表: [...], 总数: n}
        const responseData = response.data || {}
        return {
          微信账号列表: responseData.绑定微信列表 || [],
          总数: responseData.总数 || 0,
          总好友数: responseData.总好友数 || 0,
          统计信息: responseData.统计信息 || {}
        }
      } else {
        throw new Error(response.message || '获取微信账号失败')
      }
    } catch (error) {
      console.error('获取用户微信账号失败:', error)
      throw new Error(`获取微信账号列表失败: ${error.message}`)
    }
  }

  /**
   * 获取微信账号列表（支持分页和搜索）
   * @param {Object} params - 查询参数
   * @param {number} params.页码 - 页码
   * @param {number} params.每页条数 - 每页条数
   * @param {string} params.状态 - 账号状态筛选
   * @param {string} params.关键词 - 搜索关键词
   * @returns {Promise<Object>} 微信账号列表数据
   */
  async getWeChatAccounts(params = {}) {
    try {
      const queryParams = {
        页码: params.页码 || 1,
        每页条数: params.每页条数 || 20,
        状态: params.状态 || null,
        关键词: params.关键词 || null
      }

      const response = await api.post('/wechat/accounts', queryParams)
      
      console.log('微信账号列表API响应:', response) // 调试日志
      
      if (response.status === 100) {
        // 直接返回后端数据中的data部分，让组件直接使用
        return response.data
      } else {
        throw new Error(response.message || '获取微信账号列表失败')
      }
    } catch (error) {
      console.error('获取微信账号列表失败:', error)
      throw new Error(`获取微信账号列表失败: ${error.message}`)
    }
  }

  /**
   * 添加微信账号（统一调用绑定接口）
   * @param {Object} accountData - 微信账号数据
   * @param {string} accountData.昵称 - 微信昵称
   * @param {string} accountData.微信号 - 微信号
   * @param {string} accountData.绑定手机号 - 绑定手机号
   * @param {string} accountData.头像 - 头像URL
   * @param {string} accountData.备注 - 备注信息
   * @returns {Promise<Object>} 添加结果
   */
  async addWeChatAccount(accountData) {
    try {
      // 统一调用绑定接口，支持所有字段
      const response = await api.post('/wechat/bind-account', accountData)
      
      if (response.status === 100) {
        return {
          status: 100,
          message: response.message || '微信账号添加成功',
          data: response.data
        }
      } else {
        throw new Error(response.message || '添加微信账号失败')
      }
    } catch (error) {
      console.error('添加微信账号失败:', error)
      throw new Error(`添加微信账号失败: ${error.message}`)
    }
  }

  /**
   * 更新微信账号
   * @param {Object} accountData - 微信账号数据
   * @param {number} accountData.账号id - 账号id
   * @param {string} accountData.昵称 - 微信昵称
   * @param {string} accountData.微信号 - 微信号
   * @param {string} accountData.绑定手机号 - 绑定手机号
   * @param {string} accountData.头像 - 头像URL
   * @param {string} accountData.备注 - 备注信息
   * @param {string} accountData.状态 - 账号状态
   * @returns {Promise<Object>} 更新结果
   */
  async updateWeChatAccount(accountData) {
    try {
      const response = await api.post('/wechat/accounts/update', accountData)
      
      if (response.status === 100) {
        return {
          status: 100,
          message: response.message || '微信账号更新成功',
          data: response.data
        }
      } else {
        throw new Error(response.message || '更新微信账号失败')
      }
    } catch (error) {
      console.error('更新微信账号失败:', error)
      throw new Error(`更新微信账号失败: ${error.message}`)
    }
  }



  /**
   * 绑定微信账号
   * @param {Object} wechatData - 微信账号数据
   * @param {string} wechatData.微信号 - 微信号
   * @param {string} wechatData.备注 - 备注信息
   * @returns {Promise<Object>} 绑定结果
   */
  async bindWeChatAccount(wechatData) {
    try {
      const response = await api.post('/wechat/bind-account', wechatData)
      
      if (response.status === 100) {
        return {
          status: 100,
          message: response.message || '微信账号绑定成功',
          data: response.data
        }
      } else {
        throw new Error(response.message || '绑定微信账号失败')
      }
    } catch (error) {
      console.error('绑定微信账号失败:', error)
      throw new Error(`绑定微信账号失败: ${error.message}`)
    }
  }

  /**
   * 解绑微信账号
   * @param {number} wechatId - 微信账号id
   * @returns {Promise<Object>} 解绑结果
   */
  async unbindWeChatAccount(wechatId) {
    try {
      const response = await api.post('/wechat/unbind-account', {
        微信id: wechatId
      })
      
      if (response.status === 100) {
        return {
          status: 100,
          message: response.message || '微信账号解绑成功'
        }
      } else {
        throw new Error(response.message || '解绑微信账号失败')
      }
    } catch (error) {
      console.error('解绑微信账号失败:', error)
      throw new Error(`解绑微信账号失败: ${error.message}`)
    }
  }

  /**
   * 获取微信账号统计概览
   * @returns {Promise<Object>} 统计数据
   */
  async getWeChatStatistics() {
    try {
      const response = await api.get('/wechat/user/statistics/overview')

      console.log('微信账号统计概览API响应:', response) // 调试日志

      if (response.status === 100) {
        // 返回统一响应模型中的data字段
        return response
      } else {
        throw new Error(response.message || '获取微信统计失败')
      }
    } catch (error) {
      console.error('获取微信统计失败:', error)
      throw new Error(`获取微信统计失败: ${error.message}`)
    }
  }



  /**
   * 验证微信号格式
   * @param {string} wechatId - 微信号
   * @returns {boolean} 是否有效
   */
  validateWeChatId(wechatId) {
    if (!wechatId) return false
    
    // 微信号规则：6-20位，字母开头，允许字母、数字、下划线、减号
    const wechatRegex = /^[a-zA-Z][a-zA-Z0-9_-]{5,19}$/
    return wechatRegex.test(wechatId)
  }

  /**
   * 格式化微信账号数据
   * @param {Object} account - 原始账号数据
   * @returns {Object} 格式化后的数据
   */
  formatAccountData(account) {
    return {
      id: account.微信id || account.id,
      微信号: account.微信号,
      昵称: account.昵称 || '',
      头像: account.头像 || '',
      绑定时间: account.绑定时间,
      状态: account.状态 || 1,
      状态文本: account.状态 === 1 ? '正常' : '禁用',
      备注: account.备注 || '',
      好友数量: account.好友数量 || 0,
      最后活跃时间: account.最后活跃时间
    }
  }
}

// 创建并导出服务实例
const wechatService = new WeChatService()
export default wechatService 