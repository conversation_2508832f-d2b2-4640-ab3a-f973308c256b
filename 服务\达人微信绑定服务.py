"""
达人微信号绑定服务

功能概述：
- 为已认领的达人绑定微信号
- 智能查询和创建微信信息记录
- 自动生成关联数据
- 确保数据一致性和完整性

作者: CRM系统开发团队
创建时间: 2024-12-19
"""

from datetime import datetime
from typing import Any, Dict

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 系统日志器, 错误日志器


class 达人微信绑定服务:
    """达人微信号绑定服务类"""

    def __init__(self):
        self.logger = 系统日志器
        self.error_logger = 错误日志器

    async def 绑定达人微信号(
        self, 用户id: int, 达人id: int, 微信号: str, 平台: str = "微信"
    ) -> Dict[str, Any]:
        """
        为已认领的达人绑定微信号

        参数:
            用户id: 当前用户id
            达人id: 要绑定微信号的达人id
            微信号: 要绑定的微信号
            平台: 平台类型，默认为"微信"

        返回:
            操作结果字典
        """
        开始时间 = datetime.now()

        try:
            # 使用事务确保数据一致性
            async with 异步连接池实例.获取连接() as 连接:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                async with 连接.transaction():
                    # 1. 验证用户是否已认领该达人
                    验证结果 = await self._验证用户达人关系(连接, 用户id, 达人id)
                    if not 验证结果["有效"]:
                        return {"状态": "失败", "消息": 验证结果["消息"]}

                    # 2. 查询或创建微信信息记录
                    微信信息id = await self._查询或创建微信信息(连接, 微信号)

                    # 3. 创建或更新达人商务微信记录
                    达人商务微信id = await self._创建或更新达人商务微信(
                        连接, 微信信息id, 达人id, 平台
                    )

                    # 4. 创建用户达人商务微信关联记录
                    await self._创建用户达人商务微信关联(
                        连接, 用户id, 达人商务微信id
                    )

                    # 5. 更新用户达人补充信息表中的微信号
                    关联表id = 验证结果["关联表id"]
                    await self._更新达人补充信息微信号(连接, 关联表id, 微信号)

                耗时 = (datetime.now() - 开始时间).total_seconds()
                self.logger.info(
                    f"成功绑定达人微信号: 用户id={用户id}, 达人id={达人id}, "
                    f"微信号={微信号}, 耗时={耗时:.3f}秒"
                )

                return {
                    "状态": "成功",
                    "消息": "微信号绑定成功",
                    "数据": {
                        "微信信息id": 微信信息id,
                        "达人商务微信id": 达人商务微信id,
                        "关联表id": 关联表id,
                    },
                }

        except Exception as e:
            耗时 = (datetime.now() - 开始时间).total_seconds()
            self.error_logger.error(
                f"绑定达人微信号失败: 用户id={用户id}, 达人id={达人id}, "
                f"微信号={微信号}, 耗时={耗时:.3f}秒, 错误={str(e)}"
            )

            return {"状态": "失败", "消息": f"微信号绑定失败: {str(e)}"}

    async def _验证用户达人关系(self, 连接, 用户id: int, 达人id: int) -> Dict[str, Any]:
        """验证用户是否已认领该达人"""
        try:
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            验证SQL = """
            SELECT id FROM 用户达人关联表
            WHERE 用户id = $1 AND 达人id = $2 AND 状态 = 1
            ORDER BY 认领时间 DESC
            LIMIT 1
            """

            结果 = await 连接.fetchrow(验证SQL, 用户id, 达人id)

            if 结果:
                return {"有效": True, "关联表id": 结果["id"], "消息": "验证通过"}
            else:
                return {"有效": False, "消息": "您没有认领该达人，无法绑定微信号"}

        except Exception as e:
            self.error_logger.error(f"验证用户达人关系失败: {str(e)}")
            return {"有效": False, "消息": "验证用户达人关系失败"}

    async def _查询或创建微信信息(self, 连接, 微信号: str) -> int:
        """查询或创建微信信息记录"""
        try:
            # 先查询是否存在
            查询SQL = """
            SELECT id FROM 微信信息表
            WHERE 微信号 LIKE $1
            LIMIT 1
            """

            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            结果 = await 连接.fetchrow(查询SQL, f"%{微信号}%")

            if 结果:
                self.logger.debug(
                    f"找到已存在的微信信息: ID={结果['id']}, 微信号={微信号}"
                )
                return 结果["id"]
            else:
                # 创建新的微信信息记录
                创建SQL = """
                INSERT INTO 微信信息表 (微信号, 修改时间)
                VALUES ($1, NOW())
                RETURNING id
                """

                新记录 = await 连接.fetchrow(创建SQL, 微信号)
                if not 新记录:
                    raise Exception("创建微信信息记录失败")
                微信信息id = 新记录["id"]

                self.logger.info(
                    f"创建新的微信信息记录: ID={微信信息id}, 微信号={微信号}"
                )
                return 微信信息id

        except Exception as e:
            self.error_logger.error(
                f"查询或创建微信信息失败: 微信号={微信号}, 错误={str(e)}"
            )
            raise e

    async def _创建或更新达人商务微信(
        self, 连接, 微信信息id: int, 达人id: int, 平台: str
    ) -> int:
        """创建或更新达人商务微信记录"""
        try:
            # 先查询是否已存在
            查询SQL = """
            SELECT id FROM 达人商务微信表
            WHERE 达人表id = $1 AND 平台 = $2
            LIMIT 1
            """

            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            结果 = await 连接.fetchrow(查询SQL, 达人id, 平台)

            if 结果:
                # 更新现有记录
                更新SQL = """
                UPDATE 达人商务微信表
                SET 微信信息表id = $1
                WHERE id = $2
                """

                await 连接.execute(更新SQL, 微信信息id, 结果["id"])
                达人商务微信id = 结果["id"]

                self.logger.debug(f"更新达人商务微信记录: ID={达人商务微信id}")
            else:
                # 创建新记录
                创建SQL = """
                INSERT INTO 达人商务微信表 (微信信息表id, 达人表id, 平台)
                VALUES ($1, $2, $3)
                RETURNING id
                """

                新记录 = await 连接.fetchrow(创建SQL, 微信信息id, 达人id, 平台)
                if not 新记录:
                    raise Exception("创建达人商务微信记录失败")
                达人商务微信id = 新记录["id"]

                self.logger.info(f"创建达人商务微信记录: ID={达人商务微信id}")

            return 达人商务微信id

        except Exception as e:
            self.error_logger.error(
                f"创建或更新达人商务微信失败: 微信信息id={微信信息id}, "
                f"达人id={达人id}, 平台={平台}, 错误={str(e)}"
            )
            raise e

    async def _创建用户达人商务微信关联(
        self, 连接, 用户id: int, 达人商务微信id: int
    ) -> None:
        """创建用户达人商务微信关联记录"""
        try:
            # 修复：由于用户_达人商务微信_关联表不存在，简化关联逻辑
            # 通过达人商务微信表直接验证关联关系
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            查询SQL = """
            SELECT 1 FROM 达人商务微信表 dwx
            INNER JOIN 用户达人关联表 uda ON dwx.达人表id = uda.达人id
            WHERE uda.用户id = $1 AND uda.状态 = 1 AND dwx.id = $2
            LIMIT 1
            """

            结果 = await 连接.fetchrow(查询SQL, 用户id, 达人商务微信id)

            if not 结果:
                # 注意：用户_达人商务微信_关联表 不存在，暂时通过日志记录关联信息
                # 实际的关联关系通过 用户达人关联表 和 达人商务微信表 来维护
                self.logger.info(
                    f"达人商务微信绑定完成: 用户id={用户id}, 达人商务微信id={达人商务微信id}"
                )
            else:
                self.logger.debug(
                    f"达人商务微信关联已存在: 用户id={用户id}, 达人商务微信id={达人商务微信id}"
                )

        except Exception as e:
            self.error_logger.error(
                f"创建用户达人商务微信关联失败: 用户id={用户id}, "
                f"达人商务微信id={达人商务微信id}, 错误={str(e)}"
            )
            raise e

    async def _更新达人补充信息微信号(self, 连接, 关联表id: int, 微信号: str) -> None:
        """更新用户达人补充信息表中的微信号"""
        try:
            # 获取或创建联系方式表记录
            from 数据.线索数据操作 import 异步提交联系方式

            联系方式表id = await 异步提交联系方式(
                联系方式=微信号,
                类型="微信",
                来源="微信绑定"
            )

            if not 联系方式表id:
                raise Exception(f"创建联系方式记录失败: {微信号}")

            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            更新SQL = """
            INSERT INTO 用户达人补充信息表
            (用户达人关联表id, 联系方式, 联系方式类型, 联系方式表id, 创建时间, 更新时间)
            VALUES ($1, $2, $3, $4, NOW(), NOW())
            ON CONFLICT ON CONSTRAINT "补充信息_联合索引" DO UPDATE SET
                联系方式 = EXCLUDED.联系方式,
                联系方式类型 = EXCLUDED.联系方式类型,
                联系方式表id = EXCLUDED.联系方式表id,
                更新时间 = NOW()
            """

            await 连接.execute(更新SQL, 关联表id, 微信号, "微信", 联系方式表id)
            self.logger.debug(
                f"更新达人补充信息微信号: 关联表id={关联表id}, 微信号={微信号}, 联系方式表id={联系方式表id}"
            )

        except Exception as e:
            self.error_logger.error(
                f"更新达人补充信息微信号失败: 关联表id={关联表id}, "
                f"微信号={微信号}, 错误={str(e)}"
            )
            raise e


# 创建服务实例
达人微信绑定服务实例 = 达人微信绑定服务()
