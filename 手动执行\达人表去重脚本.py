"""
达人表 account_douyin 重复值去重脚本

功能：
1. 查找达人表中 account_douyin 有重复值的所有记录
2. 以 id 最小的记录为主记录
3. 将其他重复记录中的非空数据补充到主记录
4. 删除重复记录

使用方法：
python 手动执行/达人表去重脚本.py
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 应用日志器, 错误日志器


class 达人表去重处理器:
    """达人表去重处理器"""
    
    def __init__(self):
        self.处理统计 = {
            "重复组数": 0,
            "处理记录数": 0,
            "删除记录数": 0,
            "更新记录数": 0,
            "错误数": 0
        }
    
    async def 查找重复的account_douyin(self) -> List[Dict[str, Any]]:
        """
        查找所有有重复值的 account_douyin
        
        Returns:
            重复的 account_douyin 列表
        """
        try:
            查询SQL = """
            SELECT account_douyin, COUNT(*) as 重复数量
            FROM 达人表 
            WHERE account_douyin IS NOT NULL 
            AND account_douyin != ''
            GROUP BY account_douyin 
            HAVING COUNT(*) > 1
            ORDER BY COUNT(*) DESC, account_douyin
            """
            
            结果 = await 异步连接池实例.执行查询(查询SQL)
            应用日志器.info(f"找到 {len(结果)} 个重复的 account_douyin")
            
            return 结果
            
        except Exception as e:
            错误日志器.error(f"查找重复 account_douyin 失败: {str(e)}")
            raise
    
    async def 获取重复记录详情(self, account_douyin: str) -> List[Dict[str, Any]]:
        """
        获取某个 account_douyin 的所有重复记录
        
        Args:
            account_douyin: 抖音账号
            
        Returns:
            该账号的所有记录，按 id 升序排列
        """
        try:
            查询SQL = """
            SELECT * FROM 达人表 
            WHERE account_douyin = $1 
            ORDER BY id ASC
            """
            
            结果 = await 异步连接池实例.执行查询(查询SQL, (account_douyin,))
            应用日志器.info(f"account_douyin '{account_douyin}' 有 {len(结果)} 条重复记录")
            
            return 结果
            
        except Exception as e:
            错误日志器.error(f"获取重复记录详情失败: account_douyin={account_douyin}, 错误={str(e)}")
            raise
    
    async def 智能处理uid_number冲突(self, 主记录: Dict[str, Any], 其他记录列表: List[Dict[str, Any]], 补充数据: Dict[str, Any]) -> Dict[str, Any]:
        """
        智能处理uid_number冲突，确保重要数据不丢失

        Args:
            主记录: 主记录数据
            其他记录列表: 其他重复记录列表
            补充数据: 原始的补充数据

        Returns:
            处理后的补充数据
        """
        # 如果补充数据中没有uid_number，直接返回
        if 'uid_number' not in 补充数据:
            return 补充数据

        目标uid_number = 补充数据['uid_number']
        主记录id = 主记录['id']

        try:
            # 检查这个uid_number是否已经被其他记录使用
            检查SQL = """
            SELECT id, account_douyin FROM 达人表
            WHERE uid_number = $1 AND id != $2
            LIMIT 1
            """

            冲突记录 = await 异步连接池实例.执行查询(检查SQL, (目标uid_number, 主记录id))

            if 冲突记录:
                冲突记录信息 = 冲突记录[0]
                应用日志器.warning(f"uid_number {目标uid_number} 已被记录 id={冲突记录信息['id']} (account_douyin={冲突记录信息['account_douyin']}) 使用")

                # 如果主记录本身没有uid_number，我们需要先清空冲突记录的uid_number
                if 主记录.get('uid_number') is None or 主记录.get('uid_number') == '':
                    应用日志器.info(f"主记录没有uid_number，将清空冲突记录的uid_number并更新主记录")

                    # 清空冲突记录的uid_number
                    清空SQL = "UPDATE 达人表 SET uid_number = NULL WHERE id = $1"
                    await 异步连接池实例.执行更新(清空SQL, (冲突记录信息['id'],))
                    应用日志器.info(f"已清空记录 id={冲突记录信息['id']} 的uid_number")

                    # 保留uid_number在补充数据中
                    return 补充数据
                else:
                    # 主记录已有uid_number，移除补充数据中的uid_number
                    应用日志器.info(f"主记录已有uid_number，跳过冲突的uid_number更新")
                    修改后的补充数据 = 补充数据.copy()
                    del 修改后的补充数据['uid_number']
                    return 修改后的补充数据
            else:
                # 没有冲突，直接返回
                return 补充数据

        except Exception as e:
            错误日志器.error(f"处理uid_number冲突失败: {str(e)}")
            # 出错时移除uid_number，避免更新失败
            修改后的补充数据 = 补充数据.copy()
            if 'uid_number' in 修改后的补充数据:
                del 修改后的补充数据['uid_number']
            return 修改后的补充数据

    async def 清空重复记录的uid_number(self, 重复记录id列表: List[int]) -> bool:
        """
        清空重复记录的uid_number，避免唯一约束冲突

        Args:
            重复记录id列表: 需要清空uid_number的记录id列表

        Returns:
            是否清空成功
        """
        if not 重复记录id列表:
            return True

        try:
            清空SQL = """
            UPDATE 达人表
            SET uid_number = NULL
            WHERE id = ANY($1)
            """

            影响行数 = await 异步连接池实例.执行更新(清空SQL, (重复记录id列表,))

            if 影响行数 > 0:
                应用日志器.info(f"成功清空 {影响行数} 条重复记录的uid_number: {重复记录id列表}")
                return True
            else:
                错误日志器.error(f"清空重复记录uid_number失败，影响行数为0: {重复记录id列表}")
                return False

        except Exception as e:
            错误日志器.error(f"清空重复记录uid_number失败: {重复记录id列表}, 错误={str(e)}")
            return False

    def 分析需要补充的数据(self, 主记录: Dict[str, Any], 其他记录列表: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析其他记录中有哪些数据是主记录没有的

        Args:
            主记录: id 最小的记录
            其他记录列表: 其他重复记录

        Returns:
            需要补充到主记录的数据
        """
        需要补充的数据 = {}

        # 定义需要检查的字段（排除 id 和一些不需要合并的字段）
        检查字段列表 = [
            'uid_number', 'uid_short', 'sec_uid', 'buyin_account_id', '昵称', 'label',
            'categories_2', 'categories', '粉丝数', '关注数', '账号状态', 'fans_sum_str',
            'live_sale_low', 'viewer_median', 'live_sale_high', 'average_online_number',
            'agency', 'gmv', 'video_sale', 'live_sale', 'convert_rate', 'views',
            'sale_type', 'is_video', 'is_live', 'recent_category', 'average',
            'reputation_level', 'introduction', 'author_level', 'city', 'avatar',
            '性别', 'score', 'web_url', 'live_watching_number', 'product_main_type_array',
            'tags', 'total_work_day', 'promotion_sum_30', 'cooperate_shop_num_30',
            'sale_low_all_30', 'sale_high_all_30', 'live_percentage_30', 'live_couunt_30',
            'live_watching_num_30', 'live_sale_low_a_30', 'live_sale_high_a_30',
            'live_gmv_low_30', 'live_gmv_high_30', 'live_live_day_30', 'live_live_product_num_30',
            'live_avg_live_online_dur_30', 'video_percentage_30', 'video_count_30',
            'video_watching_num_30', 'video_sale_low_a_30', 'video_sale_high_a_30',
            'video_gpm_low_30', 'video_gpm_high_30', '企业认证'
        ]

        for 字段 in 检查字段列表:
            主记录值 = 主记录.get(字段)

            # 如果主记录该字段为空或None，检查其他记录是否有值
            if 主记录值 is None or 主记录值 == '' or 主记录值 == 0:
                for 其他记录 in 其他记录列表:
                    其他记录值 = 其他记录.get(字段)
                    if 其他记录值 is not None and 其他记录值 != '' and 其他记录值 != 0:
                        需要补充的数据[字段] = 其他记录值
                        应用日志器.info(f"字段 '{字段}' 需要补充: {其他记录值} (来自 id={其他记录['id']})")
                        break  # 找到第一个非空值就停止

        return 需要补充的数据
    
    async def 更新主记录(self, 主记录id: int, 补充数据: Dict[str, Any]) -> bool:
        """
        更新主记录，补充缺失的数据
        
        Args:
            主记录id: 主记录的 id
            补充数据: 需要补充的数据
            
        Returns:
            是否更新成功
        """
        if not 补充数据:
            应用日志器.info(f"主记录 id={主记录id} 无需补充数据")
            return True
        
        try:
            # 构建更新SQL
            更新字段列表 = []
            参数列表 = []
            参数索引 = 1
            
            for 字段, 值 in 补充数据.items():
                更新字段列表.append(f"{字段} = ${参数索引}")
                参数列表.append(值)
                参数索引 += 1
            
            更新SQL = f"""
            UPDATE 达人表 
            SET {', '.join(更新字段列表)}
            WHERE id = ${参数索引}
            """
            参数列表.append(主记录id)
            
            影响行数 = await 异步连接池实例.执行更新(更新SQL, tuple(参数列表))
            
            if 影响行数 > 0:
                应用日志器.info(f"主记录 id={主记录id} 更新成功，补充了 {len(补充数据)} 个字段")
                self.处理统计["更新记录数"] += 1
                return True
            else:
                错误日志器.error(f"主记录 id={主记录id} 更新失败，影响行数为0")
                return False
                
        except Exception as e:
            错误日志器.error(f"更新主记录失败: id={主记录id}, 错误={str(e)}")
            self.处理统计["错误数"] += 1
            return False
    
    async def 删除重复记录(self, 重复记录id列表: List[int]) -> bool:
        """
        删除重复记录
        
        Args:
            重复记录id列表: 需要删除的记录 id 列表
            
        Returns:
            是否删除成功
        """
        if not 重复记录id列表:
            return True
        
        try:
            删除SQL = """
            DELETE FROM 达人表 
            WHERE id = ANY($1)
            """
            
            影响行数 = await 异步连接池实例.执行更新(删除SQL, (重复记录id列表,))
            
            if 影响行数 > 0:
                应用日志器.info(f"成功删除 {影响行数} 条重复记录: {重复记录id列表}")
                self.处理统计["删除记录数"] += 影响行数
                return True
            else:
                错误日志器.error(f"删除重复记录失败，影响行数为0: {重复记录id列表}")
                return False
                
        except Exception as e:
            错误日志器.error(f"删除重复记录失败: {重复记录id列表}, 错误={str(e)}")
            self.处理统计["错误数"] += 1
            return False
    
    async def 处理单个重复组(self, account_douyin: str) -> bool:
        """
        处理单个重复组的去重

        Args:
            account_douyin: 重复的抖音账号

        Returns:
            是否处理成功
        """
        try:
            应用日志器.info(f"\n{'='*50}")
            应用日志器.info(f"开始处理重复账号: {account_douyin}")

            # 获取所有重复记录
            重复记录列表 = await self.获取重复记录详情(account_douyin)

            if len(重复记录列表) < 2:
                应用日志器.warning(f"账号 {account_douyin} 重复记录数量不足，跳过处理")
                return True

            # 第一条记录作为主记录（id最小）
            主记录 = 重复记录列表[0]
            其他记录列表 = 重复记录列表[1:]

            应用日志器.info(f"主记录 id={主记录['id']}, 需要处理的重复记录: {[r['id'] for r in 其他记录列表]}")

            # 分析需要补充的数据
            补充数据 = self.分析需要补充的数据(主记录, 其他记录列表)

            if 补充数据:
                应用日志器.info(f"原始补充数据: {json.dumps(补充数据, ensure_ascii=False, indent=2)}")

                # 智能处理uid_number冲突
                处理后的补充数据 = await self.智能处理uid_number冲突(主记录, 其他记录列表, 补充数据)

                if 处理后的补充数据 != 补充数据:
                    应用日志器.info(f"处理后补充数据: {json.dumps(处理后的补充数据, ensure_ascii=False, indent=2)}")

                # 更新主记录
                if 处理后的补充数据:
                    更新成功 = await self.更新主记录(主记录['id'], 处理后的补充数据)
                    if not 更新成功:
                        return False

            # 清空重复记录的uid_number，避免删除时的约束冲突
            重复记录id列表 = [记录['id'] for 记录 in 其他记录列表]
            清空成功 = await self.清空重复记录的uid_number(重复记录id列表)
            if not 清空成功:
                应用日志器.error(f"清空重复记录uid_number失败，跳过删除: {account_douyin}")
                return False

            # 删除重复记录
            删除成功 = await self.删除重复记录(重复记录id列表)

            if 删除成功:
                应用日志器.info(f"账号 {account_douyin} 去重处理完成")
                self.处理统计["重复组数"] += 1
                self.处理统计["处理记录数"] += len(重复记录列表)
                return True
            else:
                return False

        except Exception as e:
            错误日志器.error(f"处理重复组失败: account_douyin={account_douyin}, 错误={str(e)}")
            self.处理统计["错误数"] += 1
            return False
    
    async def 执行去重处理(self):
        """执行完整的去重处理流程"""
        try:
            应用日志器.info("开始执行达人表 account_douyin 去重处理")
            开始时间 = datetime.now()
            
            # 查找所有重复的 account_douyin
            重复账号列表 = await self.查找重复的account_douyin()
            
            if not 重复账号列表:
                应用日志器.info("没有找到重复的 account_douyin，无需处理")
                return
            
            应用日志器.info(f"总共找到 {len(重复账号列表)} 个重复的 account_douyin")
            
            # 逐个处理重复组
            成功数 = 0
            失败数 = 0
            
            for 重复信息 in 重复账号列表:
                account_douyin = 重复信息['account_douyin']
                重复数量 = 重复信息['重复数量']
                
                应用日志器.info(f"处理进度: {成功数 + 失败数+ 1}/{len(重复账号列表)} - {account_douyin} (重复{重复数量}条)")
                
                处理成功 = await self.处理单个重复组(account_douyin)
                
                if 处理成功:
                    成功数 += 1
                else:
                    失败数 += 1
                    应用日志器.error(f"处理失败: {account_douyin}")
            
            结束时间 = datetime.now()
            耗时 = (结束时间 - 开始时间).total_seconds()
            
            # 输出处理统计
            应用日志器.info(f"\n{'='*60}")
            应用日志器.info("达人表去重处理完成")
            应用日志器.info(f"处理时间: {耗时:.2f} 秒")
            应用日志器.info(f"处理统计:")
            应用日志器.info(f"  - 重复组数: {self.处理统计['重复组数']}")
            应用日志器.info(f"  - 处理记录数: {self.处理统计['处理记录数']}")
            应用日志器.info(f"  - 删除记录数: {self.处理统计['删除记录数']}")
            应用日志器.info(f"  - 更新记录数: {self.处理统计['更新记录数']}")
            应用日志器.info(f"  - 错误数: {self.处理统计['错误数']}")
            应用日志器.info(f"  - 成功组数: {成功数}")
            应用日志器.info(f"  - 失败组数: {失败数}")
            应用日志器.info(f"{'='*60}")
            
        except Exception as e:
            错误日志器.error(f"执行去重处理失败: {str(e)}")
            raise


async def main():
    """主函数"""
    try:
        # 初始化数据库连接池
        await 异步连接池实例.初始化数据库连接池()
        
        # 创建去重处理器并执行
        处理器 = 达人表去重处理器()
        await 处理器.执行去重处理()
        
    except Exception as e:
        错误日志器.error(f"脚本执行失败: {str(e)}")
        raise
    finally:
        # 关闭数据库连接池
        await 异步连接池实例.关闭连接池()


if __name__ == "__main__":
    asyncio.run(main())
