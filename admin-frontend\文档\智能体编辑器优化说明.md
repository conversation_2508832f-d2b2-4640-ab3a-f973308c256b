# 智能体编辑器优化说明

## 概述

本文档描述了智能体编辑器的完整功能优化实现，包括基础配置、提示词管理、知识库集成、RAG配置和对话测试等核心功能。

## 🎯 主要功能特性

### 1. 标签页式界面设计
- **基本配置**: 智能体基础信息和模型配置
- **提示词配置**: 系统提示词编辑和知识库变量管理
- **知识库配置**: 知识库选择、RAG配置和检索测试
- **对话测试**: 实时智能体对话测试和调试

### 2. 智能体基础功能优化

#### 基本信息配置
- 智能体名称、类型、描述设置
- 支持多种智能体类型：基础对话型、规划执行型、多智能体协作
- 实时配置预览功能

#### 模型配置
- 对话模型选择（从数据库动态获取）
- 温度参数调节（0-2范围滑块）
- 模型信息实时显示

### 3. 知识库集成功能

#### 知识库选择
- 多选知识库支持
- 知识库状态显示
- 已选择知识库管理

#### RAG检索配置
- 检索策略选择：语义相似度、关键词匹配、混合检索
- 嵌入模型选择（从数据库获取可用模型）
- 相似度阈值设置（0-1范围滑块）
- 最大检索数量配置

#### 检索测试功能
- 实时检索测试
- 检索结果展示（包含相似度分数）
- 支持指定嵌入模型进行测试

### 4. 提示词与知识库整合

#### 提示词编辑器
- 大文本编辑器支持
- 知识库变量插入功能
- 提示词模板参考
- 实时预览功能

#### 知识库变量支持
- `{knowledge_context}` - 知识库检索结果
- `{user_question}` - 用户问题
- `{chat_history}` - 对话历史

### 5. 对话测试功能

#### 实时对话测试
- 类似聊天界面的对话体验
- 支持普通对话和调试模式
- 会话历史管理
- 对话统计信息

#### 测试配置
- 测试模式切换
- 会话历史保留选项
- 对话记录清空功能

## 🔧 技术实现

### 前端技术栈
- **Vue 3**: 组合式API
- **Ant Design Vue**: UI组件库
- **响应式设计**: 支持不同屏幕尺寸

### API接口对接

#### 智能体管理接口
```javascript
// 获取智能体详情
GET /admin/langchain/agents/{id}/detail

// 更新智能体
POST /admin/langchain/agents/update

// 创建智能体
POST /admin/langchain/agents/create
```

#### 模型管理接口
```javascript
// 获取对话模型列表
POST /admin/langchain/model-providers/chat-models

// 获取嵌入模型列表
GET /admin/langchain/knowledge/embedding_models
```

#### 知识库接口
```javascript
// 获取知识库列表
POST /admin/langchain/knowledge/list

// 直接知识库检索测试
POST /admin/langchain/knowledge/{id}/test_retrieval

// 智能体检索测试
POST /admin/langchain/agent/{id}/test_retrieval
```

#### 对话测试接口
```javascript
// 智能体对话
POST /admin/langchain/agent/{id}/chat

// 获取智能体关联知识库
GET /admin/langchain/agent/{id}/knowledge_bases
```

### 数据结构

#### 智能体表单数据
```javascript
const 智能体表单 = {
  智能体名称: '',
  智能体类型: 'basic_chat',
  智能体描述: '',
  对话模型id: null,
  温度参数: 0.7,
  系统提示词: '',
  知识库列表: [],
  检索策略: 'similarity',
  嵌入模型id: null,
  相似度阈值: 0.7,
  最大检索数量: 5
}
```

#### 检索测试请求
```javascript
const 检索测试请求 = {
  查询文本: '用户查询内容',
  嵌入模型id: 123, // 可选
  检索参数: {
    最大检索数量: 5,
    相似度阈值: 0.7
  }
}
```

#### 对话测试请求
```javascript
const 对话请求 = {
  消息: '用户消息内容',
  保留历史: true,
  调试模式: false
}
```

## 🎨 用户体验优化

### 界面设计
- **标签页组织**: 避免页面过长，功能模块清晰
- **实时反馈**: 所有操作提供明确的成功/失败反馈
- **加载状态**: 完善的加载状态指示
- **响应式设计**: 支持不同屏幕尺寸

### 交互优化
- **智能默认值**: 合理的默认配置
- **表单验证**: 前端数据验证确保有效性
- **快捷操作**: 一键插入变量、模板等
- **预览功能**: 实时配置预览

### 错误处理
- **网络错误**: 友好的错误提示
- **数据验证**: 前端验证防止无效提交
- **异常恢复**: 操作失败后的恢复机制

## 📋 使用指南

### 1. 创建新智能体
1. 进入智能体管理页面
2. 点击"创建智能体"按钮
3. 填写基本信息（名称、类型、描述）
4. 选择对话模型和配置参数
5. 编写系统提示词
6. 选择关联知识库（可选）
7. 配置RAG检索参数
8. 保存创建

### 2. 编辑现有智能体
1. 在智能体列表中点击"编辑"
2. 在各个标签页中修改配置
3. 使用检索测试验证知识库配置
4. 使用对话测试验证整体效果
5. 保存修改

### 3. 知识库配置最佳实践
1. **选择相关知识库**: 确保知识库内容与智能体用途相关
2. **合理设置阈值**: 相似度阈值建议0.6-0.8之间
3. **控制检索数量**: 建议3-10个结果，避免信息过载
4. **选择合适的嵌入模型**: 根据内容语言选择对应模型

### 4. 提示词编写指南
1. **明确角色定位**: 清楚描述智能体的角色和能力
2. **使用知识库变量**: 合理使用`{knowledge_context}`变量
3. **提供使用指导**: 告诉智能体如何使用检索到的信息
4. **设置回答格式**: 指定期望的回答风格和格式

## 🔍 测试和调试

### 检索测试
- 使用不同查询测试检索效果
- 调整检索参数优化结果
- 验证嵌入模型选择的影响

### 对话测试
- 测试不同类型的用户问题
- 验证知识库信息的正确使用
- 检查回答的准确性和相关性

### 调试模式
- 启用调试模式查看详细信息
- 分析检索过程和结果
- 优化提示词和配置

## 🚀 后续扩展

### 计划功能
1. **批量操作**: 批量配置多个智能体
2. **模板管理**: 智能体配置模板
3. **性能监控**: 对话质量和性能指标
4. **A/B测试**: 不同配置的效果对比
5. **导入导出**: 智能体配置的导入导出

### 技术优化
1. **缓存机制**: 模型列表和知识库列表缓存
2. **实时更新**: WebSocket实时状态更新
3. **离线支持**: 离线编辑和同步
4. **多语言支持**: 国际化支持

## 📞 技术支持

如有问题或建议，请联系开发团队或查看相关技术文档。
