<template>
  <div class="prompt-editor">
    <a-form-item 
      :label="label" 
      :validate-status="error ? 'error' : ''"
      :help="error"
    >
      <!-- 编辑器工具栏 -->
      <div class="editor-toolbar">
        <a-space size="small">
          <a-tooltip title="字符统计">
            <a-tag color="blue">{{ textStats.字符数 }} 字符</a-tag>
          </a-tooltip>
          <a-tooltip title="预估Token数">
            <a-tag color="green">{{ textStats.预估Token数 }} Token</a-tag>
          </a-tooltip>
          <a-tooltip title="变量数量">
            <a-tag color="orange">{{ textStats.变量数 }} 变量</a-tag>
          </a-tooltip>
          <a-tag v-if="textStats.字符数> 1000" color="red">
            建议精简
          </a-tag>
        </a-space>
      </div>

      <!-- 文本编辑器 -->
      <a-textarea
        v-model:value="localValue"
        :placeholder="placeholder"
        :rows="rows"
        :maxlength="maxLength"
        show-count
        class="prompt-textarea"
        @blur="handleBlur"
        @change="handleChange"
        @focus="handleFocus"
      />

      <!-- 语法检查 -->
      <div v-if="syntaxErrors.length > 0" class="syntax-errors">
        <a-alert
          type="warning"
          show-icon
          :message="`语法检查发现 ${syntaxErrors.length} 个问题`"
        >
          <template #description>
            <ul>
              <li v-for="(error, index) in syntaxErrors" :key="index">
                {{ error }}
              </li>
            </ul>
          </template>
        </a-alert>
      </div>

      <!-- 变量提示 -->
      <div v-if="detectedVariables.length > 0" class="variable-hints">
        <div class="hint-title">检测到的变量：</div>
        <a-space wrap>
          <a-tag 
            v-for="variable in detectedVariables" 
            :key="variable"
            color="processing"
            size="small"
          >
            {{ variable }}
          </a-tag>
        </a-space>
      </div>

      <!-- 编辑提示 -->
      <div class="editor-hints">
        <a-space size="small">
          <span class="hint-text">
            <InfoCircleOutlined /> 
            使用 {变量名} 格式插入变量
          </span>
          <a-button type="link" size="small" @click="showHelpModal = true">
            查看帮助
          </a-button>
        </a-space>
      </div>
    </a-form-item>

    <!-- 帮助弹窗 -->
    <a-modal
      v-model:open="showHelpModal"
      title="提示词编写帮助"
      width="600px"
      :footer="null"
    >
      <div class="help-content">
        <h4>变量使用</h4>
        <p>在提示词中使用 <code>{变量名}</code> 格式插入变量，例如：</p>
        <ul>
          <li><code>{用户名}</code> - 当前用户姓名</li>
          <li><code>{当前时间}</code> - 当前日期时间</li>
          <li><code>{公司名称}</code> - 公司或组织名称</li>
        </ul>

        <h4>最佳实践</h4>
        <ul>
          <li>保持提示词简洁明确</li>
          <li>使用具体的指令而非模糊描述</li>
          <li>合理使用变量提高复用性</li>
          <li>避免过长的提示词影响性能</li>
        </ul>

        <h4>语法规则</h4>
        <ul>
          <li>变量名使用中文或英文</li>
          <li>避免嵌套的变量定义</li>
          <li>确保括号匹配</li>
        </ul>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { InfoCircleOutlined } from '@ant-design/icons-vue'
import { usePromptTemplates } from '../../composables/usePromptTemplates'
import { useFormValidation } from '../../composables/useFormValidation'

// Props
const props = defineProps({
  value: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: '提示词'
  },
  placeholder: {
    type: String,
    default: '请输入提示词内容'
  },
  error: {
    type: String,
    default: null
  },
  rows: {
    type: Number,
    default: 8
  },
  maxLength: {
    type: Number,
    default: 10000
  }
})

// Emits
const emit = defineEmits(['update:value', 'change', 'validate'])

// 组合式函数
const { 
  提取模板变量, 
  验证模板语法, 
  计算文本统计 
} = usePromptTemplates()

const { 验证提示词长度 } = useFormValidation()

// 本地状态
const showHelpModal = ref(false)
const isFocused = ref(false)

// 本地值
const localValue = computed({
  get: () => props.value,
  set: (value) => emit('update:value', value)
})

// 文本统计
const textStats = computed(() => {
  return 计算文本统计(localValue.value)
})

// 检测到的变量
const detectedVariables = computed(() => {
  return 提取模板变量(localValue.value || '')
})

// 语法错误
const syntaxErrors = computed(() => {
  return 验证模板语法(localValue.value || '')
})

// 处理事件
const handleChange = () => {
  emit('change', localValue.value)
}

const handleBlur = () => {
  isFocused.value = false
  validateContent()
}

const handleFocus = () => {
  isFocused.value = true
}

// 验证内容
const validateContent = () => {
  const error = 验证提示词长度(localValue.value, props.label)
  emit('validate', error)
}

// 监听值变化
watch(() => localValue.value, () => {
  if (props.error) {
    validateContent()
  }
}, { immediate: true })
</script>

<style scoped>
.prompt-editor {
  width: 100%;
}

.editor-toolbar {
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.prompt-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
}

.prompt-textarea:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.syntax-errors {
  margin-top: 8px;
}

.syntax-errors ul {
  margin: 0;
  padding-left: 16px;
}

.variable-hints {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
}

.hint-title {
  font-size: 12px;
  color: #52c41a;
  margin-bottom: 4px;
  font-weight: 500;
}

.editor-hints {
  margin-top: 8px;
  padding: 4px 0;
}

.hint-text {
  font-size: 12px;
  color: #8c8c8c;
  display: flex;
  align-items: center;
  gap: 4px;
}

.help-content h4 {
  margin: 16px 0 8px 0;
  color: #262626;
}

.help-content p {
  margin: 8px 0;
  color: #595959;
}

.help-content ul {
  margin: 8px 0;
  padding-left: 20px;
}

.help-content li {
  margin: 4px 0;
  color: #595959;
}

.help-content code {
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>
