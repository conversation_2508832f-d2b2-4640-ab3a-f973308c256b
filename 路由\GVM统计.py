"""
GVM统计路由
提供销售额统计相关的API接口

接口列表：
1. GET /gvm/user/{user_id}/stats - 获取用户销售额统计
2. GET /gvm/team/{team_id}/stats - 获取团队销售额统计
3. POST /gvm/my/stats - 获取当前用户销售额统计
4. GET /gvm/dashboard - 获取GVM仪表板数据
"""

from typing import Optional

from fastapi import APIRouter, Depends, Query
from pydantic import BaseModel, Field

from 依赖项.认证 import 获取当前用户
from 日志 import 接口日志器
from 服务.GVM统计服务 import GVM统计服务实例


# 请求模型
class 统计查询请求模型(BaseModel):
    """统计查询请求模型"""

    时间范围: str = Field(default="30d", description="时间范围：7d, 30d, 90d, all")
    开始时间: Optional[str] = Field(
        default=None, description="自定义开始时间 YYYY-MM-DD"
    )
    结束时间: Optional[str] = Field(
        default=None, description="自定义结束时间 YYYY-MM-DD"
    )


# 创建路由器
路由器 = APIRouter(prefix="/gvm", tags=["GVM统计"])


@路由器.get("/user/{user_id}/stats")
async def 获取用户销售额统计接口(
    user_id: int,
    时间范围: str = Query("30d", description="时间范围：7d, 30d, 90d, all"),
    开始时间: Optional[str] = Query(None, description="自定义开始时间 YYYY-MM-DD"),
    结束时间: Optional[str] = Query(None, description="自定义结束时间 YYYY-MM-DD"),
    当前用户=Depends(获取当前用户),
):
    """
    获取指定用户的销售额统计

    参数:
    - user_id: 用户ID
    - 时间范围: 预设时间范围（7d, 30d, 90d, all）
    - 开始时间: 自定义开始时间（可选）
    - 结束时间: 自定义结束时间（可选）
    """
    接口日志器.info(f"用户 {当前用户['id']} 请求获取用户 {user_id} 的销售额统计")

    return await GVM统计服务实例.获取用户销售额统计(
        user_id, 时间范围, 开始时间, 结束时间
    )


@路由器.get("/team/{team_id}/stats")
async def 获取团队销售额统计接口(
    team_id: int,
    时间范围: str = Query("30d", description="时间范围：7d, 30d, 90d, all"),
    开始时间: Optional[str] = Query(None, description="自定义开始时间 YYYY-MM-DD"),
    结束时间: Optional[str] = Query(None, description="自定义结束时间 YYYY-MM-DD"),
    当前用户=Depends(获取当前用户),
):
    """
    获取指定团队的销售额统计

    参数:
    - team_id: 团队ID
    - 时间范围: 预设时间范围（7d, 30d, 90d, all）
    - 开始时间: 自定义开始时间（可选）
    - 结束时间: 自定义结束时间（可选）
    """
    接口日志器.info(f"用户 {当前用户['id']} 请求获取团队 {team_id} 的销售额统计")

    return await GVM统计服务实例.获取团队销售额统计(
        team_id, 时间范围, 开始时间, 结束时间
    )


@路由器.post("/my/stats")
async def 获取我的销售额统计接口(
    请求数据: 统计查询请求模型,
    当前用户=Depends(获取当前用户),
):
    """
    获取当前用户的销售额统计

    参数:
    - 时间范围: 预设时间范围（7d, 30d, 90d, all）
    - 开始时间: 自定义开始时间（可选）
    - 结束时间: 自定义结束时间（可选）
    """
    接口日志器.info(f"用户 {当前用户['id']} 请求获取自己的销售额统计")

    return await GVM统计服务实例.获取用户销售额统计(
        当前用户["id"], 请求数据.时间范围, 请求数据.开始时间, 请求数据.结束时间
    )


@路由器.get("/dashboard")
async def 获取GVM仪表板数据接口(
    时间范围: str = Query("30d", description="时间范围：7d, 30d, 90d, all"),
    当前用户=Depends(获取当前用户),
):
    """
    获取GVM仪表板数据（包含用户个人和团队数据）

    参数:
    - 时间范围: 预设时间范围（7d, 30d, 90d, all）
    """
    接口日志器.info(f"用户 {当前用户['id']} 请求获取GVM仪表板数据")

    用户id = 当前用户["id"]
    团队id = 当前用户.get("team_id")

    # 获取用户个人统计
    用户统计 = await GVM统计服务实例.获取用户销售额统计(用户id, 时间范围)

    # 获取团队统计（如果用户有团队）
    团队统计 = None
    if 团队id:
        团队统计 = await GVM统计服务实例.获取团队销售额统计(团队id, 时间范围)

    return {
        "status": "success",
        "message": "获取GVM仪表板数据成功",
        "data": {
            "用户统计": 用户统计.get("data"),
            "团队统计": 团队统计.get("data") if 团队统计 else None,
            "时间范围": 时间范围,
        },
    }


@路由器.get("/stats/summary")
async def 获取统计概览接口(当前用户=Depends(获取当前用户)):
    """
    获取统计概览（多个时间维度的对比数据）
    """
    接口日志器.info(f"用户 {当前用户['id']} 请求获取统计概览")

    用户id = 当前用户["id"]

    # 获取不同时间范围的统计数据
    今日统计 = await GVM统计服务实例.获取用户销售额统计(用户id, "1d")
    本周统计 = await GVM统计服务实例.获取用户销售额统计(用户id, "7d")
    本月统计 = await GVM统计服务实例.获取用户销售额统计(用户id, "30d")

    return {
        "status": "success",
        "message": "获取统计概览成功",
        "data": {
            "今日": 今日统计.get("data", {}).get("统计数据", {}),
            "本周": 本周统计.get("data", {}).get("统计数据", {}),
            "本月": 本月统计.get("data", {}).get("统计数据", {}),
        },
    }
