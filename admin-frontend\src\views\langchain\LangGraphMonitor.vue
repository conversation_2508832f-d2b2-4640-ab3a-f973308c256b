<template>
  <div class="langgraph-monitor">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>LangGraph智能体监控</h1>
      <p>实时监控智能体状态、工具调用和性能指标</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="活跃智能体"
              :value="stats.activeAgents"
              :value-style="{ color: '#3f8600' }"
            >
              <template #prefix>
                <robot-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="工具调用次数"
              :value="stats.totalToolCalls"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <tool-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="成功率"
              :value="stats.successRate"
              suffix="%"
              :value-style="{ color: '#cf1322' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="平均响应时间"
              :value="stats.avgResponseTime"
              suffix="ms"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-row :gutter="16">
        <!-- 左侧：智能体列表和对话测试 -->
        <a-col :span="12">
          <!-- 智能体选择 -->
          <a-card title="智能体选择" class="agent-selector">
            <a-select
              v-model:value="selectedAgentId"
              placeholder="选择智能体"
              style="width: 100%"
              @change="onAgentChange"
            >
              <a-select-option
                v-for="agent in agents"
                :key="agent.id"
                :value="agent.id"
              >
                {{ agent.智能体名称 }} ({{ agent.模型名称 }})
              </a-select-option>
            </a-select>
          </a-card>

          <!-- 对话测试 -->
          <a-card title="对话测试" class="chat-test">
            <div class="chat-container">
              <div class="chat-messages" ref="chatMessagesRef">
                <div
                  v-for="(message, index) in chatMessages"
                  :key="index"
                  :class="['message', message.type]"
                >
                  <div class="message-content">
                    <div class="message-text">{{ message.content }}</div>
                    <div class="message-time">{{ formatTime(message.timestamp) }}</div>
                  </div>
                </div>
              </div>
              
              <div class="chat-input">
                <a-input-group compact>
                  <a-input
                    v-model:value="inputMessage"
                    placeholder="输入消息..."
                    @press-enter="sendMessage"
                    :disabled="isLoading"
                  />
                  <a-button
                    type="primary"
                    @click="sendMessage"
                    :loading="isLoading"
                  >
                    发送
                  </a-button>
                </a-input-group>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 右侧：监控面板 -->
        <a-col :span="12">
          <!-- 实时状态 -->
          <a-card title="实时状态" class="real-time-status">
            <a-descriptions :column="1" size="small">
              <a-descriptions-item label="当前线程ID">
                {{ currentThreadId || '未开始对话' }}
              </a-descriptions-item>
              <a-descriptions-item label="智能体类型">
                {{ agentType }}
              </a-descriptions-item>
              <a-descriptions-item label="LangGraph状态">
                <a-tag :color="langGraphAvailable ? 'green' : 'red'">
                  {{ langGraphAvailable ? '可用' : '兼容模式' }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="最后更新">
                {{ lastUpdateTime }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>

          <!-- 工具调用监控 -->
          <a-card title="工具调用监控" class="tool-monitor">
            <a-table
              :columns="toolColumns"
              :data-source="toolCalls"
              :pagination="false"
              size="small"
              :scroll="{ y: 200 }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                  <a-tag
                    :color="getStatusColor(record.执行状态)"
                  >
                    {{ getStatusText(record.执行状态) }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'time'">
                  {{ record.执行时间 ? `${record.执行时间}s` : '-' }}
                </template>
              </template>
            </a-table>
          </a-card>

          <!-- 执行步骤 -->
          <a-card title="执行步骤" class="execution-steps">
            <a-timeline size="small">
              <a-timeline-item
                v-for="step in executionSteps"
                :key="step.id"
                :color="getStepColor(step.步骤状态)"
              >
                <template #dot>
                  <loading-outlined v-if="step.步骤状态 === 'running'" />
                  <check-circle-outlined v-else-if="step.步骤状态 === 'completed'" />
                  <close-circle-outlined v-else-if="step.步骤状态 === 'failed'" />
                  <clock-circle-outlined v-else />
                </template>
                <div class="step-content">
                  <div class="step-name">{{ step.步骤名称 }}</div>
                  <div class="step-type">{{ step.步骤类型 }}</div>
                  <div class="step-time">{{ formatTime(step.开始时间) }}</div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <a-space>
        <a-button @click="refreshData">
          <template #icon>
            <reload-outlined />
          </template>
          刷新数据
        </a-button>
        <a-button @click="clearCache">
          <template #icon>
            <delete-outlined />
          </template>
          清理缓存
        </a-button>
        <a-button @click="exportData">
          <template #icon>
            <download-outlined />
          </template>
          导出数据
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  RobotOutlined,
  ToolOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  LoadingOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  DeleteOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import { langGraphService } from '@/services/langGraphService'
import { adminLangChainService } from '@/services/adminLangchainService'

// 响应式数据
const selectedAgentId = ref(null)
const agents = ref([])
const inputMessage = ref('')
const isLoading = ref(false)
const chatMessages = ref([])
const currentThreadId = ref('')
const agentType = ref('langgraph')
const langGraphAvailable = ref(false)
const lastUpdateTime = ref('')

// 统计数据
const stats = reactive({
  activeAgents: 0,
  totalToolCalls: 0,
  successRate: 0,
  avgResponseTime: 0
})

// 工具调用数据
const toolCalls = ref([])
const executionSteps = ref([])

// 表格列定义
const toolColumns = [
  { title: '工具名称', dataIndex: '工具名称', key: 'name' },
  { title: '状态', key: 'status' },
  { title: '执行时间', key: 'time' },
  { title: '开始时间', dataIndex: '开始时间', key: 'startTime' }
]

// 组件引用
const chatMessagesRef = ref(null)

// 生命周期
onMounted(async () => {
  await loadAgents()
  await loadStats()
})

// 方法
const loadAgents = async () => {
  try {
    const response = await adminLangChainService.getAgents()
    agents.value = response.data || []
  } catch (error) {
    message.error('加载智能体列表失败')
  }
}

const loadStats = async () => {
  try {
    const response = await langGraphService.getStats()
    if (response.success) {
      stats.activeAgents = response.cache_stats?.cached_agents || 0
      stats.totalToolCalls = response.tool_stats?.总工具数 || 0
      // 这里可以添加更多统计数据的处理
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const onAgentChange = (agentId) => {
  selectedAgentId.value = agentId
  chatMessages.value = []
  currentThreadId.value = ''
}

const sendMessage = async () => {
  if (!inputMessage.value.trim() || !selectedAgentId.value) {
    message.warning('请选择智能体并输入消息')
    return
  }

  isLoading.value = true
  
  // 添加用户消息
  chatMessages.value.push({
    type: 'user',
    content: inputMessage.value,
    timestamp: new Date()
  })

  const userInput = inputMessage.value
  inputMessage.value = ''

  try {
    const response = await langGraphService.streamChat({
      message: userInput,
      agent_id: selectedAgentId.value,
      thread_id: currentThreadId.value,
      agent_type: agentType.value
    })

    if (response.success) {
      // 添加AI响应
      chatMessages.value.push({
        type: 'assistant',
        content: response.response,
        timestamp: new Date()
      })

      // 更新状态信息
      currentThreadId.value = response.thread_id
      langGraphAvailable.value = response.langgraph_available || response.react_available
      lastUpdateTime.value = new Date().toLocaleString()

      // 加载工具调用和执行步骤
      await loadToolMonitor()
    }
  } catch (error) {
    message.error('发送消息失败')
    chatMessages.value.push({
      type: 'error',
      content: '发送失败，请重试',
      timestamp: new Date()
    })
  } finally {
    isLoading.value = false
    await nextTick()
    scrollToBottom()
  }
}

const loadToolMonitor = async () => {
  if (!currentThreadId.value) return

  try {
    const response = await langGraphService.getToolMonitor({
      thread_id: currentThreadId.value
    })

    if (response.success) {
      toolCalls.value = response.tool_calls || []
      executionSteps.value = response.execution_steps || []
    }
  } catch (error) {
    console.error('加载工具监控数据失败:', error)
  }
}

const refreshData = async () => {
  await Promise.all([
    loadStats(),
    loadToolMonitor()
  ])
  message.success('数据已刷新')
}

const clearCache = async () => {
  try {
    const response = await langGraphService.clearCache()
    if (response.success) {
      message.success(response.message)
      await loadStats()
    }
  } catch (error) {
    message.error('清理缓存失败')
  }
}

const exportData = () => {
  const data = {
    chatMessages: chatMessages.value,
    toolCalls: toolCalls.value,
    executionSteps: executionSteps.value,
    stats: stats
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `langgraph-monitor-${new Date().toISOString().slice(0, 10)}.json`
  a.click()
  URL.revokeObjectURL(url)
}

const scrollToBottom = () => {
  if (chatMessagesRef.value) {
    chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight
  }
}

const formatTime = (time) => {
  if (!time) return ''
  return new Date(time).toLocaleTimeString()
}

const getStatusColor = (status) => {
  const colors = {
    success: 'green',
    failed: 'red',
    running: 'blue',
    pending: 'orange'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    success: '成功',
    failed: '失败',
    running: '运行中',
    pending: '等待中'
  }
  return texts[status] || status
}

const getStepColor = (status) => {
  const colors = {
    completed: 'green',
    failed: 'red',
    running: 'blue',
    pending: 'gray'
  }
  return colors[status] || 'gray'
}
</script>

<style scoped>
.langgraph-monitor {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 8px 0 0 0;
  color: #666;
}

.stats-cards {
  margin-bottom: 24px;
}

.main-content {
  margin-bottom: 24px;
}

.agent-selector,
.chat-test,
.real-time-status,
.tool-monitor,
.execution-steps {
  margin-bottom: 16px;
}

.chat-container {
  height: 400px;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  margin-bottom: 16px;
}

.message {
  margin-bottom: 16px;
}

.message.user .message-content {
  background: #1890ff;
  color: white;
  margin-left: 20%;
}

.message.assistant .message-content {
  background: #f0f0f0;
  margin-right: 20%;
}

.message.error .message-content {
  background: #ff4d4f;
  color: white;
  margin-right: 20%;
}

.message-content {
  padding: 12px 16px;
  border-radius: 12px;
  max-width: 80%;
}

.message-text {
  margin-bottom: 4px;
}

.message-time {
  font-size: 12px;
  opacity: 0.7;
}

.chat-input {
  margin-top: auto;
}

.step-content {
  font-size: 12px;
}

.step-name {
  font-weight: 600;
  margin-bottom: 2px;
}

.step-type {
  color: #666;
  margin-bottom: 2px;
}

.step-time {
  color: #999;
}

.action-buttons {
  text-align: center;
  padding: 24px 0;
  border-top: 1px solid #f0f0f0;
}
</style>
