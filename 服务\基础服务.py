"""
基础服务层 - 三层分离架构的业务逻辑层基类
提供统一的服务层接口和通用功能
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from 日志 import 错误日志器, 应用日志器


class 基础服务(ABC):
    """
    基础服务层抽象类
    定义了服务层的通用接口和功能
    """
    
    def __init__(self, 服务名称: str):
        """
        初始化基础服务
        
        参数:
            服务名称: 服务的名称，用于日志记录
        """
        self.服务名称 = 服务名称
        self.日志器 = 应用日志器
        
    def 记录信息(self, 消息: str, **kwargs):
        """记录信息日志"""
        完整消息 = f"[{self.服务名称}] {消息}"
        if kwargs:
            完整消息 += f" - {kwargs}"
        self.日志器.info(完整消息)
        
    def 记录错误(self, 消息: str, 异常: Exception = None, **kwargs):
        """记录错误日志"""
        完整消息 = f"[{self.服务名称}] {消息}"
        if kwargs:
            完整消息 += f" - {kwargs}"
        if 异常:
            完整消息 += f" - 异常: {str(异常)}"
        错误日志器.error(完整消息)
        
    def 记录警告(self, 消息: str, **kwargs):
        """记录警告日志"""
        完整消息 = f"[{self.服务名称}] {消息}"
        if kwargs:
            完整消息 += f" - {kwargs}"
        self.日志器.warning(完整消息)
        
    def 构建成功响应(self, 数据: Any = None, 消息: str = "操作成功") -> Dict[str, Any]:
        """
        构建成功响应
        
        参数:
            数据: 返回的数据
            消息: 成功消息
            
        返回:
            标准化的成功响应
        """
        return {
            "status": "success",
            "message": 消息,
            "data": 数据,
            "timestamp": datetime.now().isoformat()
        }
        
    def 构建失败响应(self, 消息: str = "操作失败", 错误码: str = None) -> Dict[str, Any]:
        """
        构建失败响应
        
        参数:
            消息: 失败消息
            错误码: 错误代码
            
        返回:
            标准化的失败响应
        """
        响应 = {
            "status": "error",
            "message": 消息,
            "timestamp": datetime.now().isoformat()
        }
        
        if 错误码:
            响应["error_code"] = 错误码
            
        return 响应
        
    async def 执行安全操作(self, 操作函数, 操作名称: str, **kwargs) -> Dict[str, Any]:
        """
        安全执行操作，统一异常处理
        
        参数:
            操作函数: 要执行的操作函数
            操作名称: 操作名称，用于日志记录
            **kwargs: 传递给操作函数的参数
            
        返回:
            操作结果
        """
        try:
            self.记录信息(f"开始执行{操作名称}", **kwargs)
            
            # 执行操作
            if asyncio.iscoroutinefunction(操作函数):
                结果 = await 操作函数(**kwargs)
            else:
                结果 = 操作函数(**kwargs)
                
            self.记录信息(f"{操作名称}执行成功")
            return 结果
            
        except Exception as e:
            self.记录错误(f"{操作名称}执行失败", e, **kwargs)
            return self.构建失败响应(f"{操作名称}失败: {str(e)}")


class 数据服务基类(基础服务):
    """
    数据服务基类
    专门用于数据相关的服务层
    """
    
    def __init__(self, 服务名称: str, 数据访问层实例=None):
        """
        初始化数据服务
        
        参数:
            服务名称: 服务名称
            数据访问层实例: 对应的数据访问层实例
        """
        super().__init__(服务名称)
        self.数据访问层 = 数据访问层实例
        
    def 验证必需参数(self, 参数字典: Dict[str, Any], 必需字段: List[str]) -> Optional[str]:
        """
        验证必需参数
        
        参数:
            参数字典: 要验证的参数字典
            必需字段: 必需的字段列表
            
        返回:
            如果验证失败，返回错误消息；否则返回None
        """
        缺失字段 = []
        for 字段 in 必需字段:
            if 字段 not in 参数字典 or 参数字典[字段] is None:
                缺失字段.append(字段)
                
        if 缺失字段:
            return f"缺少必需参数: {', '.join(缺失字段)}"
            
        return None
        
    def 清理字符串参数(self, 值: Any) -> Optional[str]:
        """
        清理字符串参数
        
        参数:
            值: 要清理的值
            
        返回:
            清理后的字符串，如果为空则返回None
        """
        if 值 is None:
            return None
            
        清理后的值 = str(值).strip()
        return 清理后的值 if 清理后的值 else None
        
    async def 分页查询(
        self, 
        查询函数, 
        页码: int = 1, 
        每页数量: int = 20, 
        **查询参数
    ) -> Dict[str, Any]:
        """
        通用分页查询
        
        参数:
            查询函数: 查询函数
            页码: 页码
            每页数量: 每页数量
            **查询参数: 其他查询参数
            
        返回:
            分页查询结果
        """
        try:
            # 参数验证
            if 页码 < 1:
                页码 = 1
            if 每页数量 < 1 or 每页数量 > 100:
                每页数量 = 20
                
            # 计算偏移量
            偏移量 = (页码 - 1) * 每页数量
            
            # 执行查询
            if asyncio.iscoroutinefunction(查询函数):
                结果 = await 查询函数(
                    限制=每页数量,
                    偏移量=偏移量,
                    **查询参数
                )
            else:
                结果 = 查询函数(
                    限制=每页数量,
                    偏移量=偏移量,
                    **查询参数
                )
                
            return self.构建成功响应({
                "列表": 结果.get("列表", []),
                "总数": 结果.get("总数", 0),
                "页码": 页码,
                "每页数量": 每页数量,
                "总页数": (结果.get("总数", 0) + 每页数量 - 1) // 每页数量
            })
            
        except Exception as e:
            self.记录错误("分页查询失败", e, 页码=页码, 每页数量=每页数量)
            return self.构建失败响应("查询失败")


# 导入asyncio用于异步操作检查
import asyncio
