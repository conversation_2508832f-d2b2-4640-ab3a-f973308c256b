<template>
  <div class="sales-trend-chart">
    <div 
      ref="chartContainer" 
      :style="{ height: height }"
      class="chart-container"
    ></div>
    
    <div v-if="loading" class="chart-loading">
      <a-spin size="large" />
    </div>
    
    <div v-if="!loading && (!data || data.length === 0)" class="chart-empty">
      <a-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import gvmService from '../../services/gvmService'

defineOptions({
  name: 'SalesTrendChart'
})

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    default: 'line', // 'line' | 'bar'
    validator: (value) => ['line', 'bar'].includes(value)
  },
  height: {
    type: String,
    default: '300px'
  },
  loading: {
    type: Boolean,
    default: false
  },
  showDataZoom: {
    type: Boolean,
    default: true
  },
  showToolbox: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['dataPointClick'])

const chartContainer = ref(null)
let chartInstance = null

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  updateChart()
  
  // 添加点击事件监听
  chartInstance.on('click', (params) => {
    emit('dataPointClick', params)
  })
}

// 更新图表
const updateChart = () => {
  if (!chartInstance || props.loading) return
  
  const colors = gvmService.getChartColors()
  const option = getChartOption(colors)
  
  chartInstance.setOption(option, true)
}

// 获取图表配置
const getChartOption = (colors) => {
  if (!props.data || props.data.length === 0) {
    return {}
  }
  
  // 处理数据
  const dates = props.data.map(item => item.date)
  const salesData = props.data.map(item => item.sales || 0)
  const ordersData = props.data.map(item => item.orders || 0)
  
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: (params) => {
        let html = `<div style="margin-bottom: 4px;">${params[0].axisValue}</div>`
        params.forEach(param => {
          const value = param.seriesName === '销售额' 
            ? gvmService.formatAmount(param.value)
            : `${param.value}单`
          html += `
            <div style="display: flex; align-items: center; margin-bottom: 2px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="margin-right: 8px;">${param.seriesName}:</span>
              <span style="font-weight: 600;">${value}</span>
            </div>
          `
        })
        return html
      }
    },
    legend: {
      data: ['销售额', '订单数'],
      top: 10,
      textStyle: {
        color: '#666'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: props.showDataZoom ? '15%' : '3%',
      top: '15%',
      containLabel: true
    },
    toolbox: props.showToolbox ? {
      feature: {
        dataZoom: {
          yAxisIndex: 'none'
        },
        restore: {},
        saveAsImage: {
          name: '销售趋势图'
        }
      },
      right: 20,
      top: 10
    } : undefined,
    xAxis: [
      {
        type: 'category',
        boundaryGap: props.type === 'bar',
        data: dates,
        axisPointer: {
          type: 'shadow'
        },
        axisLabel: {
          color: '#666',
          formatter: (value) => {
            // 格式化日期显示
            const date = new Date(value)
            return `${date.getMonth() + 1}/${date.getDate()}`
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '销售额',
        position: 'left',
        axisLabel: {
          color: '#666',
          formatter: (value) => gvmService.formatAmount(value)
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: colors.primary
          }
        }
      },
      {
        type: 'value',
        name: '订单数',
        position: 'right',
        axisLabel: {
          color: '#666',
          formatter: '{value}单'
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: colors.success
          }
        }
      }
    ],
    dataZoom: props.showDataZoom ? [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100,
        height: 30,
        bottom: 20
      }
    ] : undefined,
    series: [
      {
        name: '销售额',
        type: props.type,
        yAxisIndex: 0,
        data: salesData,
        itemStyle: {
          color: colors.primary
        },
        areaStyle: props.type === 'line' ? {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: colors.primary + '40' },
            { offset: 1, color: colors.primary + '10' }
          ])
        } : undefined,
        smooth: props.type === 'line',
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 3
        }
      },
      {
        name: '订单数',
        type: props.type,
        yAxisIndex: 1,
        data: ordersData,
        itemStyle: {
          color: colors.success
        },
        areaStyle: props.type === 'line' ? {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: colors.success + '40' },
            { offset: 1, color: colors.success + '10' }
          ])
        } : undefined,
        smooth: props.type === 'line',
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 3
        }
      }
    ],
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }
}

// 响应式处理
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 监听数据变化
watch(() => props.data, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

// 监听图表类型变化
watch(() => props.type, () => {
  nextTick(() => {
    updateChart()
  })
})

// 监听加载状态
watch(() => props.loading, (newLoading) => {
  if (!newLoading) {
    nextTick(() => {
      updateChart()
    })
  }
})

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
  })
  
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  
  window.removeEventListener('resize', handleResize)
})

// 暴露方法给父组件
defineExpose({
  refreshChart: updateChart,
  getChartInstance: () => chartInstance
})
</script>

<style scoped>
.sales-trend-chart {
  position: relative;
  width: 100%;
}

.chart-container {
  width: 100%;
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.chart-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .chart-loading {
    background: rgba(31, 31, 31, 0.8);
  }
}
</style>
