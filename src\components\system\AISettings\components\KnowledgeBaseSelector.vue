<template>
  <div class="knowledge-base-selector">
    <!-- 搜索栏 -->
    <div class="selector-header">
      <a-input
        v-model:value="searchKeyword"
        placeholder="搜索知识库..."
        allow-clear
        class="search-input"
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
      <div class="selection-info">
        <a-tag color="blue">已选择 {{ selectedCount }} 个知识库</a-tag>
      </div>
    </div>

    <!-- 知识库卡片网格 -->
    <div class="knowledge-cards-container">
      <div v-if="loading" class="loading-state">
        <a-spin size="large" tip="加载知识库列表..." />
      </div>
      
      <div v-else-if="filteredKnowledgeBases.length === 0" class="empty-state">
        <a-empty description="暂无可用知识库" />
      </div>
      
      <div v-else class="knowledge-cards-grid">
        <div
          v-for="kb in filteredKnowledgeBases"
          :key="kb.value"
          class="knowledge-card"
          :class="{
            'selected': isSelected(kb.value),
            'loading': isToggling(kb.value)
          }"
          @click="handleToggleKnowledge(kb)"
        >
          <!-- 选中状态指示器 -->
          <div class="selection-indicator">
            <CheckCircleFilled v-if="isSelected(kb.value)" class="selected-icon" />
            <div v-else class="unselected-circle"></div>
          </div>

          <!-- 加载状态 -->
          <div v-if="isToggling(kb.value)" class="loading-overlay">
            <a-spin size="small" />
          </div>

          <!-- 知识库信息 -->
          <div class="card-content">
            <div class="kb-header">
              <h4 class="kb-name">{{ kb.label }}</h4>
              <a-tag 
                :color="getStatusColor(kb.status)" 
                size="small"
                class="status-tag"
              >
                {{ getStatusText(kb.status) }}
              </a-tag>
            </div>
            
            <p class="kb-description">{{ kb.description || '暂无描述' }}</p>
            
            <div class="kb-stats">
              <div class="stat-item">
                <FileTextOutlined />
                <span>{{ kb.documentCount || 0 }} 文档</span>
              </div>
              <div class="stat-item">
                <ClockCircleOutlined />
                <span>{{ formatDate(kb.updateTime) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { 
  SearchOutlined, 
  CheckCircleFilled, 
  FileTextOutlined, 
  ClockCircleOutlined 
} from '@ant-design/icons-vue'

// Props
const props = defineProps({
  knowledgeBases: {
    type: Array,
    default: () => []
  },
  selectedValues: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['toggle'])

// 响应式数据
const searchKeyword = ref('')
const togglingIds = ref(new Set())

// 计算属性
const selectedCount = computed(() => props.selectedValues.length)

const filteredKnowledgeBases = computed(() => {
  if (!searchKeyword.value.trim()) {
    return props.knowledgeBases
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return props.knowledgeBases.filter(kb => 
    kb.label.toLowerCase().includes(keyword) ||
    (kb.description && kb.description.toLowerCase().includes(keyword))
  )
})

// 方法
const isSelected = (value) => {
  return props.selectedValues.includes(value)
}

const isToggling = (value) => {
  return togglingIds.value.has(value)
}

const handleToggleKnowledge = async (kb) => {
  if (props.disabled || isToggling(kb.value)) {
    return
  }

  try {
    togglingIds.value.add(kb.value)
    
    // 触发切换事件
    await emit('toggle', kb.value)
    
  } catch (error) {
    console.error('切换知识库关联失败:', error)
    message.error('操作失败，请重试')
  } finally {
    togglingIds.value.delete(kb.value)
  }
}

const getStatusColor = (status) => {
  const colorMap = {
    'active': 'success',
    'inactive': 'default',
    'processing': 'processing',
    'error': 'error'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status) => {
  const textMap = {
    'active': '正常',
    'inactive': '停用',
    'processing': '处理中',
    'error': '异常'
  }
  return textMap[status] || '未知'
}

const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  try {
    return new Date(dateStr).toLocaleDateString('zh-CN')
  } catch {
    return '未知'
  }
}
</script>

<style scoped>
.knowledge-base-selector {
  width: 100%;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;
}

.search-input {
  flex: 1;
  max-width: 300px;
}

.selection-info {
  flex-shrink: 0;
}

.knowledge-cards-container {
  min-height: 200px;
}

.loading-state,
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.knowledge-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
  padding: 4px;
}

.knowledge-card {
  position: relative;
  background: #fff;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.knowledge-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  transform: translateY(-2px);
}

.knowledge-card.selected {
  border-color: #1890ff;
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.knowledge-card.loading {
  pointer-events: none;
  opacity: 0.7;
}

.selection-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 2;
}

.selected-icon {
  color: #1890ff;
  font-size: 18px;
}

.unselected-circle {
  width: 18px;
  height: 18px;
  border: 2px solid #d9d9d9;
  border-radius: 50%;
  transition: border-color 0.3s ease;
}

.knowledge-card:hover .unselected-circle {
  border-color: #1890ff;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  z-index: 3;
}

.card-content {
  padding-right: 30px;
}

.kb-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.kb-name {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  line-height: 1.4;
  flex: 1;
  margin-right: 8px;
}

.status-tag {
  flex-shrink: 0;
}

.kb-description {
  margin: 0 0 12px 0;
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.kb-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #8c8c8c;
}

.stat-item .anticon {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .selector-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input {
    max-width: none;
  }
  
  .knowledge-cards-grid {
    grid-template-columns: 1fr;
  }
}
</style>
