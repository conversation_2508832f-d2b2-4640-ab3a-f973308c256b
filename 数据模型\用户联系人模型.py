from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class 寄样信息模型(BaseModel):
    """寄样信息模型"""

    收件人: str = Field(..., max_length=50, description="收件人姓名")
    地址: str = Field(..., max_length=200, description="寄样地址")
    电话: str = Field(..., max_length=20, description="联系电话")


class 用户联系人响应模型(BaseModel):
    """用户联系人响应模型"""

    用户联系人id: UUID = Field(..., description="联系人UUID")
    姓名: str = Field(..., description="联系人姓名")
    用户表id: int = Field(..., description="所属用户id")
    寄样信息: Optional[List[寄样信息模型]] = Field(None, description="寄样信息列表")


class 查询用户联系人请求模型(BaseModel):
    """查询用户联系人列表请求模型"""

    关键词: Optional[str] = Field(
        None, max_length=100, description="搜索关键词（姓名或联系方式）"
    )


class 更新用户联系人请求模型(BaseModel):
    """更新用户联系人请求模型"""

    用户联系人id: UUID = Field(..., description="用户联系人UUID")
    姓名: Optional[str] = Field(None, max_length=50, description="联系人姓名")
    寄样信息: Optional[List[寄样信息模型]] = Field(None, description="寄样信息列表")


class 达人联系方式信息模型(BaseModel):
    """达人联系方式信息模型"""

    联系方式: str = Field(..., max_length=50, description="联系方式内容")
    联系方式类型: str = Field(..., max_length=20, description="联系方式类型")
    个人备注: Optional[str] = Field(None, description="个人备注")
    个人标签: Optional[List[str]] = Field(None, description="个人标签列表")
    补充信息: Optional[str] = Field(None, description="补充信息")


class 创建联系人并关联达人补充信息请求模型(BaseModel):
    """创建联系人并关联达人补充信息请求模型（完整业务流程）"""

    姓名: str = Field(..., max_length=50, description="联系人姓名")
    寄样信息: Optional[List[寄样信息模型]] = Field(None, description="寄样信息列表")
    联系方式: str = Field(..., max_length=100, description="联系方式内容")
    联系方式类型: str = Field(..., max_length=20, description="联系方式类型")
    个人备注: Optional[str] = Field(None, description="个人备注")
    个人标签: Optional[List[str]] = Field(None, description="个人标签列表")
    补充信息: Optional[str] = Field(None, description="补充信息")
