/**
 * 知识库管理服务
 * 处理知识库和文档相关的API调用
 */

import api from './api'

class KnowledgeBaseService {
  constructor() {
    this.baseUrl = '/knowledge'
  }

  /**
   * 获取用户知识库列表
   * @returns {Promise<Object>} 用户的知识库列表
   */
  async 获取知识库列表() {
    try {
      const response = await api.post(`${this.baseUrl}/list`, {})
      return response
    } catch (error) {
      console.error('获取知识库列表失败:', error)
      throw error
    }
  }

  /**
   * 从Coze平台获取知识库列表
   * @returns {Promise<Object>} Coze平台的知识库列表
   */
  async 从coze平台获取知识库列表() {
    try {
      // 注意：这个方法暂时使用数据库获取，因为路由统一了
      // 后续可以在服务层区分数据来源
      const response = await api.post(`${this.baseUrl}/list`, {})
      return response
    } catch (error) {
      console.error('从Coze平台获取知识库列表失败:', error)
      throw error
    }
  }

  /**
   * 获取知识库文档列表（含Coze状态同步）
   * @param {Object} params - 请求参数
   * @param {number} params.知识库表id - 知识库表id
   * @param {number} params.页码 - 页码，默认为1
   * @param {number} params.每页数量 - 每页记录数，默认为10
   * @param {boolean} params.同步状态 - 是否同步Coze状态，默认为true
   * @returns {Promise<Object>} 文档列表和分页信息
   */
  async 获取知识库文档列表(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/documents/list`, {
        知识库表id: params.知识库表id,
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 10,
        同步状态: params.同步状态 !== false // 默认为true
      })
      return response
    } catch (error) {
      console.error('获取知识库文档列表失败:', error)
      throw error
    }
  }

  /**
   * 创建新的知识库
   * @param {Object} params - 请求参数
   * @param {string} params.名称 - 知识库名称
   * @param {string} params.类型 - 知识库类型，默认为"自定义"
   * @param {string} params.描述 - 知识库描述
   * @param {number} params.模型id - 关联的AI模型id
   * @param {string} params.系统提示词 - 系统提示词
   * @param {string} params.用户提示词模板 - 用户提示词模板
   * @param {boolean} params.是否公开 - 是否公开
   * @param {boolean} params.自动编译 - 是否自动编译
   * @param {boolean} params.版本控制 - 是否启用版本控制
   * @param {Array} params.MCP工具配置 - MCP工具配置列表
   * @returns {Promise<Object>} 创建结果
   */
  async 创建知识库(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/create`, {
        名称: params.名称,
        类型: params.类型 || '自定义',
        描述: params.描述 || '',
        模型id: params.模型id,
        系统提示词: params.系统提示词 || '',
        用户提示词模板: params.用户提示词模板 || '',
        是否公开: params.是否公开 || false,
        自动编译: params.自动编译 !== false, // 默认为true
        版本控制: params.版本控制 || false,
        MCP工具配置: params.MCP工具配置 || []
      })
      return response
    } catch (error) {
      console.error('创建知识库失败:', error)
      throw error
    }
  }

  /**
   * 获取指定知识库的文档列表
   * @param {Object} params - 请求参数
   * @param {number} params.知识id - 知识id
   * @param {number} params.页码 - 页码，默认为1
   * @param {number} params.每页数量 - 每页记录数，默认为10
   * @param {string} params.搜索关键词 - 搜索关键词，可选
   * @param {string} params.状态筛选 - 状态筛选，可选
   * @param {string} params.类型筛选 - 类型筛选，可选
   * @returns {Promise<Object>} 文档列表和分页信息
   */
  async getDocumentList(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/documents/list`, {
        知识id: params.知识id,
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 10,
        搜索关键词: params.搜索关键词,
        状态筛选: params.状态筛选,
        类型筛选: params.类型筛选
      })
      return response
    } catch (error) {
      console.error('获取文档列表失败:', error)
      throw error
    }
  }

  /**
   * 获取文档详情
   * @param {Object} params - 请求参数
   * @param {number} params.文档id - 文档id
   * @returns {Promise<Object>} 文档详情
   */
  async getDocumentDetail(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/documents/detail`, {
        文档id: params.文档id
      })
      return response
    } catch (error) {
      console.error('获取文档详情失败:', error)
      throw error
    }
  }

  /**
   * 创建新文档
   * @param {Object} params - 请求参数
   * @param {number} params.知识id - 知识id
   * @param {string} params.文档名称 - 文档名称
   * @param {string} params.文档内容 - 文档内容
   * @returns {Promise<Object>} 创建结果
   */
  async createDocument(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/documents/create`, {
        知识id: params.知识id,
        文档名称: params.文档名称,
        文档内容: params.文档内容
      })
      return response
    } catch (error) {
      console.error('创建文档失败:', error)
      throw error
    }
  }

  /**
   * 更新文档
   * @param {Object} params - 请求参数
   * @param {number} params.文档id - 文档id
   * @param {string} params.文档名称 - 文档名称
   * @param {string} params.文档内容 - 文档内容
   * @returns {Promise<Object>} 更新结果
   */
  async updateDocument(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/documents/update`, {
        文档id: params.文档id,
        文档名称: params.文档名称,
        文档内容: params.文档内容
      })
      return response
    } catch (error) {
      console.error('更新文档失败:', error)
      throw error
    }
  }

  /**
   * 删除知识库文档
   * @param {Object} params - 请求参数
   * @param {number} params.知识库表id - 知识库表id
   * @param {Array<string>} params.文档id列表 - 要删除的Coze文档id列表
   * @returns {Promise<Object>} 删除结果
   */
  async 删除知识库文档(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/delete_documents`, {
        知识库表id: params.知识库表id,
        文档id列表: params.文档id列表 || []
      })
      return response
    } catch (error) {
      console.error('删除知识库文档失败:', error)
      throw error
    }
  }

  /**
   * 获取知识库统计信息
   * @param {Object} params - 请求参数
   * @param {number} params.知识id - 知识id
   * @returns {Promise<Object>} 统计信息
   */
  async getKnowledgeBaseStats(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/stats`, {
        知识id: params.知识id
      })
      return response
    } catch (error) {
      console.error('获取知识库统计信息失败:', error)
      throw error
    }
  }

  /**
   * 搜索文档
   * @param {Object} params - 请求参数
   * @param {number} params.知识id - 知识id，可选
   * @param {string} params.搜索关键词 - 搜索关键词
   * @param {number} params.页码 - 页码，默认为1
   * @param {number} params.每页数量 - 每页记录数，默认为10
   * @returns {Promise<Object>} 搜索结果
   */
  async searchDocuments(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/documents/search`, {
        知识id: params.知识id,
        搜索关键词: params.搜索关键词,
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 10
      })
      return response
    } catch (error) {
      console.error('搜索文档失败:', error)
      throw error
    }
  }

  /**
   * 同步知识库文档（从Coze平台同步最新状态）
   * @param {Object} params - 请求参数
   * @param {number} params.知识id - 知识id
   * @returns {Promise<Object>} 同步结果
   */
  async syncKnowledgeBase(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/sync`, {
        知识id: params.知识id
      })
      return response
    } catch (error) {
      console.error('同步知识库失败:', error)
      throw error
    }
  }

  /**
   * 导出知识库文档
   * @param {Object} params - 请求参数
   * @param {number} params.知识id - 知识id
   * @param {string} params.导出格式 - 导出格式（json, txt, csv）
   * @returns {Promise<Object>} 导出结果
   */
  async exportKnowledgeBase(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/export`, {
        知识id: params.知识id,
        导出格式: params.导出格式 || 'json'
      })
      return response
    } catch (error) {
      console.error('导出知识库失败:', error)
      throw error
    }
  }

  /**
   * 获取文档版本历史
   * @param {Object} params - 请求参数
   * @param {number} params.文档id - 文档id
   * @returns {Promise<Object>} 版本历史
   */
  async getDocumentHistory(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/documents/history`, {
        文档id: params.文档id
      })
      return response
    } catch (error) {
      console.error('获取文档版本历史失败:', error)
      throw error
    }
  }

  /**
   * 恢复文档到指定版本
   * @param {Object} params - 请求参数
   * @param {number} params.文档id - 文档id
   * @param {number} params.版本id - 版本ID
   * @returns {Promise<Object>} 恢复结果
   */
  async restoreDocumentVersion(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/documents/restore`, {
        文档id: params.文档id,
        版本id: params.版本id
      })
      return response
    } catch (error) {
      console.error('恢复文档版本失败:', error)
      throw error
    }
  }

  /**
   * 获取知识库文件上传进度
   * @param {Object} params - 请求参数
   * @param {number} params.知识库表id - 知识库表id
   * @param {Array<string>} params.文档id列表 - 要查询的文档id列表（可选）
   * @returns {Promise<Object>} 文档上传进度信息
   */
  async 获取文件上传进度(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/upload_progress`, {
        知识库表id: params.知识库表id,
        文档id列表: params.文档id列表 || []
      })
      return response
    } catch (error) {
      console.error('获取文件上传进度失败:', error)
      throw error
    }
  }

  /**
   * 检查指定文档的上传状态
   * @param {number} 知识库表id - 知识库表id
   * @param {Array<string>} 文档id列表 - 文档id列表
   * @returns {Promise<Object>} 上传状态信息
   */
  async 检查文档上传状态(知识库表id, 文档id列表) {
    try {
      const response = await this.获取文件上传进度({
        知识库表id,
        文档id列表
      })

      if (response.data && response.data.进度列表) {
        // 统计各种状态的文档数量
        const 状态统计 = {
          处理中: 0,
          处理完成: 0,
          处理失败: 0,
          未知: 0
        }

        response.data.进度列表.forEach(文档 => {
          const 状态 = 文档.处理状态 || '未知'
          状态统计[状态] = (状态统计[状态] || 0) + 1
        })

        return {
          ...response,
          状态统计
        }
      }

      return response
    } catch (error) {
      console.error('检查文档上传状态失败:', error)
      throw error
    }
  }
}

// 创建并导出服务实例
const knowledgeBaseService = new KnowledgeBaseService()
export default knowledgeBaseService
