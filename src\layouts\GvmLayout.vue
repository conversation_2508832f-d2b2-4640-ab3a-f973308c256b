<template>
  <div class="gvm-layout">
    <!-- 页面头部 -->
    <div class="gvm-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <BarChartOutlined class="title-icon" />
            业绩中心
          </h1>
          <div class="page-description">
            全面掌握销售业绩，数据驱动业务增长
          </div>
        </div>
        <div class="header-right">
          <slot name="header-actions"></slot>
        </div>
      </div>
    </div>

    <!-- 导航标签页 -->
    <div class="gvm-nav">
      <a-tabs 
        v-model:activeKey="activeTab" 
        @change="handleTabChange"
        class="gvm-tabs"
        size="large"
      >
        <a-tab-pane key="my-performance" tab="我的业绩">
          <template #tab>
            <UserOutlined />
            我的业绩
          </template>
        </a-tab-pane>
        
        <a-tab-pane key="team-performance" tab="团队业绩" v-if="showTeamTab">
          <template #tab>
            <TeamOutlined />
            团队业绩
          </template>
        </a-tab-pane>
        
        <a-tab-pane key="performance-ranking" tab="业绩排行" v-if="showRankingTab">
          <template #tab>
            <TrophyOutlined />
            业绩排行
          </template>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 主要内容区域 -->
    <div class="gvm-content">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '../store/user'
import {
  BarChartOutlined,
  UserOutlined,
  TeamOutlined,
  TrophyOutlined
} from '@ant-design/icons-vue'

defineOptions({
  name: 'GvmLayout'
})

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 当前激活的标签页
const activeTab = ref('my-performance')

// 计算是否显示团队标签页
const showTeamTab = computed(() => {
  // 根据用户权限或团队状态决定是否显示
  return userStore.userInfo?.team_id || userStore.hasTeamPermission
})

// 计算是否显示排行榜标签页
const showRankingTab = computed(() => {
  // 可以根据业务需求决定是否显示排行榜
  return true
})

// 监听路由变化，更新激活的标签页
watch(
  () => route.name,
  (newName) => {
    if (newName === 'MyPerformance') {
      activeTab.value = 'my-performance'
    } else if (newName === 'TeamPerformance') {
      activeTab.value = 'team-performance'
    } else if (newName === 'PerformanceRanking') {
      activeTab.value = 'performance-ranking'
    }
  },
  { immediate: true }
)

// 处理标签页切换
const handleTabChange = (key) => {
  activeTab.value = key
  
  switch (key) {
    case 'my-performance':
      router.push('/gvm/my-performance')
      break
    case 'team-performance':
      router.push('/gvm/team-performance')
      break
    case 'performance-ranking':
      router.push('/gvm/performance-ranking')
      break
  }
}
</script>

<style scoped>
.gvm-layout {
  min-height: 100vh;
  background: #f5f5f5;
}

.gvm-header {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  padding: 24px 0;
}

.header-content {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: #1890ff;
  font-size: 32px;
}

.page-description {
  margin-top: 8px;
  color: #8c8c8c;
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.gvm-nav {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 24px;
  max-width: 1600px;
  margin: 0 auto;
}

.gvm-tabs {
  margin: 0;
}

.gvm-tabs :deep(.ant-tabs-nav) {
  margin-bottom: 0;
}

.gvm-tabs :deep(.ant-tabs-tab) {
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 500;
}

.gvm-tabs :deep(.ant-tabs-tab-active) {
  color: #1890ff;
}

.gvm-content {
  max-width: 1600px;
  margin: 0 auto;
  padding: 24px;
  min-height: calc(100vh - 200px);
}

/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .gvm-nav {
    padding: 0 16px;
  }
  
  .gvm-content {
    padding: 16px;
  }
  
  .gvm-tabs :deep(.ant-tabs-tab) {
    padding: 12px 16px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 20px;
  }
  
  .title-icon {
    font-size: 24px;
  }
  
  .gvm-tabs :deep(.ant-tabs-tab) {
    padding: 10px 12px;
    font-size: 13px;
  }
}
</style>
