"""
管理员用户管理API路由
提供用户管理相关的API接口
主要功能：
1. 用户CRUD操作
2. 用户状态管理
3. 用户权限管理
4. 用户统计和分析
"""

from fastapi import APIRouter, Body, Depends

from 依赖项.认证 import 获取当前管理员用户
from 数据.管理_统一入口 import (
    异步删除用户,
    异步批量操作用户,
    异步更新用户,
    异步更新用户状态,
    异步添加用户,
    异步获取用户信息,
    异步获取用户列表,
    异步获取用户接口调用历史分页,
    异步获取用户数据导出,
    异步获取用户行为分析,
    异步获取用户详细统计,
    异步获取用户邀约统计,
)
from 数据模型.SuperAdmin_模型 import 用户列表请求模型 as 用户列表请求
from 数据模型.SuperAdmin_模型 import 用户创建请求模型 as 用户创建请求
from 数据模型.SuperAdmin_模型 import 用户批量操作请求模型 as 用户批量操作请求
from 数据模型.SuperAdmin_模型 import 用户更新请求模型 as 用户更新请求
from 数据模型.SuperAdmin_模型 import 用户状态更新请求模型 as 用户状态更新请求
from 数据模型.SuperAdmin_模型 import 用户详情请求模型 as 用户详情请求
from 数据模型.响应模型 import 统一响应模型
from 日志 import 安全日志器, 接口日志器, 错误日志器
from 服务.服务_用户权限业务处理 import (
    异步处理_用户关联店铺查询_支持分页,
    异步处理_用户安全审计查询_生成审计报告,
    异步处理_用户权限详情查询_包含会员和权限信息,
    异步处理_用户登录历史查询_支持分页,
)

# 创建用户管理路由
用户管理路由 = APIRouter(tags=["管理员-用户管理"])

# ==================== 用户基础管理 ====================


@用户管理路由.post("/list", summary="获取用户列表")
async def 获取用户列表接口(
    请求参数: 用户列表请求 = Body(...), 当前用户: dict = Depends(获取当前管理员用户)
):
    """获取用户列表"""
    try:
        接口日志器.info(f"管理员 {当前用户.get('id')} 请求用户列表")

        # {{ AURA-X: Add - 添加排序参数传递，简洁高效直接对接. Approval: 寸止(ID:1721062800). }}
        用户列表 = await 异步获取用户列表(
            页码=请求参数.页码,
            每页数量=请求参数.每页数量,
            搜索关键词=请求参数.搜索关键词,
            排序字段=请求参数.排序字段,
            排序顺序=请求参数.排序顺序,
        )

        return 统一响应模型.成功(数据=用户列表, 消息="获取用户列表成功")

    except Exception as e:
        错误日志器.error(f"获取用户列表失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户列表失败")


@用户管理路由.post("/detail", summary="获取用户详情")
async def 获取用户详情接口(
    请求参数: 用户详情请求 = Body(...), 当前用户: dict = Depends(获取当前管理员用户)
):
    """获取用户详情"""
    try:
        用户信息 = await 异步获取用户信息(请求参数.用户id)

        if 用户信息:
            return 统一响应模型.成功(数据=用户信息, 消息="获取用户详情成功")
        else:
            return 统一响应模型.失败(状态码=404, 消息="用户不存在")

    except Exception as e:
        错误日志器.error(f"获取用户详情失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户详情失败")


@用户管理路由.post("/create", summary="创建用户")
async def 创建用户接口(
    请求参数: 用户创建请求, 当前用户: dict = Depends(获取当前管理员用户)
):
    """创建新用户"""
    try:
        创建结果 = await 异步添加用户(
            用户名=请求参数.用户名,
            密码=请求参数.密码,
            邮箱=请求参数.邮箱 or "",
            手机号=请求参数.手机号 or "",
        )

        if 创建结果.get("success"):
            安全日志器.info(
                f"管理员 {当前用户.get('id')} 创建了用户: {请求参数.用户名}"
            )
            return 统一响应模型.成功(
                数据={"用户id": 创建结果.get("用户id")}, 消息="用户创建成功"
            )
        else:
            return 统一响应模型.失败(
                状态码=400, 消息=创建结果.get("message", "创建用户失败")
            )

    except Exception as e:
        错误日志器.error(f"创建用户失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="创建用户失败")


@用户管理路由.post("/update", summary="更新用户")
async def 更新用户接口(
    请求参数: 用户更新请求, 当前用户: dict = Depends(获取当前管理员用户)
):
    """更新用户信息"""
    try:
        # 构建更新数据字典
        更新数据 = {}
        if hasattr(请求参数, '用户名') and 请求参数.用户名:
            更新数据["昵称"] = 请求参数.用户名
        if hasattr(请求参数, '邮箱') and 请求参数.邮箱:
            更新数据["邮箱"] = 请求参数.邮箱

        更新结果 = await 异步更新用户(
            用户id=请求参数.用户id, 更新数据=更新数据
        )

        if 更新结果.get("success"):
            安全日志器.info(f"管理员 {当前用户.get('id')} 更新了用户 {请求参数.用户id}")
            return 统一响应模型.成功(消息="用户更新成功")
        else:
            return 统一响应模型.失败(
                状态码=400, 消息=更新结果.get("message", "更新用户失败")
            )

    except Exception as e:
        错误日志器.error(f"更新用户失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="更新用户失败")


@用户管理路由.post("/delete", summary="删除用户")
async def 删除用户接口(
    请求参数: dict = Body(...), 当前用户: dict = Depends(获取当前管理员用户)
):
    """删除用户"""
    try:
        用户id = 请求参数.get("用户id")
        if not 用户id:
            return 统一响应模型.失败(状态码=400, 消息="缺少用户id")

        删除结果 = await 异步删除用户(用户id)

        if 删除结果.get("success"):
            安全日志器.info(f"管理员 {当前用户.get('id')} 删除了用户 {用户id}")
            return 统一响应模型.成功(消息="用户删除成功")
        else:
            return 统一响应模型.失败(
                状态码=400, 消息=删除结果.get("message", "删除用户失败")
            )

    except Exception as e:
        错误日志器.error(f"删除用户失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="删除用户失败")


# ==================== 用户状态管理 ====================


@用户管理路由.post("/status/update", summary="更新用户状态")
async def 更新用户状态接口(
    请求参数: 用户状态更新请求, 当前用户: dict = Depends(获取当前管理员用户)
):
    """更新用户状态"""
    try:
        更新结果 = await 异步更新用户状态(
            用户id=请求参数.用户id,
            新状态=请求参数.状态
        )

        if 更新结果.get("success"):
            安全日志器.info(
                f"管理员 {当前用户.get('id')} 更新用户 {请求参数.用户id} 状态为 {请求参数.状态}"
            )
            return 统一响应模型.成功(消息="用户状态更新成功")
        else:
            return 统一响应模型.失败(
                状态码=400, 消息=更新结果.get("message", "更新用户状态失败")
            )

    except Exception as e:
        错误日志器.error(f"更新用户状态失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="更新用户状态失败")


@用户管理路由.post("/batch", summary="批量操作用户")
async def 批量操作用户接口(
    请求参数: 用户批量操作请求, 当前用户: dict = Depends(获取当前管理员用户)
):
    """批量操作用户"""
    try:
        操作结果 = await 异步批量操作用户(
            用户id列表=请求参数.用户id列表,
            操作类型=请求参数.操作类型,
            操作参数=请求参数.操作参数 or {}
        )

        if 操作结果.get("success"):
            安全日志器.info(
                f"管理员 {当前用户.get('id')} 批量{请求参数.操作类型}用户: {请求参数.用户id列表}"
            )
            return 统一响应模型.成功(数据=操作结果.get("data"), 消息="批量操作成功")
        else:
            return 统一响应模型.失败(
                状态码=400, 消息=操作结果.get("message", "批量操作失败")
            )

    except Exception as e:
        错误日志器.error(f"批量操作用户失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="批量操作失败")


# ==================== 用户统计和分析 ====================


@用户管理路由.post("/stats/detailed", summary="获取用户详细统计")
async def 获取用户详细统计接口(
    请求参数: dict = Body(...), _: dict = Depends(获取当前管理员用户)
):
    """获取用户详细统计信息"""
    try:
        用户id = 请求参数.get("用户id")
        if not 用户id:
            return 统一响应模型.失败(状态码=400, 消息="缺少用户id")

        统计数据 = await 异步获取用户详细统计(用户id)
        return 统一响应模型.成功(数据=统计数据, 消息="获取用户详细统计成功")

    except Exception as e:
        错误日志器.error(f"获取用户详细统计失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户详细统计失败")


@用户管理路由.post("/history/login", summary="获取用户登录历史")
async def 获取用户登录历史接口_旧路径(
    请求参数: dict = Body(...), _: dict = Depends(获取当前管理员用户)
):
    """获取用户登录历史分页 - 兼容旧路径"""
    try:
        用户id = 请求参数.get("用户id")
        页码 = 请求参数.get("页码", 1)
        每页数量 = 请求参数.get("每页数量", 20)

        if not 用户id:
            return 统一响应模型.失败(状态码=400, 消息="缺少用户id")

        历史数据 = await 异步处理_用户登录历史查询_支持分页(用户id, 页码, 每页数量)
        return 统一响应模型.成功(数据=历史数据, 消息="获取用户登录历史成功")

    except Exception as e:
        错误日志器.error(f"获取用户登录历史失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户登录历史失败")


@用户管理路由.post("/login-history", summary="获取用户登录历史")
async def 获取用户登录历史接口(
    请求参数: dict = Body(...), _: dict = Depends(获取当前管理员用户)
):
    """获取用户登录历史分页"""
    try:
        用户id = 请求参数.get("用户id") or 请求参数.get("用户id")
        页码 = 请求参数.get("页码", 1)
        每页数量 = 请求参数.get("每页数量", 20)

        if not 用户id:
            return 统一响应模型.失败(状态码=400, 消息="缺少用户id")

        历史数据 = await 异步处理_用户登录历史查询_支持分页(用户id, 页码, 每页数量)
        return 统一响应模型.成功(数据=历史数据, 消息="获取用户登录历史成功")

    except Exception as e:
        错误日志器.error(f"获取用户登录历史失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户登录历史失败")


@用户管理路由.post("/api-history", summary="获取用户接口调用历史")
async def 获取用户接口调用历史接口(
    请求参数: dict = Body(...), _: dict = Depends(获取当前管理员用户)
):
    """获取用户接口调用历史分页"""
    try:
        用户id = 请求参数.get("用户id")
        页码 = 请求参数.get("页码", 1)
        每页数量 = 请求参数.get("每页数量", 20)

        if not 用户id:
            return 统一响应模型.失败(状态码=400, 消息="缺少用户id")

        历史数据 = await 异步获取用户接口调用历史分页(用户id, 页码, 每页数量)
        return 统一响应模型.成功(数据=历史数据, 消息="获取用户接口调用历史成功")

    except Exception as e:
        错误日志器.error(f"获取用户接口调用历史失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户接口调用历史失败")


@用户管理路由.post("/shops", summary="获取用户关联店铺")
async def 获取用户关联店铺接口(
    请求参数: dict = Body(...), _: dict = Depends(获取当前管理员用户)
):
    """获取用户关联店铺列表"""
    try:
        用户id = 请求参数.get("用户id")
        页码 = 请求参数.get("页码", 1)
        每页数量 = 请求参数.get("每页数量", 10)

        if not 用户id:
            return 统一响应模型.失败(状态码=400, 消息="缺少用户id")

        店铺数据 = await 异步处理_用户关联店铺查询_支持分页(用户id, 页码, 每页数量)
        return 统一响应模型.成功(数据=店铺数据, 消息="获取用户关联店铺成功")

    except Exception as e:
        错误日志器.error(f"获取用户关联店铺失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户关联店铺失败")


@用户管理路由.post("/statistics", summary="获取用户统计信息")
async def 获取用户统计信息接口(
    请求参数: dict = Body(...), _: dict = Depends(获取当前管理员用户)
):
    """获取用户统计信息"""
    try:
        用户id = 请求参数.get("用户id") or 请求参数.get("用户id")
        if not 用户id:
            return 统一响应模型.失败(状态码=400, 消息="缺少用户id")

        统计数据 = await 异步获取用户详细统计(用户id)
        return 统一响应模型.成功(数据=统计数据, 消息="获取用户统计信息成功")

    except Exception as e:
        错误日志器.error(f"获取用户统计信息失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户统计信息失败")


@用户管理路由.post("/invitation-stats", summary="获取用户邀约统计")
async def 获取用户邀约统计接口(
    请求参数: dict = Body(...), _: dict = Depends(获取当前管理员用户)
):
    """获取用户邀约统计"""
    try:
        用户id = 请求参数.get("用户id")
        if not 用户id:
            return 统一响应模型.失败(状态码=400, 消息="缺少用户id")

        统计数据 = await 异步获取用户邀约统计(用户id)
        return 统一响应模型.成功(数据=统计数据, 消息="获取用户邀约统计成功")

    except Exception as e:
        错误日志器.error(f"获取用户邀约统计失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户邀约统计失败")


@用户管理路由.post("/stats/invitation", summary="获取用户邀约统计")
async def 获取用户邀约统计接口_兼容(
    请求参数: dict = Body(...), _: dict = Depends(获取当前管理员用户)
):
    """获取用户邀约统计 - 兼容旧路径"""
    try:
        用户id = 请求参数.get("用户id")
        if not 用户id:
            return 统一响应模型.失败(状态码=400, 消息="缺少用户id")

        统计数据 = await 异步获取用户邀约统计(用户id)
        return 统一响应模型.成功(数据=统计数据, 消息="获取用户邀约统计成功")

    except Exception as e:
        错误日志器.error(f"获取用户邀约统计失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户邀约统计失败")


@用户管理路由.post("/permissions/details", summary="获取用户权限详情")
async def 获取用户权限详情接口(
    请求参数: dict = Body(...), _: dict = Depends(获取当前管理员用户)
):
    """获取用户权限详情"""
    try:
        用户id = 请求参数.get("用户id")
        if not 用户id:
            return 统一响应模型.失败(状态码=400, 消息="缺少用户id")

        权限详情 = await 异步处理_用户权限详情查询_包含会员和权限信息(用户id)
        return 统一响应模型.成功(数据=权限详情, 消息="获取用户权限详情成功")

    except Exception as e:
        错误日志器.error(f"获取用户权限详情失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户权限详情失败")


@用户管理路由.post("/audit/security", summary="获取用户安全审计")
async def 获取用户安全审计接口(
    请求参数: dict = Body(...), _: dict = Depends(获取当前管理员用户)
):
    """获取用户安全审计"""
    try:
        用户id = 请求参数.get("用户id")
        if not 用户id:
            return 统一响应模型.失败(状态码=400, 消息="缺少用户id")

        审计数据 = await 异步处理_用户安全审计查询_生成审计报告(用户id)
        return 统一响应模型.成功(数据=审计数据, 消息="获取用户安全审计成功")

    except Exception as e:
        错误日志器.error(f"获取用户安全审计失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户安全审计失败")


@用户管理路由.post("/export/data", summary="获取用户数据导出")
async def 获取用户数据导出接口(
    请求参数: dict = Body(...), _: dict = Depends(获取当前管理员用户)
):
    """获取用户数据导出"""
    try:
        用户id列表 = 请求参数.get("用户id列表", [])

        导出数据 = await 异步获取用户数据导出(用户id列表)
        return 统一响应模型.成功(数据=导出数据, 消息="获取用户数据导出成功")

    except Exception as e:
        错误日志器.error(f"获取用户数据导出失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户数据导出失败")


@用户管理路由.post("/analysis/behavior", summary="获取用户行为分析")
async def 获取用户行为分析接口(
    请求参数: dict = Body(...), _: dict = Depends(获取当前管理员用户)
):
    """获取用户行为分析"""
    try:
        用户id = 请求参数.get("用户id")
        分析类型 = 请求参数.get("分析类型", "活跃度")

        if not 用户id:
            return 统一响应模型.失败(状态码=400, 消息="缺少用户id")

        分析数据 = await 异步获取用户行为分析(用户id, 分析类型)
        return 统一响应模型.成功(数据=分析数据, 消息="获取用户行为分析成功")

    except Exception as e:
        错误日志器.error(f"获取用户行为分析失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户行为分析失败")
