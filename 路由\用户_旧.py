# 标准库模块
import traceback
from datetime import datetime
from typing import Optional

# 第三方库模块
from fastapi import APIRouter, Depends, HTTPException, Request, Response, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

import 状态

# 导入认证依赖项
from 依赖项.认证 import 获取当前用户

# 自定义模块
# from 数据 import 异步连接池  # 已迁移到PostgreSQL，使用Postgre_异步连接池实例
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

# 引入非用户相关函数
from 数据.异步数据库函数 import (
    异步更新用户最后登录时间,
    异步验证码系统,
)

# 引入用户相关函数
from 数据.用户 import (
    获取JWT认证信息 as 异步获取用户JWT认证信息,
    根据手机号获取用户信息 as 异步获取用户_电话,
    根据用户id获取用户信息 as 异步获取用户_id,
    获取用户昵称_id as 异步获取用户昵称_id,
)

# 引入联系方式相关函数
from 数据.线索数据操作 import 异步提交联系方式

# 新增：导入统一响应模型
from 数据模型.响应模型 import 统一响应模型, 设置昵称请求

# 导入统一日志系统
from 日志 import 安全日志器, 接口日志器, 错误日志器
from 服务.异步用户服务 import (
    异步发送验证码服务,
    异步处理权限状态,
    异步处理获取推广用户列表,
    异步密码服务,
    异步收到店铺信息,
    异步注册服务,
    异步激活账户,
    异步用户发送邀约请求,
    异步认证服务,
    # 新增导入
    异步设置用户昵称服务,
)
from 服务.支付服务 import 统一订单支付服务类

# -----------------------------
# 路由配置
# -----------------------------
用户路由 = APIRouter()


# -----------------------------
# 数据模型
# -----------------------------
class 数据类型_用户登录(BaseModel):
    phone: str
    password: str


class 验证码请求(BaseModel):
    phone: str
    类型: str


class 注册请求(BaseModel):
    phone: str
    password: str
    verification_code: str
    invite_code: Optional[str] = Field(None, description="邀请码（可选）")


class 修改密码请求(BaseModel):
    old_password: str
    new_password: str


class 重置密码请求(BaseModel):
    phone: str
    verification_code: str
    new_password: str


class 激活请求(BaseModel):
    code: str


class 邀请kol请求(BaseModel):
    uid_number: str
    account_douyin: str
    昵称: str


class 登录店铺请求(BaseModel):
    shop_id: str
    shop_name: str
    avatar: str


# -----------------------------
# 数据模型
# -----------------------------
class 微信对接进度请求(BaseModel):
    我方微信号: str
    对方微信号: str
    好友状态: Optional[int] = None  # 改为整数类型
    回复状态: Optional[int] = None
    意向状态: Optional[int] = None
    合作状态: Optional[int] = None


# 添加在数据模型部分
class 联系方式提交请求(BaseModel):
    联系方式: str
    类型: Optional[str] = "微信"
    来源: str


class 推广用户列表请求(BaseModel):
    页码: int = 1
    每页数量: int = 10
    搜索关键词: Optional[str] = None


class 手机号查找用户请求(BaseModel):
    手机号: str


# -----------------------------
# 依赖项
# -----------------------------
# 统一使用依赖项.认证模块中的获取当前用户函数


# -----------------------------
# 路由处理函数
# -----------------------------


# PC端口旧版登录接口,先保留
@用户路由.post("/login")
async def 登录(接口数据: 数据类型_用户登录, 响应: Response, request: Request):
    """
    用户登录，返回 JWT 并设置Cookie
    :param 接口数据: 用户登录信息
    :param 响应: FastAPI Response对象，用于设置Cookie
    :param request: FastAPI Request对象，用于获取IP
    :return: JWT 字符串
    """
    try:
        接口日志器.info(
            f"开始登录流程: 手机号={接口数据.phone}, IP={(request.client.host if request.client else '未知IP')}"
        )
        用户数据 = await 异步获取用户_电话(接口数据.phone)

        if 用户数据 is None:
            安全日志器.warning(f"登录失败: 用户不存在 - {接口数据.phone}")
            return JSONResponse(
                status_code=200,
                content={
                    "status": 状态.用户.用户不存在,
                    "message": "该手机号尚未注册，请检查后重试或注册新账户。",
                    "data": None,
                },
            )

        # 检查用户是否为未注册状态（半注册用户不允许登录）
        if 用户数据.get("状态") == "未注册":
            安全日志器.warning(f"登录失败: 用户为未注册状态 - {接口数据.phone}")
            return JSONResponse(
                status_code=200,
                content={
                    "status": 状态.用户.用户不存在,
                    "message": "该手机号尚未完成注册，请先完成注册流程。",
                    "data": None,
                },
            )

        if 接口数据.password != 用户数据["password"]:
            安全日志器.warning(f"登录失败: 密码错误 - {接口数据.phone}")
            return JSONResponse(
                status_code=200,
                content={
                    "status": 状态.用户.密码错误,
                    "message": "您输入的密码不正确，请重试。如果忘记密码，可以尝试找回。",
                    "data": None,
                },
            )

        权限数据 = await 异步处理权限状态(用户数据["id"])

        if 权限数据 is None:
            安全日志器.warning(f"登录失败: 权限不足 - 用户id: {用户数据['id']}")
            return JSONResponse(
                status_code=200,
                content={
                    "status": 状态.用户.没有权限,
                    "message": "抱歉，您的账户目前没有足够的权限。",
                    "data": None,
                },
            )

        # 获取用户完整认证信息（包括会员信息和权限信息）
        完整认证信息 = await 异步获取用户JWT认证信息(用户数据["id"])

        # 如果获取完整信息失败，使用基本信息作为备用
        if not 完整认证信息:
            完整认证信息 = {
                "id": 用户数据["id"],
                "手机号": 用户数据["手机号"],
                "昵称": 用户数据.get("昵称", ""),
                "is_admin": 用户数据.get("is_admin", False),
                "会员信息": {
                    "会员id": None,
                    "会员名称": "免费用户",
                    "到期时间": None,
                    "算力点": 0,
                    "可创建团队数": 0,
                    "创建团队默认人数上限": 0,
                },
                "权限信息": [],
            }

        认证服务实例 = 异步认证服务()
        token = await 认证服务实例.生成令牌(完整认证信息)
        接口日志器.debug(f"令牌生成成功: {token[:20]}...")

        # 新增：更新用户最后登录时间
        if 用户数据 and "id" in 用户数据:
            login_time_updated = await 异步更新用户最后登录时间(用户数据["id"], request)
            if not login_time_updated:
                客户端IP = request.client.host if request.client else "未知IP"
                安全日志器.warning(
                    f"未能为用户id {用户数据['id']} (IP: {客户端IP}) 创建登录记录。但这不会中断登录流程。"
                )

        响应.set_cookie(key="token", value=token, httponly=True, max_age=1800)
        接口日志器.info(f"用户 {用户数据['手机号']} 登录成功，已设置Cookie。")

        return {
            "status": 状态.通用.成功_旧,
            "message": "欢迎回来！已成功登录。",
            "access_token": token,
            "token_type": "bearer",
            "权限": 权限数据,
        }
    except HTTPException as he:
        # 确保HTTP异常的格式统一
        if hasattr(he, "detail") and isinstance(he.detail, dict):
            if "data" not in he.detail:
                he.detail["data"] = None
            if "msg" in he.detail and "message" not in he.detail:
                he.detail["message"] = he.detail["msg"]
                del he.detail["msg"]
        raise he  # 直接抛出 HTTPException
    except Exception as e:
        错误日志器.error(f"登录接口失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": "登录服务暂时遇到问题，请稍后重试。如果问题持续，请联系客服支持。",
                "data": None,
            },
        )


@用户路由.get(
    "/get_permission",
    summary="权限检查接口",
    description="通过Authorization头验证用户权限",
)
async def 权限检查接口(用户: dict = Depends(获取当前用户)):
    """
    获取当前用户的权限信息
    :param 用户: 通过依赖注入的认证用户数据
    :return: 用户权限信息
    """
    try:
        # 使用已导入的异步处理权限状态
        用户权限 = await 异步处理权限状态(用户["id"])
        用户昵称 = await 异步获取用户昵称_id(用户["id"])

        # 修复字段名不一致问题：token中存储的是"手机号"，不是"phone"
        手机号 = 用户.get("手机号") or 用户.get("手机号", "")

        # 获取用户完整权限信息（新增功能）
        from 服务.异步用户服务 import 获取用户完整权限信息

        完整权限结果 = await 获取用户完整权限信息(
            用户id=用户["id"], 包含团队权限=True, 包含历史权限=False
        )

        # 提取代理类型信息和会员信息
        代理类型信息 = None
        会员信息 = None
        权限配置 = None

        if 完整权限结果.get("成功") and 完整权限结果.get("数据"):
            完整权限数据 = 完整权限结果["数据"]
            代理类型信息 = 完整权限数据.get("代理类型信息")
            会员信息 = 完整权限数据.get("会员信息")
            权限配置 = 完整权限数据.get("权限配置")

        return {
            "status": 状态.通用.获取权限成功,
            "message": "您的账户权限信息已成功获取。",
            "用户信息": {"id": 用户["id"], "手机号": 手机号, "昵称": 用户昵称},
            "权限": 用户权限,
            "代理类型信息": 代理类型信息,
            "会员信息": 会员信息,
            "权限配置": 权限配置,
        }
    except KeyError as ke:
        错误日志器.error(
            f"权限接口KeyError异常: {str(ke)}, 用户数据: {用户}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": "抱歉，权限系统当前似乎遇到了一些问题，我们正在紧急处理。",
                "data": None,
            },
        )
    except Exception as e:
        错误日志器.error(f"权限接口异常: {str(e)}, 用户数据: {用户}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": "抱歉，权限系统当前似乎遇到了一些问题，我们正在紧急处理。",
                "data": None,
            },
        )
    except Exception as e:
        错误日志器.error(f"权限检查接口失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": f"获取权限信息时遇到未知错误，请稍后重试。错误详情: {str(e)}",
                "data": None,
            },
        )


@用户路由.post(
    "/member-status", summary="检查用户会员状态", description="检查当前用户的会员状态"
)
async def 检查用户会员状态(用户: dict = Depends(获取当前用户)):
    """
    检查用户会员状态接口
    :param 用户: 通过依赖注入的认证用户数据
    :return: 用户会员状态信息
    """
    try:
        from 服务.会员服务 import 会员服务类

        会员服务 = 会员服务类()
        会员信息 = await 会员服务.获取用户会员信息(用户["id"])

        return {
            "status": 状态.通用.成功,
            "message": "会员状态获取成功",
            "data": 会员信息,
        }

    except Exception as e:
        错误日志器.error(f"检查用户会员状态失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": "获取会员状态失败，请稍后重试。",
                "data": None,
            },
        )


@用户路由.get("/get_order", summary="获取订单列表", description="获取当前用户订单列表")
async def 获取订单列表(用户: dict = Depends(获取当前用户)):
    """
    获取当前用户的订单列表
    :param 用户: 通过依赖注入的认证用户数据
    :return: 订单列表
    """
    try:
        订单服务实例 = 统一订单支付服务类()
        订单列表 = await 订单服务实例.获取用户订单列表(用户["id"])
        return {
            "status": 状态.通用.成功_旧,
            "message": "您的订单列表已成功加载。",
            "data": 订单列表,
        }
    except Exception as e:
        错误日志器.error(f"获取订单列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": "获取订单列表时遇到问题，请稍后再试。",
                "data": None,
            },
        )


@用户路由.post("/update_password")
async def 修改密码接口(请求数据: 修改密码请求, 用户: dict = Depends(获取当前用户)):
    """
    修改用户密码
    :param 请求数据: 包含旧密码和新密码的请求
    :param 用户: 通过依赖注入的认证用户数据
    :return: 操作结果
    """
    try:
        密码服务实例 = 异步密码服务()
        结果 = await 密码服务实例.修改密码(
            用户id=用户["id"],
            旧密码=请求数据.old_password,
            新密码=请求数据.new_password,
        )

        # 转换为统一格式
        if isinstance(结果, dict) and "status" in 结果:
            if "message" not in 结果 and "msg" in 结果:
                结果["message"] = 结果["msg"]
                del 结果["msg"]
            if "data" not in 结果:
                结果["data"] = None
            return 结果

        return {
            "status": 状态.通用.成功,
            "message": "您的密码已成功更新。请牢记新密码。",
            "data": 结果,
        }
    except HTTPException as he:
        # 确保HTTP异常的格式统一
        if hasattr(he, "detail") and isinstance(he.detail, dict):
            if "data" not in he.detail:
                he.detail["data"] = None
            if "msg" in he.detail and "message" not in he.detail:
                he.detail["message"] = he.detail["msg"]
                del he.detail["msg"]
        raise he  # 直接抛出 HTTPException
    except Exception as e:
        错误日志器.error(f"修改密码接口失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": "修改密码时遇到问题，请稍后再试。",
                "data": None,
            },
        )


@用户路由.post("/sms_code", summary="获取短信验证码", description="获取短信验证码")
async def 获取验证码接口(请求数据: 验证码请求):
    """
    发送短信验证码
    :param 请求数据: 包含电话号码的请求
    :return: 操作结果
    """
    try:
        # 参数验证
        if not 请求数据.phone:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "status": 状态.用户.手机号格式错误,
                    "message": "请输入有效的手机号码。",
                    "data": None,
                },
            )

        if 请求数据.类型 not in ["注册", "重置密码"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "status": 400,
                    "message": "抱歉，当前验证码类型无效。",
                    "data": None,
                },
            )

        # 使用异步验证码服务
        结果 = await 异步发送验证码服务(请求数据.phone, 请求数据.类型)

        # 转换为统一格式
        if isinstance(结果, dict) and "status" in 结果:
            if "message" not in 结果 and "msg" in 结果:
                结果["message"] = 结果["msg"]
                del 结果["msg"]
            if "data" not in 结果:
                结果["data"] = None
            return 结果

        return {
            "status": 状态.通用.成功_旧,
            "message": "验证码已发送，请注意查收短信。",
            "data": 结果,
        }
    except HTTPException as he:
        # 确保HTTP异常的格式统一
        if hasattr(he, "detail") and isinstance(he.detail, dict):
            if "data" not in he.detail:
                he.detail["data"] = None
            if "msg" in he.detail and "message" not in he.detail:
                he.detail["message"] = he.detail["msg"]
                del he.detail["msg"]
        raise he  # 直接抛出 HTTPException
    except Exception as e:
        错误日志器.error(f"获取验证码接口失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": "发送验证码时遇到问题，请稍后再试。",
                "data": None,
            },
        )


@用户路由.post("/register", response_model=统一响应模型)
async def 注册(请求: 注册请求, 响应: Response, request: Request):
    """
    用户注册
    :param 请求: 注册请求数据
    :param 响应: FastAPI响应对象
    :param request: FastAPI Request对象，用于获取IP
    :return: 注册结果 (统一响应模型)
    """
    try:
        service_result = await 异步注册服务.注册用户(
            phone=请求.phone,
            password=请求.password,
            verification_code=请求.verification_code,
        )

        # 修正判断逻辑：直接检查服务层返回的状态码
        if service_result and service_result.get("status") == 状态.通用.注册成功:
            用户id = service_result.get("data", {}).get("user_id")
            token = service_result.get("data", {}).get("token")

            if not 用户id or not token:
                错误日志器.error(
                    f"注册服务成功但未返回用户id或Token，手机号: {请求.phone}"
                )
                return 统一响应模型.失败(
                    状态码=状态.用户.注册失败, 消息="注册失败：无法生成用户凭证"
                )

            # 如果提供了邀请码，处理邀请关联
            if 请求.invite_code:
                try:
                    from 数据.客户邀请数据 import 处理用户注册邀请码关联

                    邀请关联结果 = await 处理用户注册邀请码关联(
                        请求.phone, 用户id, 请求.invite_code
                    )
                    if 邀请关联结果["success"]:
                        接口日志器.info(
                            f"用户 {请求.phone} 注册成功，邀请码 {请求.invite_code} 关联成功"
                        )
                    else:
                        接口日志器.warning(
                            f"用户 {请求.phone} 注册成功，但邀请码 {请求.invite_code} 关联失败: {邀请关联结果['message']}"
                        )
                except Exception as e:
                    错误日志器.error(f"处理邀请关联时发生异常: {e}", exc_info=True)
                    # 不影响注册流程，只记录错误

            # 更新用户最后登录时间 (注册成功视为首次登录)
            await 异步更新用户最后登录时间(用户id, request)

            # 设置Cookie
            响应.set_cookie(
                key="access_token",
                value=f"Bearer {token}",
                httponly=True,
                max_age=3600 * 24 * 7,  # 7天
                samesite="Lax",
            )
            # 获取用户基本信息用于前端
            用户信息 = await 异步获取用户_id(用户id)
            用户昵称 = await 异步获取用户昵称_id(用户id) if 用户信息 else ""

            接口日志器.info(f"用户 {请求.phone} 注册成功并自动登录。")
            return 统一响应模型.成功(
                数据={
                    "access_token": token,
                    "token_type": "bearer",
                    "用户信息": {
                        "id": 用户id,
                        "手机号": 请求.phone,
                        "昵称": 用户昵称 or "",
                        "头像": 用户信息.get("头像", "") if 用户信息 else "",
                    },
                },
                消息="注册成功",
            )
        else:
            业务消息 = service_result.get("message", "注册失败")
            业务状态码 = service_result.get("status", 状态.用户.注册失败)
            接口日志器.warning(
                f"注册接口业务异常: {业务消息}, 状态码: {业务状态码}, 请求手机: {请求.phone}"
            )
            return 统一响应模型.失败(状态码=业务状态码, 消息=业务消息)

    except HTTPException as http_exc:
        # FastAPI 会自动处理 HTTPException 并返回适当的 HTTP 响应
        # 我们只需要确保 detail 字段符合期望的 JSON 结构
        业务状态码 = (
            http_exc.detail.get("status")
            if isinstance(http_exc.detail, dict)
            else 状态.通用.服务器错误
        )
        业务消息 = (
            http_exc.detail.get("message")
            if isinstance(http_exc.detail, dict)
            else str(http_exc.detail)
        )

        接口日志器.warning(
            f"注册接口业务异常: {业务消息}, 状态码: {业务状态码}, 请求手机: {请求.phone}"
        )
        # 直接重新抛出，让 FastAPI 的异常处理器处理
        # 如果要完全控制返回的 JSON，可以捕获后返回 JSONResponse(content=统一响应模型.失败(...).转字典(), status_code=http_exc.status_code)
        raise http_exc
    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(
            f"注册接口发生意外错误: {str(e)}\\n{错误详情}，请求手机: {请求.phone}"
        )
        # 返回统一错误响应
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=统一响应模型.失败(
                状态码=状态.通用.服务器错误,
                消息="系统处理注册请求时发生内部错误，请稍后再试。",
            ).转字典(),
        )


@用户路由.post("/password/forgot/reset", response_model=统一响应模型)
async def 重置密码接口(请求数据: 重置密码请求):
    """
    重置密码
    :param 请求数据: 包含手机号、验证码和新密码的请求
    :return: 重置结果 (统一响应模型)
    """
    try:
        用户数据 = await 异步获取用户_电话(请求数据.phone)
        if not 用户数据:
            接口日志器.warning(f"重置密码尝试失败：用户不存在 - {请求数据.phone}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,  # 更合适的HTTP状态码
                detail={  # detail 保持业务错误结构
                    "status": 状态.用户.用户不存在,
                    "message": "抱歉，该手机号尚未注册。",  # MODIFIED
                    "data": None,
                },
            )

        if not await 异步验证码系统.验证验证码(
            请求数据.phone, 请求数据.verification_code, "重置密码"
        ):
            接口日志器.warning(f"重置密码尝试失败：验证码错误 - {请求数据.phone}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,  # 更合适的HTTP状态码
                detail={  # detail 保持业务错误结构
                    "status": 状态.用户.验证码错误,
                    "message": "您输入的验证码不正确或已过期，请重新获取。",  # MODIFIED
                    "data": None,
                },
            )

        密码服务实例 = 异步密码服务()
        # 假设 异步密码服务.重置密码 成功时不返回特定数据，如果操作失败会抛出异常
        await 密码服务实例.重置密码(用户id=用户数据["id"], 新密码=请求数据.new_password)

        接口日志器.info(f"用户 {请求数据.phone} 密码重置成功。")
        return 统一响应模型.成功(
            消息="密码已成功重置！您现在可以使用新密码登录了。"
        )  # MODIFIED

    except HTTPException as he:
        # 从 HTTPException 的 detail 中提取业务状态码和消息
        业务状态码 = (
            he.detail.get("status")
            if isinstance(he.detail, dict)
            else 状态.通用.服务器错误
        )
        业务消息 = (
            he.detail.get("message") if isinstance(he.detail, dict) else str(he.detail)
        )
        业务数据 = he.detail.get("data") if isinstance(he.detail, dict) else None

        # 记录日志已在抛出点完成，这里可以补充HTTPException的status_code
        # 接口日志器.warning(f"重置密码接口HTTP异常: {业务消息}, HTTP状态码: {he.status_code}, 业务状态码: {业务状态码}")

        # 重新抛出，携带规范化的detail，以便全局异常处理器可以生成统一格式的JSONResponse
        raise HTTPException(
            status_code=he.status_code,
            detail=统一响应模型.失败(
                状态码=业务状态码, 消息=业务消息, 数据=业务数据
            ).转字典(),
        )
    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(
            f"重置密码接口发生意外错误: {str(e)}\\n{错误详情}，请求手机: {请求数据.phone}"
        )
        # 对于未预料的异常，返回统一的服务器错误响应
        # 注意：FastAPI 的 response_model 在抛出非 HTTPException 时可能不会按预期工作，
        # 因此显式返回 JSONResponse 更安全，或者确保全局异常处理器能处理。
        # 但由于上面加了 response_model=统一响应模型，FastAPI 在遇到非HTTPException时，
        # 应该会尝试用500错误包装。为明确起见，我们还是构造一个HTTPException。
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=统一响应模型.失败(
                状态码=状态.通用.服务器错误,
                消息="系统处理密码重置请求时发生内部错误，请稍后再试。",
            ).转字典(),
        )


@用户路由.post("/activate_code", summary="激活账户", description="通过激活码激活账户")
async def 激活账户接口(请求数据: 激活请求, 用户: dict = Depends(获取当前用户)):
    """
    通过激活码激活账户
    :param 请求数据: 包含激活码的请求
    :param 用户: 通过依赖注入的认证用户数据
    :return: 激活结果
    """
    try:
        # 确保用户 ID 存在
        if not 用户 or "id" not in 用户:
            return {
                "status": 状态.激活.激活用户不存在,
                "message": "请先登录或注册账户，然后再尝试激活。",
                "data": None,
            }

        激活结果 = await 异步激活账户(用户["id"], 请求数据.code)

        # 转换为统一格式
        if isinstance(激活结果, dict) and "status" in 激活结果:
            if "message" not in 激活结果 and "msg" in 激活结果:
                激活结果["message"] = 激活结果["msg"]
                del 激活结果["msg"]
            if "data" not in 激活结果:
                激活结果["data"] = {
                    "激活码": 激活结果.get("激活码"),
                    "激活用户id": 激活结果.get("激活用户id"),
                    "会员表id": 激活结果.get("会员表id"),
                    "会员天数": 激活结果.get("会员天数"),
                }
            return 激活结果

        if 激活结果:
            # 使用异步处理权限状态
            权限内容 = await 异步处理权限状态(用户["id"])
            return {
                "status": 状态.通用.成功_旧,
                "message": "恭喜！您的账户已成功激活。",
                "data": {"权限内容": 权限内容},
            }
        else:
            return {
                "status": 状态.激活.激活失败,
                "message": "激活失败，可能是激活码无效或您的账户已激活。请检查后重试。",
                "data": None,
            }
    except Exception as e:
        # 捕获所有异常，统一返回200状态码和业务错误信息
        错误日志器.error(f"激活账户接口失败: {str(e)}", exc_info=True)

        # 如果是业务逻辑错误，提取错误信息
        if hasattr(e, "detail") and isinstance(e.detail, dict):
            return {
                "status": e.detail.get("status", 状态.通用.服务器错误),
                "message": e.detail.get("message", "激活失败"),
                "data": e.detail.get("data"),
                "error_type": e.detail.get("error_type"),
                "input_phone": e.detail.get("input_phone"),
            }

        return {
            "status": 状态.通用.服务器错误,
            "message": "激活服务暂时遇到问题，请稍后重试。",
            "data": None,
        }


@用户路由.post(
    "/invite_kol", summary="邀请KOL", description="用户发送邀约请求给指定KOL"
)
async def 邀请KOL(请求数据: 邀请kol请求, 用户: dict = Depends(获取当前用户)):
    """
    用户发送邀约请求给指定KOL
    :param 请求数据: 包含 uid_number, account_douyin, 昵称 的请求数据
    :param 用户: 当前登录用户的认证信息
    :return: 成功或失败的响应
    """
    try:
        # 验证用户是否存在
        if not 用户 or "id" not in 用户:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "status": 状态.用户.令牌无效,
                    "message": "请先登录，登录后才能发送邀约。",
                    "data": None,
                },
            )

        用户id = 用户["id"]

        # 调用服务层函数处理邀约逻辑
        结果 = await 异步用户发送邀约请求(
            用户id=用户id,
            uid_number=请求数据.uid_number,
            account_douyin=请求数据.account_douyin,
            昵称=请求数据.昵称,
        )

        # 转换为统一格式
        if isinstance(结果, dict) and "status" in 结果:
            if "message" not in 结果 and "msg" in 结果:
                结果["message"] = 结果["msg"]
                del 结果["msg"]
            if "data" not in 结果:
                结果["data"] = None
            return 结果

        return {
            "status": 状态.通用.成功_旧,
            "message": "您的邀约请求已成功发送！",
            "data": 结果,
        }

    except HTTPException as he:
        # 确保HTTP异常的格式统一
        if hasattr(he, "detail") and isinstance(he.detail, dict):
            if "data" not in he.detail:
                he.detail["data"] = None
            if "msg" in he.detail and "message" not in he.detail:
                he.detail["message"] = he.detail["msg"]
                del he.detail["msg"]
        raise he

    except ValueError as ve:
        # 捕获值错误（例如，参数无效）
        错误日志器.warning(f"邀请KOL接口参数错误: {str(ve)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "status": 状态.邀约.邀约失败,
                "message": f"邀约请求参数似乎有些问题，请检查后重试。详情: {str(ve)}",
                "data": None,
            },
        )
    except Exception as e:
        # 捕获未知异常并记录日志
        错误日志器.error(f"邀请KOL接口发生未知错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": "发送邀约时遇到未知问题，请稍后重试。",
                "data": None,
            },
        )


@用户路由.post("/bind_shop", summary="关联店铺", description="用户关联店铺信息")
async def 关联店铺接口(请求数据: 登录店铺请求, 用户: dict = Depends(获取当前用户)):
    """
    用户关联店铺信息
    :param 请求数据: 包含店铺id、名称和头像的请求
    :param 用户: 通过依赖注入的认证用户数据
    :return: 关联结果
    """
    try:
        # 用户认证检查
        if not 用户 or "id" not in 用户:
            raise HTTPException(
                status_code=状态.用户.令牌无效,
                detail={
                    "status": 状态.用户.令牌无效,
                    "message": "请先登录，登录后才能关联店铺信息。",
                    "data": None,
                },
            )

        用户id = 用户["id"]

        # 参数有效性验证
        if not all([请求数据.shop_id, 请求数据.shop_name, 请求数据.avatar]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "status": 状态.用户.参数缺失,
                    "message": "店铺id、店铺名称和店铺头像都是必填项哦。",
                    "data": None,
                },
            )

        处理结果 = await 异步收到店铺信息(
            用户id, 请求数据.shop_id, 请求数据.shop_name, 请求数据.avatar
        )

        # 转换为统一格式
        if isinstance(处理结果, dict) and "status" in 处理结果:
            if "message" not in 处理结果 and "msg" in 处理结果:
                处理结果["message"] = 处理结果["msg"]
                del 处理结果["msg"]
            if "data" not in 处理结果:
                处理结果["data"] = None
            return 处理结果

        return {
            "status": 状态.通用.成功,
            "message": "店铺信息已成功关联到您的账户。",
            "data": 处理结果,
        }

    except HTTPException as he:
        # 确保HTTP异常的格式统一
        if hasattr(he, "detail") and isinstance(he.detail, dict):
            if "data" not in he.detail:
                he.detail["data"] = None
            if "msg" in he.detail and "message" not in he.detail:
                he.detail["message"] = he.detail["msg"]
                del he.detail["msg"]
        raise he  # 直接抛出 HTTPException
    except Exception as e:
        错误日志器.error(f"关联店铺接口失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": f"关联店铺时发生错误，请稍后重试。错误详情: {str(e)}",
                "data": None,
            },
        )


@用户路由.post("/submit_contact")
async def 提交联系方式接口(
    请求数据: 联系方式提交请求, 用户: dict = Depends(获取当前用户)
):
    """
    提交联系方式接口
    - 根据联系方式和类型判断是否存在
    - 存在返回现有ID，不存在则插入并返回新ID
    """
    try:
        # 参数验证
        if not 请求数据.联系方式:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "status": 状态.通用.参数错误,
                    "message": "请提供您的联系方式。",
                },
            )

        # 调用服务
        联系方式_id = await 异步提交联系方式(
            联系方式=请求数据.联系方式,
            类型=请求数据.类型,
            来源=请求数据.来源,
            来源用户=用户["id"],
        )

        return {
            "status": 状态.通用.成功_旧,
            "message": "您的联系方式已成功提交，感谢您的提供！",
            "data": {"联系方式ID": 联系方式_id},
        }

    except HTTPException as he:
        raise he
    except Exception as e:
        错误日志器.error(f"提交联系方式失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": "提交联系方式时遇到问题，请稍后重试。",
            },
        )


@用户路由.post(
    "/users/promoted_list",
    summary="获取推广用户列表",
    description="获取当前登录用户邀请的用户列表，支持分页和搜索",
)
async def 获取推广用户列表接口(
    请求数据: 推广用户列表请求, 当前用户: dict = Depends(获取当前用户)
):
    """
    获取当前登录用户邀请的用户列表。

    参数:
        请求数据 (推广用户列表请求): 包含分页和搜索关键词的请求体。
        当前用户 (dict): 通过依赖注入的认证用户数据。

    返回:
        包含用户列表和分页信息的响应。
    """
    try:
        接口日志器.info(f"用户 {当前用户.get('id')} 请求推广用户列表: {请求数据}")
        结果 = await 异步处理获取推广用户列表(
            当前用户id=当前用户.get("id"),
            页码=请求数据.页码,
            每页数量=请求数据.每页数量,
            查询条件=请求数据.搜索关键词,
        )
        return JSONResponse(content=结果)  # 直接返回服务层构造的完整响应

    except HTTPException as he:
        # 透传已知的HTTP异常
        raise he
    except Exception as e:
        错误日志器.error(f"获取推广用户列表接口失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": f"获取您的推广用户列表时遇到问题，请稍后重试。错误详情: {str(e)}",
                "data": None,
            },
        )


@用户路由.post(
    "/users/set_昵称",
    summary="设置或更新用户昵称",
    description="用户设置自己的昵称，首次设置会增加每日邀约次数。",
)
async def 设置用户昵称接口(
    请求数据: 设置昵称请求, 当前用户: dict = Depends(获取当前用户)
):
    """
    设置或更新用户昵称
    """
    try:
        # 参数验证
        if not 请求数据.昵称 or len(请求数据.昵称.strip()) == 0:
            return 统一响应模型.失败(
                状态码=状态.通用.参数错误, 消息="昵称不能为空", 数据=None
            ).转字典()

        # 昵称长度验证
        昵称 = 请求数据.昵称.strip()
        if len(昵称) < 2 or len(昵称) > 20:
            return 统一响应模型.失败(
                状态码=状态.通用.参数错误,
                消息="昵称长度必须在2到20个字符之间",
                数据=None,
            ).转字典()

        接口日志器.info(f"用户 {当前用户['id']} 请求设置昵称: {昵称}")

        # 调用异步用户服务设置昵称
        设置结果 = await 异步设置用户昵称服务(当前用户["id"], 昵称)

        # 检查服务层返回的结果
        if 设置结果.get("status") == 状态.通用.成功:
            接口日志器.info(f"用户 {当前用户['id']} 昵称设置成功")
            return 统一响应模型.成功(
                数据=设置结果.get("data"), 消息=设置结果.get("message", "昵称设置成功")
            ).转字典()
        else:
            # 处理业务逻辑错误
            错误状态码 = 设置结果.get("status", 状态.通用.失败)
            错误消息 = 设置结果.get("message", "设置昵称失败")
            接口日志器.warning(f"用户 {当前用户['id']} 昵称设置失败: {错误消息}")
            return 统一响应模型.失败(
                状态码=错误状态码, 消息=错误消息, 数据=None
            ).转字典()

    except Exception as e:
        错误日志器.error(f"设置用户昵称失败，用户id: {当前用户['id']}, 错误: {str(e)}")
        错误日志器.error(f"详细错误信息: {traceback.format_exc()}")
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误, 消息="服务器内部错误，请稍后重试", 数据=None
        ).转字典()


@用户路由.post(
    "/find-by-phone",
    summary="通过手机号查找用户",
    description="根据手机号查找用户，用于团队负责人等功能",
)
async def 通过手机号查找用户接口(请求数据: 手机号查找用户请求):
    """
    通过手机号查找用户
    """
    try:
        接口日志器.info(f"查找用户请求，手机号: {请求数据.手机号}")

        # 调用数据库函数获取用户信息
        用户数据 = await 异步获取用户_电话(请求数据.手机号)

        if 用户数据 is None:
            接口日志器.info(f"用户未找到，手机号: {请求数据.手机号}")
            return 统一响应模型.失败(状态码=404, 消息="该手机号用户未注册", 数据=None)

        # 获取用户昵称
        用户昵称 = await 异步获取用户昵称_id(用户数据["id"])

        # 返回用户基本信息（不包含敏感信息）
        用户信息 = {
            "用户id": 用户数据["id"],
            "手机号": 用户数据["手机号"],
            "昵称": 用户昵称 or 用户数据.get("昵称", ""),
            "状态": "正常",  # 这里可以根据实际情况调整
        }

        接口日志器.info(f"用户查找成功，用户id: {用户数据['id']}")
        return 统一响应模型.成功(数据=用户信息, 消息="用户查找成功")

    except Exception as e:
        错误日志器.error(
            f"通过手机号查找用户失败，手机号: {请求数据.手机号}, 错误: {str(e)}"
        )
        错误日志器.error(f"详细错误信息: {traceback.format_exc()}")
        return 统一响应模型.失败(状态码=500, 消息="查找用户失败，请稍后重试", 数据=None)


# 新增：智能搜索用户请求模型
class 智能搜索用户请求(BaseModel):
    搜索关键词: str
    模糊匹配: bool = True
    限制数量: int = 10
    用户类型: Optional[str] = None
    状态筛选: str = "active"


class 公司用户列表请求(BaseModel):
    公司id: int
    每页数量: int = 20
    页码: int = 1
    搜索关键词: Optional[str] = None


@用户路由.post(
    "/smart-search",
    summary="智能搜索用户",
    description="根据关键词智能搜索用户，支持手机号、昵称、邮箱等",
)
async def 智能搜索用户接口(请求数据: 智能搜索用户请求):
    """
    智能搜索用户接口
    支持按手机号、昵称、邮箱等进行模糊搜索
    """
    try:
        接口日志器.info(f"开始智能搜索用户，关键词: {请求数据.搜索关键词}")

        # 构建搜索条件
        关键词模式 = f"%{请求数据.搜索关键词}%"

        搜索SQL = """
        SELECT
            id,
            昵称,
            手机号,
            邮箱,
            created_at as 创建时间,
            level as 等级,
            experience_points as 经验值
        FROM 用户表 u
        WHERE (昵称 LIKE $1 OR 手机号 LIKE $2 OR 邮箱 LIKE $3)
        AND 手机号 IS NOT NULL
        ORDER BY
            CASE
                WHEN 手机号 = $4 THEN 1
                WHEN 昵称 = $5 THEN 2
                WHEN 邮箱 = $6 THEN 3
                ELSE 4
            END,
            created_at DESC
        LIMIT $7
        """

        参数 = [
            关键词模式,
            关键词模式,
            关键词模式,  # LIKE搜索
            请求数据.搜索关键词,
            请求数据.搜索关键词,
            请求数据.搜索关键词,  # 精确匹配排序
            请求数据.限制数量,
        ]

        搜索结果 = await 异步连接池实例.执行查询(搜索SQL, 参数)

        # 格式化结果
        用户列表 = []
        for 用户 in 搜索结果 or []:
            用户列表.append(
                {
                    "id": 用户["id"],
                    "昵称": 用户["昵称"],
                    "real_name": 用户["昵称"],  # 兼容字段
                    "phone": 用户["手机号"],
                    "email": 用户["邮箱"],
                    "avatar": None,  # 暂时没有头像字段
                    "level": 用户["等级"],
                    "experience_points": 用户["经验值"],
                    "created_at": 用户["创建时间"].isoformat()
                    if 用户["创建时间"]
                    else None,
                }
            )

        接口日志器.info(f"智能搜索用户完成，找到 {len(用户列表)} 个用户")

        return 统一响应模型.成功(
            data={
                "list": 用户列表,
                "total": len(用户列表),
                "keyword": 请求数据.搜索关键词,
            }
        )

    except Exception as e:
        错误日志器.error(f"智能搜索用户失败: {e}", exc_info=True)
        return 统一响应模型.失败(消息="搜索用户失败，请稍后重试")


@用户路由.post(
    "/company-users", summary="获取公司用户列表", description="获取指定公司的用户列表"
)
async def 获取公司用户列表接口(请求数据: 公司用户列表请求):
    """
    获取公司用户列表接口
    """
    try:
        接口日志器.info(f"获取公司 {请求数据.公司id} 的用户列表")

        偏移量 = (请求数据.页码 - 1) * 请求数据.每页数量

        # 构建查询条件
        where_conditions = ["c.id = $1"]
        params = [请求数据.公司id]

        if 请求数据.搜索关键词:
            where_conditions.append(
                "(u.昵称 LIKE $1 OR u.手机号 LIKE $2 OR u.邮箱 LIKE $3)"
            )
            关键词模式 = f"%{请求数据.搜索关键词}%"
            params.extend([关键词模式, 关键词模式, 关键词模式])

        where_clause = " AND ".join(where_conditions)

        # 查询用户列表（通过团队关联表找到公司用户）
        用户查询SQL = f"""
        SELECT DISTINCT
            u.id,
            u.昵称,
            u.手机号,
            u.邮箱,
            u.created_at as 创建时间,
            u.level as 等级
        FROM 用户表 u
        JOIN 用户团队关联表 ut ON u.id = ut.用户id
        JOIN 团队表 t ON ut.团队id = t.id
        JOIN 公司表 c ON t.公司id = c.id
        WHERE {where_clause}
        AND ut.状态 = '正常'
        ORDER BY u.created_at DESC
        LIMIT ${len(params) + 1} OFFSET ${len(params) + 2}
        """

        # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
        # {{ Source: PostgreSQL参数占位符文档 }}
        params.extend([请求数据.每页数量, 偏移量])

        # 查询总数
        总数查询SQL = f"""
        SELECT COUNT(DISTINCT u.id) as total
        FROM 用户表 u
        JOIN 用户团队关联表 ut ON u.id = ut.用户id
        JOIN 团队表 t ON ut.团队id = t.id
        JOIN 公司表 c ON t.公司id = c.id
        WHERE {where_clause}
        AND ut.状态 = '正常'
        """

        总数参数 = params[:-2]  # 去掉LIMIT和OFFSET参数

        用户结果 = await 异步连接池实例.执行查询(用户查询SQL, params)
        总数结果 = await 异步连接池实例.执行查询(总数查询SQL, 总数参数)

        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # 格式化结果
        用户列表 = []
        for 用户 in 用户结果 or []:
            用户列表.append(
                {
                    "id": 用户["id"],
                    "昵称": 用户["昵称"],
                    "real_name": 用户["昵称"],
                    "phone": 用户["手机号"],
                    "email": 用户["邮箱"],
                    "avatar": None,
                    "level": 用户["等级"],
                    "created_at": 用户["创建时间"].isoformat()
                    if 用户["创建时间"]
                    else None,
                }
            )

        接口日志器.info(f"获取公司用户列表完成，共 {总数} 个用户")

        return 统一响应模型.成功(
            数据={
                "list": 用户列表,
                "total": 总数,
                "page": 请求数据.页码,
                "size": 请求数据.每页数量,
            }
        )

    except Exception as e:
        错误日志器.error(f"获取公司用户列表失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=1001, 消息="获取公司用户列表失败，请稍后重试")


@用户路由.get(
    "/shops", summary="获取用户店铺列表", description="获取当前用户关联的店铺列表"
)
async def 获取用户店铺列表接口(当前用户: dict = Depends(获取当前用户)):
    """
    获取当前用户关联的店铺列表
    """
    try:
        用户id = 当前用户["id"]
        接口日志器.info(f"获取用户店铺列表: 用户id={用户id}")

        # 使用已导入的PostgreSQL连接池实例

        # 获取用户关联的店铺
        店铺查询 = """
        SELECT
            s.id as 店铺id,
            s.shop_id as 店铺标识,
            s.shop_name as 店铺名称,
            s.avatar as 店铺头像,
            s.创建时间,
            us.用户id as 关联用户id
        FROM 用户_店铺 us
        JOIN 店铺 s ON us.店铺id = s.id
        WHERE us.用户id = $1
        ORDER BY s.创建时间 DESC
        """

        店铺列表 = await 异步连接池实例.执行查询(店铺查询, (用户id,))

        # 格式化店铺数据
        格式化列表 = []
        for 店铺 in 店铺列表:
            格式化列表.append(
                {
                    "店铺id": 店铺["店铺id"],
                    "店铺标识": 店铺["店铺标识"] or "",
                    "店铺名称": 店铺["店铺名称"] or "",
                    "店铺头像": 店铺["店铺头像"] or "",
                    "创建时间": 店铺["创建时间"].strftime("%Y-%m-%d %H:%M:%S")
                    if 店铺["创建时间"]
                    else "",
                    "关联状态": "已关联",
                }
            )

        接口日志器.info(
            f"用户店铺列表获取成功: 用户id={用户id}, 店铺数量={len(格式化列表)}"
        )
        return 统一响应模型.成功(数据=格式化列表, 消息="获取店铺列表成功")

    except Exception as e:
        错误日志器.error(f"获取用户店铺列表失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取店铺列表失败，请稍后重试")


@用户路由.get(
    "/teams", summary="获取用户团队列表", description="获取当前用户参与的团队列表"
)
async def 获取用户团队列表接口(当前用户: dict = Depends(获取当前用户)):
    """
    获取当前用户参与的团队列表
    """
    try:
        用户id = 当前用户["id"]
        接口日志器.info(f"获取用户团队列表: 用户id={用户id}")

        # 使用已导入的PostgreSQL连接池实例

        # 获取用户参与的团队
        团队查询 = """
        SELECT
            t.id as 团队id,
            t.团队名称,
            t.团队代码,
            t.团队描述,
            t.团队状态,
            t.当前成员数,
            t.最大成员数,
            t.创建时间,
            utr.职位 as 角色,
            utr.加入时间,
            c.公司名称
        FROM 用户团队关联表 utr
        JOIN 团队表 t ON utr.团队id = t.id
        LEFT JOIN 公司表 c ON t.公司id = c.id
        WHERE utr.用户id = $1 AND t.团队状态 != '解散' AND utr.状态 = '正常'
        ORDER BY utr.加入时间 DESC
        """

        团队列表 = await 异步连接池实例.执行查询(团队查询, (用户id,))

        # 格式化团队数据
        格式化列表 = []
        for 团队 in 团队列表:
            格式化列表.append(
                {
                    "团队id": 团队["团队id"],
                    "团队名称": 团队["团队名称"] or "",
                    "团队代码": 团队["团队代码"] or "",
                    "团队描述": 团队["团队描述"] or "",
                    "团队状态": 团队["团队状态"] or "正常",
                    "当前成员数": 团队["当前成员数"] or 0,
                    "最大成员数": 团队["最大成员数"] or 100,
                    "我的角色": 团队["角色"] or "成员",
                    "加入时间": 团队["加入时间"].strftime("%Y-%m-%d %H:%M:%S")
                    if 团队["加入时间"]
                    else "",
                    "创建时间": 团队["创建时间"].strftime("%Y-%m-%d %H:%M:%S")
                    if 团队["创建时间"]
                    else "",
                    "公司名称": 团队["公司名称"] or "未关联公司",
                }
            )

        接口日志器.info(
            f"用户团队列表获取成功: 用户id={用户id}, 团队数量={len(格式化列表)}"
        )
        return 统一响应模型.成功(数据=格式化列表, 消息="获取团队列表成功")

    except Exception as e:
        错误日志器.error(f"获取用户团队列表失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取团队列表失败，请稍后重试")


@用户路由.get(
    "/full-info",
    summary="获取用户完整信息",
    description="获取当前用户的完整信息，包括算力值、邀约次数等",
)
async def 获取用户完整信息接口(当前用户: dict = Depends(获取当前用户)):
    """
    获取当前用户的完整信息，包括基本信息、算力值、邀约次数等
    """
    try:
        用户id = 当前用户["id"]
        接口日志器.info(f"获取用户完整信息: 用户id={用户id}")

        from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

        # 获取用户完整信息（包括算力值、邀约次数、会员信息和自定义邀请码）
        用户查询 = """
        SELECT
            u.id,
            u.phone,
            u.email,
            u.昵称,
            u.created_at,
            u.level,
            u.experience_points,
            u.算力值,
            u.每日邀约次数,
            u.代理类型表id,
            u.用户自定义邀请码,
            (SELECT MAX(登陆时间) FROM 用户登陆记录表 WHERE 用户id = u.id) as last_login_time,
            (SELECT m.名称
             FROM 用户_会员_关联表 um
             LEFT JOIN 会员表 m ON um.会员id = m.id
             WHERE um.用户id = u.id
             AND um.到期时间 > CURRENT_TIMESTAMP
             ORDER BY m.id DESC
             LIMIT 1) as 会员名称
        FROM 用户表 u
        WHERE u.id = $1
        """

        用户结果 = await 异步连接池实例.执行查询(用户查询, (用户id,))

        if not 用户结果:
            return 统一响应模型.失败(状态码=404, 消息="用户不存在")

        用户信息 = 用户结果[0]

        # 获取用户昵称（如果基本信息中没有）
        用户昵称 = await 异步获取用户昵称_id(用户id)

        # 从用户表中获取算力值和邀约次数
        算力值 = 用户信息.get("算力值", 0) or 0
        每日邀约次数 = 用户信息.get("每日邀约次数", 30) or 30

        # 计算已使用邀约次数（从邀约记录表查询今日使用次数）
        今日邀约查询 = """
        SELECT COUNT(*) as 已使用次数
        FROM 用户抖音达人邀约记录表
        WHERE 用户id = $1 AND DATE(邀约发起时间) = CURRENT_DATE AND 当日计次 = 1
        """

        今日邀约结果 = await 异步连接池实例.执行查询(今日邀约查询, (用户id,))
        已使用邀约次数 = 今日邀约结果[0]["已使用次数"] if 今日邀约结果 else 0

        # 构建完整用户信息 - 纯中文字段
        完整信息 = {
            "用户id": 用户信息["id"],
            "手机号": 用户信息["phone"],
            "邮箱": 用户信息["email"] or "",
            "昵称": 用户昵称 or 用户信息.get("昵称", ""),
            "头像": "",  # 暂时没有头像字段
            "角色名称": 用户信息.get("会员名称") or "普通用户",  # 从会员表获取角色名称
            "状态": 1,  # 默认正常状态
            "等级": 用户信息.get("level", 1) or 1,
            "经验值": 用户信息.get("experience_points", 0) or 0,
            "创建时间": 用户信息["created_at"].strftime("%Y-%m-%d %H:%M:%S")
            if 用户信息["created_at"]
            else "",
            "上次登录时间": 用户信息["last_login_time"].strftime("%Y-%m-%d %H:%M:%S")
            if 用户信息["last_login_time"]
            else "",
            "算力值": 算力值,
            "每日邀约次数": 每日邀约次数,
            "已使用邀约次数": 已使用邀约次数,
            "剩余邀约次数": max(0, 每日邀约次数 - 已使用邀约次数),
            "代理类型表id": 用户信息.get("代理类型表id"),  # 新增：代理类型表id字段
            "用户自定义邀请码": 用户信息.get("用户自定义邀请码")
            or "",  # 新增：用户自定义邀请码字段
        }

        接口日志器.info(f"用户完整信息获取成功: 用户id={用户id}")
        return 统一响应模型.成功(数据=完整信息, 消息="获取用户完整信息成功")

    except Exception as e:
        错误日志器.error(f"获取用户完整信息失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户完整信息失败，请稍后重试")


# ==================== 样品自动审核设置接口 ====================


class 自动审核设置请求(BaseModel):
    """自动审核设置请求模型"""

    是否自动审核: int = Field(..., description="是否启用自动审核：0=关闭，1=开启")


@用户路由.post(
    "/auto-audit-settings",
    summary="设置样品自动审核",
    description="用户设置样品申请自动审核功能的开启/关闭状态",
)
async def 设置样品自动审核(
    请求数据: 自动审核设置请求, 当前用户: dict = Depends(获取当前用户)
):
    """
    设置样品自动审核功能

    功能：
        用户可以开启或关闭样品申请的自动审核功能
        开启后，用户申请自己产品的样品时将自动通过审核

    参数:
        是否自动审核: 0=关闭自动审核，1=开启自动审核

    返回:
        操作结果的统一响应
    """
    try:
        # 获取当前用户id
        用户id = 当前用户["id"]

        # 参数验证
        if 请求数据.是否自动审核 not in [0, 1]:
            return 统一响应模型.失败(400, "无效的设置值，有效值为：0(关闭)、1(开启)")

        # 更新用户表中的自动审核设置
        # 使用已导入的PostgreSQL连接池实例

        更新查询 = """
        UPDATE 用户表
        SET 是否自动审核 = $1
        WHERE id = $2
        """

        影响行数 = await 异步连接池实例.执行更新(
            更新查询, (请求数据.是否自动审核, 用户id)
        )

        if 影响行数 == 0:
            return 统一响应模型.失败(404, "用户不存在或更新失败")

        # 记录操作日志
        设置状态文本 = "开启" if 请求数据.是否自动审核 == 1 else "关闭"
        接口日志器.info(
            f"用户样品自动审核设置更新: 用户id={用户id}, 设置={设置状态文本}"
        )

        # 构建返回数据
        返回数据 = {
            "用户id": 用户id,
            "是否自动审核": 请求数据.是否自动审核,
            "设置状态": 设置状态文本,
            "更新时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }

        # 返回成功响应
        return 统一响应模型.成功(返回数据, f"样品自动审核功能已{设置状态文本}")

    except Exception as e:
        # 记录错误日志
        错误详情 = traceback.format_exc()
        错误日志器.error(f"设置样品自动审核失败: {str(e)}")
        错误日志器.error(f"详细错误信息: {错误详情}")

        # 返回失败响应
        return 统一响应模型.失败(500, f"设置样品自动审核失败: {str(e)}")


@用户路由.post(
    "/get-auto-audit-settings",
    summary="获取样品自动审核设置",
    description="获取用户当前的样品自动审核设置状态",
)
async def 获取样品自动审核设置(当前用户: dict = Depends(获取当前用户)):
    """
    获取样品自动审核设置

    功能：
        获取用户当前的样品自动审核功能设置状态

    返回:
        用户的自动审核设置信息
    """
    try:
        # 获取当前用户id
        用户id = 当前用户["id"]

        # 查询用户的自动审核设置
        # 使用已导入的PostgreSQL连接池实例

        查询SQL = """
        SELECT 是否自动审核
        FROM 用户表
        WHERE id = $1
        """

        查询结果 = await 异步连接池实例.执行查询(查询SQL, (用户id,))

        if not 查询结果:
            return 统一响应模型.失败(404, "用户不存在")

        是否自动审核 = 查询结果[0].get("是否自动审核", 0)
        设置状态文本 = "开启" if 是否自动审核 == 1 else "关闭"

        # 构建返回数据
        返回数据 = {
            "用户id": 用户id,
            "是否自动审核": 是否自动审核,
            "设置状态": 设置状态文本,
            "功能说明": "开启后，申请自己产品的样品时将自动通过审核",
        }

        接口日志器.info(
            f"获取用户样品自动审核设置: 用户id={用户id}, 当前设置={设置状态文本}"
        )

        # 返回成功响应
        return 统一响应模型.成功(返回数据, "获取自动审核设置成功")

    except Exception as e:
        # 记录错误日志
        错误详情 = traceback.format_exc()
        错误日志器.error(f"获取样品自动审核设置失败: {str(e)}")
        错误日志器.error(f"详细错误信息: {错误详情}")

        # 返回失败响应
        return 统一响应模型.失败(500, f"获取样品自动审核设置失败: {str(e)}")


# 注意：确保APIRouter实例在此文件末尾被其他文件（如main.py）导入和使用
