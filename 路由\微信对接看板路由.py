import datetime
from typing import Any, List, Optional

from fastapi import APIRouter, Body, Depends
from pydantic import BaseModel

import 状态
from 依赖项.认证 import 获取当前用户

# 导入数据层进行数据库操作
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 数据模型.响应模型 import 统一响应模型
from 日志 import 错误日志器

# 创建一个新的路由实例，专门用于看板功能
微信对接看板路由 = APIRouter()


class 看板数据请求(BaseModel):
    微信id: int = Body(..., description="我方微信账号id")
    产品名称: Optional[str] = Body(None, description="按产品名称筛选")
    对方微信号: Optional[str] = Body(None, description="按对方微信号筛选")


class 看板统计请求(BaseModel):
    微信id: int = Body(..., description="我方微信账号id")
    统计天数: int = Body(30, description="统计最近多少天的数据趋势")


class 列表数据请求(BaseModel):
    微信id: int = Body(..., description="我方微信账号id")
    页码: int = Body(1, description="页码，从1开始")
    每页条数: int = Body(10, description="每页显示的数据条数")
    产品名称: Optional[str] = Body(None, description="按产品名称筛选")
    对方微信号: Optional[str] = Body(None, description="按对方微信号筛选")
    意向状态: Optional[int] = Body(None, description="按意向状态筛选")
    样品状态: Optional[int] = Body(None, description="按样品状态筛选")
    排期状态: Optional[int] = Body(None, description="按排期状态筛选")
    排序字段: str = Body("更新时间", description="排序字段名")
    排序方式: str = Body("desc", description="排序方式：asc升序，desc降序")


def 分配泳道(进度):
    """根据进度状态，将其分配到对应的泳道ID"""
    意向状态 = 进度.get("意向状态", 0)
    样品状态 = 进度.get("样品状态", 0)
    排期状态 = 进度.get("排期状态", 0)

    if 意向状态 == -1:
        return "lane-5"  # 合作失败
    if 排期状态 in [1, 2]:
        return "lane-4"  # 排期合作
    if 样品状态 in [1, -2, 2, 3, -3, 4, -4, 5, -5]:
        return "lane-3"  # 样品流程
    if 意向状态 == 1:
        return "lane-2"  # 意向确认
    return "lane-1"  # 初步接触


@微信对接看板路由.post(
    "/board-data",
    summary="获取对接看板数据",
    description="根据我方微信id，获取按状态分组的产品对接看板数据",
)
async def 获取看板数据(请求: 看板数据请求, 用户: dict = Depends(获取当前用户)):
    """
    获取产品对接看板所需的数据。
    数据将按对接流程的各个阶段（泳道）进行分组。
    """
    try:
        # 记录请求参数，方便调试
        错误日志器.info(
            f"获取看板数据 - 请求参数: 微信id={请求.微信id}, 用户id={用户['id']}"
        )

        # 第一步：查询微信产品对接进度表的基础数据
        基础查询语句 = """
            SELECT
                p.id AS 进度ID,
                p.合作产品id,
                p.用户联系人表id,
                p.我方微信号id,
                p.回复状态,
                p.意向状态,
                p.样品状态,
                p.排期状态,
                p.开播状态,
                p.销售额,
                p.排期开始时间,
                p.排期结束时间,
                p.创建时间,
                p.更新时间
            FROM 微信产品对接进度表 p
            WHERE p.我方微信号id = $1
        """
        参数: List[Any] = [请求.微信id]  # 支持混合类型的参数列表

        # 添加状态筛选条件
        基础查询语句, 参数 = _构建状态筛选条件(基础查询语句, 参数, 请求)

        基础查询语句 += " ORDER BY p.更新时间 DESC"

        # 第一步：执行基础查询
        try:
            基础进度数据 = await 异步连接池实例.执行查询(基础查询语句, tuple(参数))
        except Exception as sql_err:
            错误日志器.error(f"基础查询SQL执行错误: {sql_err}", exc_info=True)
            return 统一响应模型.失败(
                状态.通用.服务器错误, f"数据库查询失败: {str(sql_err)}"
            )

        if not 基础进度数据:
            错误日志器.info("获取看板数据 - 无基础数据")
            所有进度 = []
        else:
            # 第二步：批量查询关联数据
            所有进度 = await _批量查询关联数据(基础进度数据, 请求)

        # 记录查询结果数量
        错误日志器.info(f"获取看板数据 - 最终结果数量: {len(所有进度)}")

        # 初始化泳道数据
        lanes = {
            "lane-1": {"id": "lane-1", "title": "初步接触", "cards": []},
            "lane-2": {"id": "lane-2", "title": "意向确认", "cards": []},
            "lane-3": {"id": "lane-3", "title": "样品流程", "cards": []},
            "lane-4": {"id": "lane-4", "title": "排期合作", "cards": []},
            "lane-5": {"id": "lane-5", "title": "合作失败", "cards": []},
        }

        # 遍历进度数据并添加到对应泳道
        for 进度 in 所有进度:
            try:
                # 使用微信号作为好友标识
                好友标识 = (
                    进度.get("对方微信号") or f"未知好友(ID:{进度.get('对方微信号id')})"
                )

                # 确保进度ID存在
                进度ID = 进度.get("进度ID")
                if not 进度ID:
                    错误日志器.warning(f"发现无ID的进度数据: {进度}")
                    continue

                # 分配泳道
                lane_id = 分配泳道(进度)

                # 构建卡片数据
                card = {
                    "id": f"card-{进度ID}",
                    "title": 进度.get("产品名称", "未知产品"),
                    "owner": 好友标识,
                    "lastUpdate": 进度["更新时间"].strftime("%Y-%m-%d")
                    if 进度.get("更新时间")
                    else "N/A",
                    "details": 进度,  # 保留所有细节以便弹窗显示
                }

                # 添加到对应泳道
                if lane_id in lanes:
                    lanes[lane_id]["cards"].append(card)
                else:
                    错误日志器.warning(f"未知泳道ID: {lane_id}, 进度数据: {进度}")
            except Exception as card_err:
                错误日志器.error(
                    f"处理进度卡片数据时发生错误: {card_err}, 进度数据: {进度}",
                    exc_info=True,
                )
                # 继续处理其他卡片，不中断整个流程

        # 返回成功响应
        return 统一响应模型.成功(list(lanes.values()))

    except Exception as e:
        错误日志器.error(f"获取看板数据时发生错误: {e}", exc_info=True)
        return 统一响应模型.失败(
            状态.微信.获取对接进度列表失败, f"获取看板数据失败: {str(e)}"
        )


@微信对接看板路由.post(
    "/board-list",
    summary="获取产品对接进度列表",
    description="分页获取产品对接进度列表，支持筛选和排序",
)
async def 获取列表数据(请求: 列表数据请求, 用户: dict = Depends(获取当前用户)):
    """
    获取产品对接进度列表，支持分页、筛选和排序功能。
    返回带有分页信息的列表数据。
    """
    try:
        错误日志器.info(f"获取列表数据 - 请求参数: {请求}")

        # 使用分步查询策略 - 第一步：基础查询
        基础查询语句 = """
            SELECT
                p.id AS 进度ID,
                p.合作产品id,
                p.用户联系人表id,
                p.我方微信号id,
                p.回复状态,
                p.意向状态,
                p.样品状态,
                p.排期状态,
                p.开播状态,
                p.销售额,
                p.排期开始时间,
                p.排期结束时间,
                p.创建时间,
                p.更新时间
            FROM 微信产品对接进度表 p
            WHERE p.我方微信号id = $1
        """
        参数 = [请求.微信id]  # 支持混合类型的参数列表

        # 添加状态筛选条件
        基础查询语句, 参数 = _构建状态筛选条件(基础查询语句, 参数, 请求)

        # 添加排序和分页到基础查询
        基础查询语句 += f" ORDER BY p.{请求.排序字段} {请求.排序方式.upper()}"
        # 计算正确的参数索引
        下一个参数索引 = len(参数) + 1
        基础查询语句 += f" LIMIT ${下一个参数索引} OFFSET ${下一个参数索引 + 1}"
        偏移量 = (请求.页码 - 1) * 请求.每页条数
        参数.append(请求.每页条数)
        参数.append(偏移量)

        # 获取总记录数的查询（不包含LIMIT和OFFSET）
        计数查询语句 = 基础查询语句.split(" ORDER BY")[0]  # 移除排序和分页
        计数查询 = f"SELECT COUNT(*) as 总数 FROM ({计数查询语句}) AS 子查询"

        try:
            # 执行计数查询
            计数结果 = await 异步连接池实例.执行查询(
                计数查询, tuple(参数[:-2])
            )  # 不包括LIMIT和OFFSET参数
            总数 = 计数结果[0]["总数"] if 计数结果 else 0

            # 第一步：执行基础分页查询
            基础列表结果 = await 异步连接池实例.执行查询(基础查询语句, tuple(参数))

            if not 基础列表结果:
                列表结果 = []
            else:
                # 第二步：批量查询关联数据
                列表结果 = await _批量查询关联数据(基础列表结果, 请求)

            错误日志器.info(
                f"获取列表数据 - 查询结果: 总数={总数}, 当前页记录数={len(列表结果)}"
            )

            # 格式化分页数据
            分页数据 = {
                "列表": 列表结果,
                "总数": 总数,
                "页码": 请求.页码,
                "每页条数": 请求.每页条数,
                "总页数": (总数 + 请求.每页条数 - 1) // 请求.每页条数,  # 向上取整
            }

            return 统一响应模型.成功(分页数据)

        except Exception as sql_err:
            错误日志器.error(f"SQL执行错误: {sql_err}", exc_info=True)
            return 统一响应模型.失败(
                状态.通用.服务器错误, f"数据库查询失败: {str(sql_err)}"
            )

    except Exception as e:
        错误日志器.error(f"获取列表数据时发生错误: {e}", exc_info=True)
        return 统一响应模型.失败(
            状态.微信.获取对接进度列表失败, f"获取对接进度列表失败: {str(e)}"
        )


@微信对接看板路由.post(
    "/board-stats",
    summary="获取看板统计数据",
    description="获取产品对接各状态的统计数据和趋势",
)
async def 获取看板统计数据(请求: 看板统计请求, 用户: dict = Depends(获取当前用户)):
    """
    获取产品对接看板的统计数据，包括：
    1. 各阶段状态的数量统计
    2. 各产品分类的分布
    3. 近期对接数据的趋势
    """
    try:
        错误日志器.info(
            f"获取看板统计数据 - 请求参数: 微信id={请求.微信id}, 用户id={用户['id']}"
        )

        # 1. 查询各状态数量统计
        状态统计查询 = """
            SELECT 
                '意向状态' AS 状态类型,
                意向状态 AS 状态值,
                COUNT(*) AS 数量
            FROM 
                微信产品对接进度表
            WHERE 
                我方微信号id = $1
                AND 用户id = $1
            GROUP BY 
                意向状态
            UNION ALL
            SELECT 
                '样品状态' AS 状态类型,
                样品状态 AS 状态值,
                COUNT(*) AS 数量
            FROM 
                微信产品对接进度表
            WHERE 
                我方微信号id = $1
                AND 用户id = $1
                AND 样品状态 IS NOT NULL
            GROUP BY 
                样品状态
            UNION ALL
            SELECT 
                '排期状态' AS 状态类型,
                排期状态 AS 状态值,
                COUNT(*) AS 数量
            FROM 
                微信产品对接进度表
            WHERE 
                我方微信号id = $1
                AND 用户id = $1
                AND 排期状态 IS NOT NULL
            GROUP BY 
                排期状态
            UNION ALL
            SELECT 
                '开播状态' AS 状态类型,
                开播状态 AS 状态值,
                COUNT(*) AS 数量
            FROM 
                微信产品对接进度表
            WHERE 
                我方微信号id = $1
                AND 用户id = $1
                AND 开播状态 IS NOT NULL
            GROUP BY 
                开播状态
        """
        状态统计参数 = [请求.微信id, 用户["id"]] * 4  # 每个子查询都需要这两个参数

        # 2. 查询各阶段完成情况
        阶段统计查询 = """
            SELECT
                COUNT(*) AS 总进度数量,
                SUM(CASE WHEN 意向状态 = 1 THEN 1 ELSE 0 END) AS 意向确认数量,
                SUM(CASE WHEN 样品状态 = 1 THEN 1 ELSE 0 END) AS 样品确认数量,
                SUM(CASE WHEN 排期状态 = 1 THEN 1 ELSE 0 END) AS 排期确认数量,
                SUM(CASE WHEN 开播状态 = 1 THEN 1 ELSE 0 END) AS 开播完成数量,
                SUM(CASE WHEN 更新时间 >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) AS 近7天更新数量,
                SUM(CASE WHEN 创建时间 >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) AS 近30天新增数量
            FROM 
                微信产品对接进度表
            WHERE 
                我方微信号id = $1
                AND 用户id = $1
        """
        阶段统计参数 = [请求.微信id, 用户["id"]]

        # 3. 查询产品分类分布
        分类统计查询 = """
            SELECT 
                JSON_UNQUOTE(JSON_EXTRACT(prod.产品信息, '$."产品分类"[0]')) AS 产品分类,
                COUNT(*) AS 进度数量,
                SUM(CASE WHEN p.意向状态 = 1 THEN 1 ELSE 0 END) AS 意向确认数量,
                SUM(CASE WHEN p.样品状态 = 1 THEN 1 ELSE 0 END) AS 样品确认数量
            FROM 
                微信产品对接进度表 p
            JOIN 
                用户产品表 prod ON p.合作产品id = prod.id
            WHERE 
                p.我方微信号id = $1
                AND p.用户id = $1
                AND JSON_EXTRACT(prod.产品信息, '$."产品分类"[0]') IS NOT NULL
            GROUP BY 
                JSON_UNQUOTE(JSON_EXTRACT(prod.产品信息, '$."产品分类"[0]'))
            ORDER BY 
                进度数量 DESC
        """
        分类统计参数 = [请求.微信id, 用户["id"]]

        # 4. 查询近期趋势数据
        今天 = datetime.datetime.now().date()
        开始日期 = 今天 - datetime.timedelta(days=请求.统计天数)

        趋势查询 = """
            SELECT
                DATE(创建时间) AS 日期,
                COUNT(*) AS 新增对接数量
            FROM
                微信产品对接进度表
            WHERE
                我方微信号id = $1
                AND 用户id = $2
                AND 创建时间 >= $3
            GROUP BY
                DATE(创建时间)
            ORDER BY
                日期
        """
        趋势参数 = [请求.微信id, 用户["id"], 开始日期]

        # 执行所有查询
        try:
            状态统计结果 = await 异步连接池实例.执行查询(
                状态统计查询, tuple(状态统计参数)
            )
            阶段统计结果 = await 异步连接池实例.执行查询(
                阶段统计查询, tuple(阶段统计参数)
            )
            分类统计结果 = await 异步连接池实例.执行查询(
                分类统计查询, tuple(分类统计参数)
            )
            趋势结果 = await 异步连接池实例.执行查询(趋势查询, tuple(趋势参数))
        except Exception as sql_err:
            错误日志器.error(f"统计数据SQL执行错误: {sql_err}", exc_info=True)
            return 统一响应模型.失败(
                状态.微信.获取统计失败, f"数据库查询失败: {str(sql_err)}"
            )

        # 处理状态统计结果
        状态统计 = {"意向状态": {}, "样品状态": {}, "排期状态": {}, "开播状态": {}}

        for 统计 in 状态统计结果:
            状态类型 = 统计["状态类型"]
            状态值 = 统计["状态值"]
            数量 = 统计["数量"]

            if 状态类型 not in 状态统计:
                状态统计[状态类型] = {}

            状态统计[状态类型][str(状态值)] = 数量

        # 处理阶段统计结果
        阶段统计 = (
            阶段统计结果[0]
            if 阶段统计结果
            else {
                "总进度数量": 0,
                "意向确认数量": 0,
                "样品确认数量": 0,
                "排期确认数量": 0,
                "开播完成数量": 0,
                "近7天更新数量": 0,
                "近30天新增数量": 0,
            }
        )

        # 处理分类统计结果
        分类统计 = []
        for 分类 in 分类统计结果:
            分类统计.append(
                {
                    "产品分类": 分类["产品分类"],
                    "进度数量": 分类["进度数量"],
                    "意向确认数量": 分类["意向确认数量"],
                    "样品确认数量": 分类["样品确认数量"],
                }
            )

        # 处理趋势数据，填充没有数据的日期
        趋势数据 = {}
        for 数据 in 趋势结果:
            日期字符串 = 数据["日期"].strftime("%Y-%m-%d")
            趋势数据[日期字符串] = 数据["新增对接数量"]

        # 填充所有日期
        完整趋势 = []
        当前日期 = 开始日期
        while 当前日期 <= 今天:
            日期字符串 = 当前日期.strftime("%Y-%m-%d")
            完整趋势.append(
                {"日期": 日期字符串, "新增对接数量": 趋势数据.get(日期字符串, 0)}
            )
            当前日期 += datetime.timedelta(days=1)

        # 组装结果
        统计结果 = {
            "状态统计": 状态统计,
            "阶段统计": 阶段统计,
            "分类统计": 分类统计,
            "趋势数据": 完整趋势,
        }

        return 统一响应模型.成功(统计结果)

    except Exception as e:
        错误日志器.error(f"获取看板统计数据时发生错误: {e}", exc_info=True)
        return 统一响应模型.失败(状态.通用.服务器错误, f"服务器内部错误: {str(e)}")


def _构建状态筛选条件(基础查询语句: str, 参数: list, 请求) -> tuple:
    """构建状态筛选条件的通用函数"""
    状态字段映射 = {
        "回复状态": getattr(请求, "回复状态", None),
        "意向状态": getattr(请求, "意向状态", None),
        "样品状态": getattr(请求, "样品状态", None),
        "排期状态": getattr(请求, "排期状态", None),
        "开播状态": getattr(请求, "开播状态", None),
    }

    for 字段名, 字段值 in 状态字段映射.items():
        if 字段值 is not None:
            基础查询语句 += f" AND p.{字段名} = ${len(参数) + 1}"
            参数.append(字段值)

    return 基础查询语句, 参数


async def _批量查询关联数据(基础进度数据: List[dict], 请求) -> List[dict]:
    """
    批量查询关联数据，避免复杂JOIN

    Args:
        基础进度数据: 基础的进度数据列表
        请求: 请求对象，包含筛选条件

    Returns:
        完整的进度数据列表
    """
    try:
        # 提取需要查询的ID列表
        用户联系人表id列表 = [
            row["用户联系人表id"] for row in 基础进度数据 if row["用户联系人表id"]
        ]
        合作产品id列表 = list(
            set([row["合作产品id"] for row in 基础进度数据 if row["合作产品id"]])
        )
        我方微信号id列表 = list(
            set([row["我方微信号id"] for row in 基础进度数据 if row["我方微信号id"]])
        )

        # 批量查询产品信息
        产品信息映射 = {}
        if 合作产品id列表:
            产品查询 = """
                SELECT id, 产品名称 FROM 用户产品表
                WHERE id = ANY($1)
            """
            产品结果 = await 异步连接池实例.执行查询(产品查询, (合作产品id列表,))
            产品信息映射 = {row["id"]: row for row in 产品结果}

        # 批量查询我方微信信息
        我方微信信息映射 = {}
        if 我方微信号id列表:
            我方微信查询 = """
                SELECT id, 微信号 FROM 微信信息表
                WHERE id = ANY($1)
            """
            我方微信结果 = await 异步连接池实例.执行查询(
                我方微信查询, (我方微信号id列表,)
            )
            我方微信信息映射 = {row["id"]: row for row in 我方微信结果}

        # 批量查询联系人和微信信息
        联系人微信信息映射 = {}
        if 用户联系人表id列表:
            联系人微信查询 = """
                SELECT
                    uc.用户联系人id,
                    uc.姓名 AS 联系人姓名,
                    usi.微信信息表id,
                    wi.微信号 AS 对方微信号,
                    wi.昵称 AS 对方微信昵称
                FROM 用户联系人表 uc
                LEFT JOIN 用户达人补充信息表 usi ON uc.用户联系人id = usi.用户联系人表id
                LEFT JOIN 微信信息表 wi ON usi.微信信息表id = wi.id
                WHERE uc.用户联系人id = ANY($1)
            """
            联系人微信结果 = await 异步连接池实例.执行查询(
                联系人微信查询, (用户联系人表id列表,)
            )
            联系人微信信息映射 = {row["用户联系人id"]: row for row in 联系人微信结果}

        # 在应用层组装数据并应用筛选条件
        最终结果 = []
        for row in 基础进度数据:
            # 获取关联信息
            产品信息 = 产品信息映射.get(row["合作产品id"], {})
            我方微信信息 = 我方微信信息映射.get(row["我方微信号id"], {})
            联系人微信信息 = 联系人微信信息映射.get(row["用户联系人表id"], {})

            # 应用需要JOIN才能筛选的条件
            if 请求.对方微信号:
                对方微信号 = 联系人微信信息.get("对方微信号", "")
                if 请求.对方微信号 not in 对方微信号:
                    continue

            if 请求.产品名称:
                产品名称 = 产品信息.get("产品名称", "")
                if 请求.产品名称 not in 产品名称:
                    continue

            # 组装最终数据
            组装数据 = {
                "进度ID": row["进度ID"],
                "合作产品id": row["合作产品id"],
                "产品名称": 产品信息.get("产品名称", ""),
                "用户联系人表id": row["用户联系人表id"],
                "我方微信号id": row["我方微信号id"],
                "我方微信号": 我方微信信息.get("微信号", ""),
                "对方微信号id": 联系人微信信息.get("微信信息表id"),
                "对方微信号": 联系人微信信息.get("对方微信号", ""),
                "对方微信昵称": 联系人微信信息.get("对方微信昵称", ""),
                "联系人姓名": 联系人微信信息.get("联系人姓名", ""),
                "回复状态": row["回复状态"],
                "意向状态": row["意向状态"],
                "样品状态": row["样品状态"],
                "排期状态": row["排期状态"],
                "开播状态": row["开播状态"],
                "销售额": row["销售额"],
                "排期开始时间": row["排期开始时间"],
                "排期结束时间": row["排期结束时间"],
                "创建时间": row["创建时间"],
                "更新时间": row["更新时间"],
            }
            最终结果.append(组装数据)

        return 最终结果

    except Exception as e:
        错误日志器.error(f"批量查询关联数据失败: {e}", exc_info=True)
        return []
