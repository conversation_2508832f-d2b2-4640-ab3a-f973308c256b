"""
团队数据看板查询模块

此模块专门用于处理团队数据看板相关的复杂数据查询，提供可复用的查询函数，
优化数据库访问效率，避免在业务逻辑中直接写大量SQL语句。

创建时间: 2024
更新时间: 2024
作者: AI系统优化
"""

from typing import Any, Dict, List, Optional

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器


class 团队数据查询工具:
    """
    团队数据查询工具类 - 优化版本
    移除无效数据项和冗余查询，提供高效的可复用查询函数
    """

    def __init__(self):
        self.连接池 = 异步连接池实例

    async def 获取团队基本信息和公司信息(self, 团队id: int) -> Optional[Dict[str, Any]]:
        """
        获取团队基本信息和关联的公司信息

        Args:
            团队id: 团队标识

        Returns:
            包含团队和公司信息的字典，如果团队不存在则返回None
        """
        try:
            SQL = """
            SELECT 
                t.id as 团队id,
                t.团队名称, 
                t.最大成员数, 
                t.创建时间, 
                t.团队状态,
                t.创建人id,
                t.团队负责人id,
                c.公司名称,
                c.id as 公司id
            FROM 团队表 t
            LEFT JOIN 公司表 c ON t.公司id = c.id
            WHERE t.id = $1 AND t.团队状态 != '解散'
            """

            结果 = await self.连接池.执行查询(SQL, (团队id,))
            return 结果[0] if 结果 else None

        except Exception as e:
            错误日志器.error(f"获取团队基本信息失败: 团队id={团队id}, 错误={e}")
            return None

    async def 获取团队成员统计(self, 团队id: int) -> Dict[str, int]:
        """
        获取团队成员相关的各项统计数据

        Args:
            团队id: 团队标识

        Returns:
            包含成员统计数据的字典
        """
        try:
            SQL = """
            SELECT 
                -- 基础成员统计
                COUNT(ut.用户id) as 总成员数,
                COUNT(CASE WHEN ut.状态 = '正常' THEN 1 END) as 活跃成员数,
                COUNT(CASE WHEN ut.状态 = '暂停' THEN 1 END) as 暂停成员数,
                COUNT(CASE WHEN ut.状态 = '已移除' THEN 1 END) as 已移除成员数,
                
                -- 时间相关统计
                COUNT(CASE WHEN ut.加入时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days' AND ut.状态 = '正常' THEN 1 END) as 近30天新增,
                COUNT(CASE WHEN ut.状态 = '已移除' AND ut.更新时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days' THEN 1 END) as 近30天离开,

                -- 在线和活跃统计（使用最新登录记录）
                COUNT(DISTINCT CASE
                    WHEN ulr.登陆时间 >= CURRENT_TIMESTAMP - INTERVAL '5 minutes' AND ut.状态 = '正常'
                    THEN ut.用户id
                END) as 在线成员数,
                COUNT(DISTINCT CASE
                    WHEN ulr.登陆时间::date = CURRENT_DATE AND ut.状态 = '正常'
                    THEN ut.用户id
                END) as 今日活跃数
                
            FROM 用户团队关联表 ut
            LEFT JOIN (
                -- 子查询：获取每个用户的最新登录记录
                SELECT 用户id, MAX(登陆时间) as 登陆时间
                FROM 用户登陆记录表 
                WHERE 登陆时间 >= CURRENT_TIMESTAMP - INTERVAL '1 day'
                GROUP BY 用户id
            ) ulr ON ut.用户id = ulr.用户id
            WHERE ut.团队id = $1
            """

            结果 = await self.连接池.执行查询(SQL, (团队id,))
            return 结果[0] if 结果 else {}

        except Exception as e:
            错误日志器.error(f"获取团队成员统计失败: 团队id={团队id}, 错误={e}")
            return {}

    async def 获取团队微信好友汇总(self, 团队id: int) -> Dict[str, int]:
        """
        获取团队所有成员的微信好友汇总统计

        Args:
            团队id: 团队标识

        Returns:
            包含微信好友汇总数据的字典
        """
        try:
            SQL = """
            SELECT 
                COUNT(DISTINCT 成员汇总.用户id) as 参与统计成员数,
                SUM(成员汇总.微信个数) as 总微信数,
                SUM(成员汇总.好友数量) as 总好友数,
                SUM(成员汇总.今日新增好友) as 今日新增好友总数
            FROM (
                SELECT 
                    uwx.用户id,
                    COUNT(DISTINCT uwx.微信id) as 微信个数,
                    COUNT(DISTINCT wxhy.对方微信号id) as 好友数量,
                    COUNT(DISTINCT CASE
                        WHEN DATE(wxhy.好友入库时间) = CURDATE()
                        THEN wxhy.对方微信号id
                        ELSE NULL
                    END) as 今日新增好友
                FROM 用户团队关联表 ut
                JOIN 用户微信关联表 uwx ON ut.用户id = uwx.用户id
                LEFT JOIN 微信好友表 wxhy ON uwx.微信id = wxhy.我方微信号id
                WHERE ut.团队id = $1 AND ut.状态 = '正常' AND uwx.状态 = 1
                GROUP BY uwx.用户id
            ) as 成员汇总
            """

            结果 = await self.连接池.执行查询(SQL, (团队id,))
            return 结果[0] if 结果 else {}

        except Exception as e:
            错误日志器.error(f"获取团队微信好友汇总失败: 团队id={团队id}, 错误={e}")
            return {}

    async def 获取团队寄样汇总(self, 团队id: int) -> Dict[str, int]:
        """
        获取团队所有成员的寄样数量汇总统计

        Args:
            团队id: 团队标识

        Returns:
            包含寄样汇总数据的字典
        """
        try:
            SQL = """
            SELECT
                COUNT(DISTINCT ut.用户id) as 参与寄样成员数,
                SUM(s.数量) as 总寄样数量
            FROM 用户团队关联表 ut
            LEFT JOIN 用户寄样信息表 s ON s.用户表id = ut.用户id
            WHERE ut.团队id = $1 AND ut.状态 = '正常'
            """

            结果 = await self.连接池.执行查询(SQL, (团队id,))
            if 结果:
                原始数据 = 结果[0]
                # 确保数值类型正确转换，处理 Decimal 类型
                return {
                    "参与寄样成员数": int(原始数据.get("参与寄样成员数", 0) or 0),
                    "总寄样数量": int(原始数据.get("总寄样数量", 0) or 0),
                }
            else:
                return {"参与寄样成员数": 0, "总寄样数量": 0}

        except Exception as e:
            错误日志器.error(f"获取团队寄样汇总失败: 团队id={团队id}, 错误={e}")
            return {"参与寄样成员数": 0, "总寄样数量": 0}

    async def 获取团队成员详细列表(
        self, 团队id: int, 限制数量: int = 20
    ) -> List[Dict[str, Any]]:
        """
        获取团队成员详细列表 - 优化版本，移除冗余查询和无效数据项

        Args:
            团队id: 团队标识
            限制数量: 返回的成员数量限制，默认20

        Returns:
            包含核心成员信息的列表，移除了不必要的统计字段
        """
        try:
            SQL = """
            SELECT 
                ut.用户id,
                u.昵称 as 用户名,
                u.phone as 手机号,
                ut.职位,
                ut.加入时间,
                ut.状态,
                -- 添加最新登录时间用于排序
                COALESCE(ulr.最新登录时间, '1970-01-01') as 最新登录时间,
                -- 添加微信和好友统计
                COALESCE(wx_stats.微信个数, 0) as 微信个数,
                COALESCE(wx_stats.好友数量, 0) as 好友数量,
                COALESCE(wx_stats.今日新增好友, 0) as 今日新增好友,
                -- 添加真实的寄样数量统计
                COALESCE(sample_stats.寄样数量, 0) as 寄样数量
            FROM 用户团队关联表 ut
            LEFT JOIN 用户表 u ON ut.用户id = u.id
            LEFT JOIN (
                SELECT 用户id, MAX(登陆时间) as 最新登录时间
                FROM 用户登陆记录表
                WHERE 登陆时间 >= CURRENT_TIMESTAMP - INTERVAL '7 days'
                GROUP BY 用户id
            ) ulr ON ut.用户id = ulr.用户id
            LEFT JOIN (
                -- 子查询：获取每个用户的微信个数、好友数量和今日新增好友
                SELECT 
                    uwx.用户id,
                    COUNT(DISTINCT uwx.微信id) as 微信个数,
                    COUNT(DISTINCT wxhy.对方微信号id) as 好友数量,
                    COUNT(DISTINCT CASE
                        WHEN DATE(wxhy.好友入库时间) = CURRENT_DATE
                        THEN wxhy.对方微信号id
                        ELSE NULL
                    END) as 今日新增好友
                FROM 用户微信关联表 uwx
                LEFT JOIN 微信好友表 wxhy ON uwx.微信id = wxhy.我方微信号id
                WHERE uwx.状态 = 1  -- 只统计有效的微信绑定
                GROUP BY uwx.用户id
            ) wx_stats ON ut.用户id = wx_stats.用户id
            LEFT JOIN (
                -- 子查询：获取每个用户的寄样数量统计（基于新的关联关系）
                SELECT
                    s.用户表id as 用户id,
                    SUM(s.数量) as 寄样数量
                FROM 用户寄样信息表 s
                GROUP BY s.用户表id
            ) sample_stats ON ut.用户id = sample_stats.用户id
            WHERE ut.团队id = $1 AND ut.状态 = '正常'
            ORDER BY 
                wx_stats.好友数量 DESC,  -- 按好友数量排序，好友多的排前面
                CASE WHEN ulr.最新登录时间 IS NOT NULL THEN 0 ELSE 1 END,  -- 有登录记录的排前面
                ulr.最新登录时间 DESC,  -- 按最新登录时间排序
                ut.加入时间 DESC       -- 再按加入时间排序
            LIMIT $2
            """

            结果 = await self.连接池.执行查询(SQL, (团队id, 限制数量))
            return 结果 if 结果 else []

        except Exception as e:
            错误日志器.error(f"获取团队成员详细列表失败: 团队id={团队id}, 错误={e}")
            return []

    async def 获取团队全部数据聚合(self, 团队id: int) -> Dict[str, Any]:
        """
        获取团队全部数据的聚合查询 - 优化并发版本，移除无效数据项

        Args:
            团队id: 团队标识

        Returns:
            包含团队核心数据的字典，移除了冗余和无效数据项
        """
        try:
            数据库日志器.info(
                f"🚀 开始获取团队全部数据聚合（优化版本）: 团队id={团队id}"
            )

            # 使用协程并发执行多个查询，提高效率
            import asyncio

            # 并发执行核心查询，移除不必要的查询
            团队信息任务 = self.获取团队基本信息和公司信息(团队id)
            成员统计任务 = self.获取团队成员统计(团队id)
            微信好友汇总任务 = self.获取团队微信好友汇总(团队id)
            寄样汇总任务 = self.获取团队寄样汇总(团队id)
            成员列表任务 = self.获取团队成员详细列表(
                团队id, 15
            )  # 减少默认查询数量，提升性能

            # 等待所有查询完成，使用return_exceptions确保单个查询失败不影响其他查询
            数据库日志器.info(f"📊 并发执行 {5} 个数据库查询任务...")
            团队信息, 成员统计, 微信好友汇总, 寄样汇总, 成员列表 = await asyncio.gather(
                团队信息任务,
                成员统计任务,
                微信好友汇总任务,
                寄样汇总任务,
                成员列表任务,
                return_exceptions=True,
            )

            # 检查查询结果
            if isinstance(团队信息, Exception):
                错误日志器.error(f"获取团队信息失败: {团队信息}")
                return {"error": "获取团队信息失败"}

            if not 团队信息:
                return {"error": "团队不存在或已解散"}

            # 处理异常结果
            if isinstance(成员统计, Exception):
                错误日志器.error(f"获取成员统计失败: {成员统计}")
                成员统计 = {}

            if isinstance(微信好友汇总, Exception):
                错误日志器.error(f"获取微信好友汇总失败: {微信好友汇总}")
                微信好友汇总 = {}

            if isinstance(寄样汇总, Exception):
                错误日志器.error(f"获取寄样汇总失败: {寄样汇总}")
                寄样汇总 = {}

            if isinstance(成员列表, Exception):
                错误日志器.error(f"获取成员列表失败: {成员列表}")
                成员列表 = []

            # 合并所有数据
            聚合数据 = {
                "团队信息": 团队信息,
                "成员统计": 成员统计,
                "微信好友汇总": 微信好友汇总,
                "寄样汇总": 寄样汇总,
                "成员列表": 成员列表,
            }

            数据库日志器.info(f"获取团队全部数据聚合成功: 团队id={团队id}")
            return 聚合数据

        except Exception as e:
            错误日志器.error(f"获取团队全部数据聚合失败: 团队id={团队id}, 错误={e}")
            return {"error": f"获取数据失败: {str(e)}"}

    async def 验证团队数据去重逻辑(self, 团队id: int) -> Dict[str, Any]:
        """
        验证团队数据去重逻辑 - 确保所有统计都在数据库层面正确去重

        Args:
            团队id: 团队标识

        Returns:
            验证结果和详细报告
        """
        try:
            from datetime import datetime

            验证结果 = {
                "团队id": 团队id,
                "验证时间": datetime.now().isoformat(),
                "去重验证": {},
                "数据一致性": True,
                "问题报告": [],
            }

            # 1. 验证微信账号去重 - 确保同一微信账号不被重复统计
            微信去重验证SQL = """
            SELECT
                COUNT(*) as 总记录数,
                COUNT(DISTINCT uwx.微信id) as 去重后微信数,
                COUNT(*) - COUNT(DISTINCT uwx.微信id) as 重复记录数
            FROM 用户团队关联表 ut
            JOIN 用户微信关联表 uwx ON ut.用户id = uwx.用户id
            WHERE ut.团队id = $1 AND ut.状态 = '正常' AND uwx.状态 = 1
            """

            # 2. 验证好友去重 - 确保同一好友不被重复统计
            好友去重验证SQL = """
            SELECT
                COUNT(*) as 总记录数,
                COUNT(DISTINCT wxhy.对方微信号id) as 去重后好友数,
                COUNT(*) - COUNT(DISTINCT wxhy.对方微信号id) as 重复记录数
            FROM 用户团队关联表 ut
            JOIN 用户微信关联表 uwx ON ut.用户id = uwx.用户id
            JOIN 微信好友表 wxhy ON uwx.微信id = wxhy.我方微信号id
            WHERE ut.团队id = $1 AND ut.状态 = '正常' AND uwx.状态 = 1
                AND (wxhy.是否失效 IS NULL OR wxhy.是否失效 = 0)
            """

            # 3. 验证达人去重 - 确保同一达人不被重复统计
            达人去重验证SQL = """
            SELECT
                COUNT(*) as 总记录数,
                COUNT(DISTINCT uda.达人id) as 去重后达人数,
                COUNT(*) - COUNT(DISTINCT uda.达人id) as 重复记录数
            FROM 用户团队关联表 ut
            JOIN 用户达人关联表 uda ON ut.用户id = uda.用户id
            WHERE ut.团队id = $1 AND ut.状态 = '正常' AND uda.状态 = 1
            """

            # 执行验证查询
            微信验证结果 = await self.连接池.执行查询(微信去重验证SQL, (团队id,))
            好友验证结果 = await self.连接池.执行查询(好友去重验证SQL, (团队id,))
            达人验证结果 = await self.连接池.执行查询(达人去重验证SQL, (团队id,))

            # 处理验证结果
            验证项目 = [
                (微信验证结果, "微信", "去重后微信数"),
                (好友验证结果, "好友", "去重后好友数"),
                (达人验证结果, "达人", "去重后达人数"),
            ]

            for 结果, 类型, 字段名 in 验证项目:
                if 结果:
                    数据 = 结果[0]
                    重复数 = 数据.get("重复记录数", 0)
                    验证结果["去重验证"][类型] = {
                        "总记录数": 数据.get("总记录数", 0),
                        "去重后数量": 数据.get(字段名, 0),
                        "重复记录数": 重复数,
                        "去重正确": 重复数 == 0,
                    }

                    if 重复数 > 0:
                        验证结果["数据一致性"] = False
                        验证结果["问题报告"].append(
                            f"{类型}数据存在{重复数}条重复记录，需要优化去重逻辑"
                        )

            数据库日志器.info(
                f"团队 {团队id} 数据去重验证完成: "
                f"数据一致性={'通过' if 验证结果['数据一致性'] else '失败'}, "
                f"问题数量: {len(验证结果['问题报告'])}"
            )

            return 验证结果

        except Exception as e:
            错误日志器.error(f"验证团队数据去重逻辑失败: 团队id={团队id}, 错误={e}")
            return {
                "团队id": 团队id,
                "验证时间": datetime.now().isoformat(),
                "去重验证": {},
                "数据一致性": False,
                "问题报告": [f"验证过程异常: {str(e)}"],
            }


# 创建全局实例
团队数据查询工具实例 = 团队数据查询工具()
