"""
达人筛选查询索引优化脚本

功能：
1. 为达人管理功能的筛选查询创建优化索引
2. 特别针对补充信息筛选的EXISTS查询进行优化
3. 确保用户达人关联表和用户达人补充信息表的查询性能

使用方法：
python 手动执行/优化达人筛选查询索引.py
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 应用日志器, 错误日志器


async def 创建达人筛选优化索引():
    """创建达人筛选查询的优化索引"""
    try:
        应用日志器.info("开始创建达人筛选查询优化索引...")
        
        # 索引定义列表
        索引定义列表 = [
            # 1. 用户达人关联表的核心查询索引
            {
                "name": "idx_用户达人关联表_用户id_状态_平台",
                "sql": """
                CREATE INDEX IF NOT EXISTS idx_用户达人关联表_用户id_状态_平台 
                ON 用户达人关联表(用户id, 状态, 平台) 
                WHERE 状态 = 1
                """,
                "description": "用户达人关联表主查询索引（用户id + 状态 + 平台）"
            },
            
            # 2. 用户达人补充信息表的关联查询索引
            {
                "name": "idx_用户达人补充信息表_关联表id",
                "sql": """
                CREATE INDEX IF NOT EXISTS idx_用户达人补充信息表_关联表id 
                ON 用户达人补充信息表(用户达人关联表id)
                """,
                "description": "用户达人补充信息表关联查询索引（用于EXISTS查询优化）"
            },
            
            # 3. 用户达人关联表的认领时间排序索引
            {
                "name": "idx_用户达人关联表_认领时间_desc",
                "sql": """
                CREATE INDEX IF NOT EXISTS idx_用户达人关联表_认领时间_desc 
                ON 用户达人关联表(认领时间 DESC) 
                WHERE 状态 = 1
                """,
                "description": "用户达人关联表认领时间降序索引（用于默认排序）"
            },
            
            # 4. 达人表的搜索优化索引
            {
                "name": "idx_达人表_昵称_抖音号_uid",
                "sql": """
                CREATE INDEX IF NOT EXISTS idx_达人表_昵称_抖音号_uid 
                ON 达人表(昵称, account_douyin, uid_number) 
                WHERE 昵称 IS NOT NULL
                """,
                "description": "达人表搜索优化索引（昵称 + 抖音号 + UID）"
            },
            
            # 5. 微信达人表的搜索优化索引
            {
                "name": "idx_微信达人表_昵称_微信号",
                "sql": """
                CREATE INDEX IF NOT EXISTS idx_微信达人表_昵称_微信号 
                ON 微信达人表(昵称, finderUsername) 
                WHERE 昵称 IS NOT NULL
                """,
                "description": "微信达人表搜索优化索引（昵称 + 微信号）"
            },
            
            # 6. 用户达人补充信息表的更新时间索引
            {
                "name": "idx_用户达人补充信息表_更新时间_desc",
                "sql": """
                CREATE INDEX IF NOT EXISTS idx_用户达人补充信息表_更新时间_desc 
                ON 用户达人补充信息表(更新时间 DESC)
                """,
                "description": "用户达人补充信息表更新时间降序索引"
            },
            
            # 7. 复合索引：用户达人关联表的完整查询优化
            {
                "name": "idx_用户达人关联表_完整查询",
                "sql": """
                CREATE INDEX IF NOT EXISTS idx_用户达人关联表_完整查询 
                ON 用户达人关联表(用户id, 平台, 状态, 认领时间 DESC) 
                WHERE 状态 = 1
                """,
                "description": "用户达人关联表完整查询复合索引"
            }
        ]
        
        async with 异步连接池实例.获取连接() as conn:
            成功创建数量 = 0
            跳过数量 = 0
            
            for 索引定义 in 索引定义列表:
                try:
                    应用日志器.info(f"创建索引: {索引定义['name']} - {索引定义['description']}")
                    
                    # 执行索引创建SQL
                    await conn.execute(索引定义['sql'])
                    
                    应用日志器.info(f"✅ 索引创建成功: {索引定义['name']}")
                    成功创建数量 += 1
                    
                except Exception as e:
                    if "already exists" in str(e).lower() or "duplicate" in str(e).lower():
                        应用日志器.info(f"⏭️  索引已存在，跳过: {索引定义['name']}")
                        跳过数量 += 1
                    else:
                        错误日志器.error(f"❌ 创建索引失败: {索引定义['name']}, 错误: {str(e)}")
                        raise
            
            应用日志器.info(f"索引创建完成！成功创建: {成功创建数量}个，跳过: {跳过数量}个")
            
    except Exception as e:
        错误日志器.error(f"创建达人筛选优化索引失败: {str(e)}")
        raise


async def 分析查询性能():
    """分析筛选查询的性能"""
    try:
        应用日志器.info("开始分析达人筛选查询性能...")
        
        # 测试查询列表
        测试查询列表 = [
            {
                "name": "基础达人列表查询",
                "sql": """
                EXPLAIN (ANALYZE, BUFFERS) 
                SELECT COUNT(*) 
                FROM 用户达人关联表 uda 
                INNER JOIN 达人表 d ON uda.达人id = d.id 
                WHERE uda.用户id = 1 AND uda.平台 = '抖音' AND uda.状态 = 1
                """
            },
            {
                "name": "有补充信息筛选查询",
                "sql": """
                EXPLAIN (ANALYZE, BUFFERS) 
                SELECT COUNT(*) 
                FROM 用户达人关联表 uda 
                INNER JOIN 达人表 d ON uda.达人id = d.id 
                WHERE uda.用户id = 1 AND uda.平台 = '抖音' AND uda.状态 = 1 
                AND EXISTS (SELECT 1 FROM 用户达人补充信息表 s WHERE s.用户达人关联表id = uda.id)
                """
            },
            {
                "name": "无补充信息筛选查询",
                "sql": """
                EXPLAIN (ANALYZE, BUFFERS) 
                SELECT COUNT(*) 
                FROM 用户达人关联表 uda 
                INNER JOIN 达人表 d ON uda.达人id = d.id 
                WHERE uda.用户id = 1 AND uda.平台 = '抖音' AND uda.状态 = 1 
                AND NOT EXISTS (SELECT 1 FROM 用户达人补充信息表 s WHERE s.用户达人关联表id = uda.id)
                """
            }
        ]
        
        async with 异步连接池实例.获取连接() as conn:
            for 测试查询 in 测试查询列表:
                try:
                    应用日志器.info(f"分析查询: {测试查询['name']}")
                    
                    结果 = await conn.fetch(测试查询['sql'])
                    
                    应用日志器.info(f"查询计划 - {测试查询['name']}:")
                    for 行 in 结果:
                        应用日志器.info(f"  {行[0]}")
                    
                except Exception as e:
                    应用日志器.warning(f"分析查询失败: {测试查询['name']}, 错误: {str(e)}")
            
    except Exception as e:
        错误日志器.error(f"分析查询性能失败: {str(e)}")


async def main():
    """主函数"""
    try:
        应用日志器.info("=== 达人筛选查询索引优化开始 ===")
        
        # 1. 创建优化索引
        await 创建达人筛选优化索引()
        
        # 2. 分析查询性能（可选）
        try:
            await 分析查询性能()
        except Exception as e:
            应用日志器.warning(f"查询性能分析失败，但不影响索引创建: {str(e)}")
        
        应用日志器.info("=== 达人筛选查询索引优化完成 ===")
        
    except Exception as e:
        错误日志器.error(f"达人筛选查询索引优化失败: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
