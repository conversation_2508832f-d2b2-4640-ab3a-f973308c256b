"""
PostgreSQL异步连接池
基于asyncpg实现的高性能PostgreSQL异步连接池

特性：
1. 基于asyncpg的高性能连接池
2. 自动重连和故障恢复
3. 连接健康检查
4. 详细的连接状态监控
5. 支持事务管理
6. 连接池状态统计
"""

import asyncio
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional, Sequence, Union

import asyncpg
from asyncpg import Record

from config import PostgreSQL_数据库配置
from 日志 import 数据库日志器, 错误日志器


class Postgre_异步数据库连接池:
    """PostgreSQL异步数据库连接池"""

    _实例: Optional["Postgre_异步数据库连接池"] = None
    _锁 = asyncio.Lock()

    def __new__(cls) -> "Postgre_异步数据库连接池":
        if cls._实例 is None:
            cls._实例 = super().__new__(cls)
        return cls._实例

    def __init__(self):
        if hasattr(self, "_已初始化"):
            return

        self._连接池: Optional[asyncpg.Pool] = None
        self._已初始化 = False
        self._连接池状态 = "未初始化"
        self._上次初始化时间 = None
        self._初始化失败次数 = 0
        self._连接失败次数 = 0
        self._连接统计 = {
            "总连接数": 0,
            "活跃连接数": 0,
            "成功查询数": 0,
            "失败查询数": 0,
            "平均响应时间": 0.0,
        }
        数据库日志器.info("PostgreSQL连接池实例创建完成")

    async def 初始化数据库连接池(self) -> None:
        """初始化PostgreSQL数据库连接池"""
        async with self._锁:
            # 如果连接池已存在且未关闭，直接返回
            if self._连接池 and not self._连接池.is_closing():
                return

            try:
                数据库日志器.info("正在初始化PostgreSQL连接池...")

                # 关闭现有连接池（如果存在）
                await self._安全关闭连接池()

                # 构建连接字符串
                连接字符串 = f"postgresql://{PostgreSQL_数据库配置['user']}:{PostgreSQL_数据库配置['password']}@{PostgreSQL_数据库配置['host']}:5432/{PostgreSQL_数据库配置['database']}"

                # 创建PostgreSQL连接池 - 针对大批量导入优化
                self._连接池 = await asyncpg.create_pool(
                    连接字符串,
                    min_size=5,  # 最小连接数（提升至5，保证基础性能）
                    max_size=25,  # 最大连接数（提升至25，支持高并发导入）
                    command_timeout=60,  # 命令超时时间（提升至60秒，适应大批量操作）
                    max_queries=50000,  # 每个连接最大查询数
                    max_inactive_connection_lifetime=300,  # 非活跃连接生命周期5分钟
                    server_settings={
                        "application_name": "invitation_system_backend",
                        "timezone": "Asia/Shanghai",
                        "statement_timeout": "300000",  # SQL语句超时5分钟
                        "idle_in_transaction_session_timeout": "600000",  # 事务空闲超时10分钟
                    },
                    init=self._初始化连接回调,
                )

                # 更新状态信息
                self._连接池状态 = "正常"
                self._已初始化 = True
                self._上次初始化时间 = datetime.now()
                self._初始化失败次数 = 0
                self._连接失败次数 = 0

                数据库日志器.info(
                    "PostgreSQL连接池初始化成功 [最大连接数: 25, 最小连接数: 5] - 大批量导入优化版本"
                )

                # 测试连接
                await self._测试连接池()

            except Exception as e:
                self._连接池状态 = "初始化失败"
                self._初始化失败次数 += 1
                错误日志器.error(
                    f"初始化PostgreSQL连接池失败 (第{self._初始化失败次数}次): {str(e)}"
                )
                raise

    async def _初始化连接回调(self, 连接: asyncpg.Connection) -> None:
        """连接初始化回调函数"""
        try:
            # 设置连接编码
            await 连接.execute("SET CLIENT_ENCODING TO 'UTF8'")
            # 设置时区
            await 连接.execute("SET TIMEZONE TO 'Asia/Shanghai'")
            数据库日志器.debug("连接初始化完成")
        except Exception as e:
            错误日志器.error(f"连接初始化失败: {str(e)}")

    async def _测试连接池(self) -> None:
        """测试连接池是否正常工作 - 直接使用连接池避免循环调用"""
        try:
            # 直接从连接池获取连接，避免循环调用获取连接方法
            if not self._连接池:
                raise RuntimeError("连接池未初始化")

            async with self._连接池.acquire() as 连接:
                结果 = await 连接.fetchval("SELECT 1")
                if 结果 == 1:
                    数据库日志器.info("✅ PostgreSQL连接池测试通过")
                else:
                    数据库日志器.warning("⚠️ PostgreSQL连接池测试异常")
        except Exception as e:
            错误日志器.error(f"PostgreSQL连接池测试失败: {str(e)}")
            raise

    def _是否为SQL相关错误(self, error: Exception) -> bool:
        """判断是否为SQL相关错误（不需要重新初始化连接池）"""
        错误信息 = str(error).lower()
        SQL错误关键词 = [
            "syntax error",
            "column",
            "does not exist",
            "expects",
            "arguments",
            "data type",
            "constraint",
            "violation",
            "invalid input",
            "permission denied",
            "relation",
            "function",
            "operator",
            "cast",
            "duplicate key",
            "inconsistent types",  # 参数类型不一致错误
            "text versus character varying",  # 字符串类型冲突
            "deduced for parameter",  # 参数推导错误
        ]
        return any(关键词 in 错误信息 for 关键词 in SQL错误关键词)

    def _是否为连接相关错误(self, error: Exception) -> bool:
        """判断是否为连接相关错误（可能需要重新初始化）"""
        错误信息 = str(error).lower()
        连接错误关键词 = [
            "connection",
            "timeout",
            "refused",
            "closed",
            "network",
            "host",
            "unreachable",
            "reset",
            "broken pipe",
            "no route",
        ]
        return any(关键词 in 错误信息 for 关键词 in 连接错误关键词)

    def _需要重新初始化连接池(self, error: Exception, 重试次数: int) -> bool:
        """判断是否需要重新初始化连接池

        注意：SQL相关错误在调用此方法前已被直接抛出，不会到达这里
        """
        # 连接相关错误，在多次重试后才考虑重新初始化
        if self._是否为连接相关错误(error):
            return 重试次数 >= 2  # 第3次重试时才重新初始化

        # 其他错误，更加保守的处理 - 只有在最后一次重试时才重新初始化
        return 重试次数 >= 2

    @asynccontextmanager
    async def 获取连接(self) -> AsyncGenerator[asyncpg.Connection, None]:
        """获取数据库连接 - 支持自动恢复"""
        连接 = None
        重试次数 = 0
        最大重试次数 = 3

        while 重试次数 < 最大重试次数:
            try:
                # 确保连接池已初始化且可用
                if not self._连接池 or self._连接池.is_closing():
                    数据库日志器.info("连接池未初始化或已关闭，正在重新初始化...")
                    await self.初始化数据库连接池()

                # 再次检查连接池状态
                if self._连接池 is None:
                    raise RuntimeError("连接池初始化失败，无法获取数据库连接")

                if self._连接池.is_closing():
                    raise RuntimeError("连接池已关闭，无法获取数据库连接")

                连接 = await self._连接池.acquire()
                self._连接统计["活跃连接数"] += 1

                try:
                    yield 连接
                finally:
                    # 确保连接被正确释放
                    if 连接:
                        try:
                            if self._连接池 is not None:
                                await self._连接池.release(连接)
                            self._连接统计["活跃连接数"] = max(
                                0, self._连接统计["活跃连接数"] - 1
                            )
                        except Exception as release_error:
                            错误日志器.error(f"释放连接失败: {str(release_error)}")
                return

            except Exception as e:
                # 使用新的错误分类逻辑
                if self._是否为SQL相关错误(e):
                    # SQL相关错误直接抛出，不重试，不增加重试次数
                    数据库日志器.error(f"🚨 SQL相关错误，不重试: {str(e)}")
                    raise

                # 非SQL错误才增加重试次数
                重试次数 += 1
                self._连接失败次数 += 1

                if 重试次数 < 最大重试次数:
                    # 记录错误信息
                    if self._是否为连接相关错误(e):
                        数据库日志器.warning(
                            f"连接相关错误，正在重试 ({重试次数}/{最大重试次数}): {str(e)}"
                        )
                    else:
                        数据库日志器.warning(
                            f"获取连接失败，正在重试 ({重试次数}/{最大重试次数}): {str(e)}"
                        )

                    await asyncio.sleep(0.5 * 重试次数)  # 指数退避

                    # 根据错误类型决定是否重新初始化连接池
                    if self._需要重新初始化连接池(e, 重试次数):
                        try:
                            数据库日志器.info(
                                f"第{重试次数}次重试，尝试重新初始化连接池..."
                            )
                            await self._强制重新初始化()
                        except Exception as init_error:
                            数据库日志器.error(
                                f"重新初始化连接池失败: {str(init_error)}"
                            )
                    else:
                        数据库日志器.debug("错误类型不需要重新初始化连接池，直接重试")
                else:
                    错误日志器.error(f"获取连接最终失败: {str(e)}")
                    raise

    async def 执行查询(
        self, 查询语句: str, 参数: Optional[Union[tuple, list, Sequence]] = None
    ) -> List[Dict[str, Any]]:
        """
        执行查询并返回结果

        Args:
            查询语句: SQL查询语句
            参数: 查询参数，支持 tuple、list 或任何序列类型，内部自动转换为 tuple

        Returns:
            查询结果列表

        Note:
            为了开发便利性，接受 list 类型参数，但内部统一转换为 tuple 以确保类型安全和性能
        """
        开始时间 = datetime.now()

        try:
            async with self.获取连接() as 连接:
                if 参数:
                    # 统一转换为 tuple，确保类型安全和性能
                    参数_tuple = tuple(参数) if not isinstance(参数, tuple) else 参数
                    记录列表: List[Record] = await 连接.fetch(查询语句, *参数_tuple)
                else:
                    记录列表: List[Record] = await 连接.fetch(查询语句)

                # 转换为字典列表
                结果 = [dict(记录) for 记录 in 记录列表]

                # 更新统计信息
                执行时间 = (datetime.now() - 开始时间).total_seconds()
                self._更新查询统计(True, 执行时间)

                数据库日志器.debug(
                    f"查询执行成功，返回 {len(结果)} 条记录，耗时 {执行时间:.3f}s"
                )
                return 结果

        except Exception as e:
            执行时间 = (datetime.now() - 开始时间).total_seconds()
            self._更新查询统计(False, 执行时间)
            错误日志器.error(f"查询执行失败: {str(e)}, SQL: {查询语句}")
            raise

    async def 执行插入(
        self, 插入语句: str, 参数: Optional[Union[tuple, list, Sequence]] = None
    ) -> Optional[int]:
        """执行插入并返回插入的ID"""
        开始时间 = datetime.now()

        try:
            async with self.获取连接() as 连接:
                if 参数:
                    # 统一转换为 tuple，确保类型安全和性能
                    参数_tuple = tuple(参数) if not isinstance(参数, tuple) else 参数
                    await 连接.execute(插入语句, *参数_tuple)
                else:
                    await 连接.execute(插入语句)

                # 更新统计信息
                执行时间 = (datetime.now() - 开始时间).total_seconds()
                self._更新查询统计(True, 执行时间)

                数据库日志器.debug(f"插入执行成功，耗时 {执行时间:.3f}s")
                return None  # PostgreSQL的execute不直接返回id，需要使用RETURNING子句

        except Exception as e:
            执行时间 = (datetime.now() - 开始时间).total_seconds()
            self._更新查询统计(False, 执行时间)
            错误日志器.error(f"插入执行失败: {str(e)}, SQL: {插入语句}")
            raise

    async def 执行插入并返回id(
        self, 插入语句: str, 参数: Optional[tuple] = None
    ) -> Optional[int]:
        """执行插入并返回新插入记录的ID（使用RETURNING子句）"""
        开始时间 = datetime.now()

        try:
            # 确保SQL语句包含RETURNING子句
            if "RETURNING" not in 插入语句.upper():
                # 自动添加RETURNING id子句
                插入语句 = 插入语句.rstrip(";") + " RETURNING id"

            async with self.获取连接() as 连接:
                if 参数:
                    结果 = await 连接.fetchval(插入语句, *参数)
                else:
                    结果 = await 连接.fetchval(插入语句)

                # 更新统计信息
                执行时间 = (datetime.now() - 开始时间).total_seconds()
                self._更新查询统计(True, 执行时间)

                数据库日志器.debug(
                    f"插入执行成功，返回id: {结果}，耗时 {执行时间:.3f}s"
                )
                return 结果

        except Exception as e:
            执行时间 = (datetime.now() - 开始时间).total_seconds()
            self._更新查询统计(False, 执行时间)
            错误日志器.error(f"插入执行失败: {str(e)}, SQL: {插入语句}")
            raise

    async def 执行更新(
        self, 更新语句: str, 参数: Optional[Union[tuple, list, Sequence]] = None
    ) -> int:
        """执行更新并返回影响的行数"""
        开始时间 = datetime.now()

        try:
            async with self.获取连接() as 连接:
                if 参数:
                    # 统一转换为 tuple，确保类型安全和性能
                    参数_tuple = tuple(参数) if not isinstance(参数, tuple) else 参数
                    结果 = await 连接.execute(更新语句, *参数_tuple)
                else:
                    结果 = await 连接.execute(更新语句)

                # 解析影响的行数
                影响行数 = (
                    int(结果.split()[-1])
                    if 结果.startswith(("UPDATE", "DELETE"))
                    else 0
                )

                # 更新统计信息
                执行时间 = (datetime.now() - 开始时间).total_seconds()
                self._更新查询统计(True, 执行时间)

                数据库日志器.debug(
                    f"更新执行成功，影响 {影响行数} 行，耗时 {执行时间:.3f}s"
                )
                return 影响行数

        except Exception as e:
            执行时间 = (datetime.now() - 开始时间).total_seconds()
            self._更新查询统计(False, 执行时间)
            错误日志器.error(f"更新执行失败: {str(e)}, SQL: {更新语句}")
            raise

    async def 批量执行更新(
        self, 更新语句: str, 参数列表: List[Union[tuple, list]]
    ) -> int:
        """批量执行更新并返回总影响的行数"""
        if not 参数列表:
            return 0

        开始时间 = datetime.now()
        总影响行数 = 0

        try:
            async with self.获取连接() as 连接:
                async with 连接.transaction():
                    for 参数 in 参数列表:
                        参数_tuple = (
                            tuple(参数) if not isinstance(参数, tuple) else 参数
                        )
                        结果 = await 连接.execute(更新语句, *参数_tuple)

                        # 解析影响的行数
                        影响行数 = (
                            int(结果.split()[-1])
                            if 结果.startswith(("UPDATE", "DELETE"))
                            else 0
                        )
                        总影响行数 += 影响行数

            # 更新统计信息
            执行时间 = (datetime.now() - 开始时间).total_seconds()
            self._更新查询统计(True, 执行时间)

            数据库日志器.debug(
                f"批量更新执行成功: 总影响行数={总影响行数}, 执行时间={执行时间:.3f}s"
            )
            return 总影响行数

        except Exception as e:
            执行时间 = (datetime.now() - 开始时间).total_seconds()
            self._更新查询统计(False, 执行时间)
            错误日志器.error(f"批量更新执行失败: {str(e)}, SQL: {更新语句}")
            raise

    async def 执行单值查询(
        self, 查询语句: str, 参数: Optional[Union[tuple, list, Sequence]] = None
    ) -> Any:
        """
        执行查询并返回单个值（fetchval的便利方法）

        Args:
            查询语句: SQL查询语句
            参数: 查询参数

        Returns:
            查询结果的单个值
        """
        开始时间 = datetime.now()

        try:
            async with self.获取连接() as 连接:
                if 参数:
                    参数_tuple = tuple(参数) if not isinstance(参数, tuple) else 参数
                    结果 = await 连接.fetchval(查询语句, *参数_tuple)
                else:
                    结果 = await 连接.fetchval(查询语句)

                # 更新统计信息
                执行时间 = (datetime.now() - 开始时间).total_seconds()
                self._更新查询统计(True, 执行时间)

                数据库日志器.debug(f"单值查询执行成功，耗时 {执行时间:.3f}s")
                return 结果

        except Exception as e:
            执行时间 = (datetime.now() - 开始时间).total_seconds()
            self._更新查询统计(False, 执行时间)
            错误日志器.error(f"单值查询执行失败: {str(e)}, SQL: {查询语句}")
            raise

    async def 执行单行查询(
        self, 查询语句: str, 参数: Optional[Union[tuple, list, Sequence]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        执行查询并返回单行结果（fetchrow的便利方法）

        Args:
            查询语句: SQL查询语句
            参数: 查询参数

        Returns:
            查询结果的单行数据或None
        """
        开始时间 = datetime.now()

        try:
            async with self.获取连接() as 连接:
                if 参数:
                    参数_tuple = tuple(参数) if not isinstance(参数, tuple) else 参数
                    记录 = await 连接.fetchrow(查询语句, *参数_tuple)
                else:
                    记录 = await 连接.fetchrow(查询语句)

                # 转换为字典
                结果 = dict(记录) if 记录 else None

                # 更新统计信息
                执行时间 = (datetime.now() - 开始时间).total_seconds()
                self._更新查询统计(True, 执行时间)

                数据库日志器.debug(f"单行查询执行成功，耗时 {执行时间:.3f}s")
                return 结果

        except Exception as e:
            执行时间 = (datetime.now() - 开始时间).total_seconds()
            self._更新查询统计(False, 执行时间)
            错误日志器.error(f"单行查询执行失败: {str(e)}, SQL: {查询语句}")
            raise

    def _更新查询统计(self, 成功: bool, 执行时间: float) -> None:
        """更新查询统计信息"""
        if 成功:
            self._连接统计["成功查询数"] += 1
        else:
            self._连接统计["失败查询数"] += 1

        # 更新平均响应时间
        总查询数 = self._连接统计["成功查询数"] + self._连接统计["失败查询数"]
        当前平均时间 = self._连接统计["平均响应时间"]
        self._连接统计["平均响应时间"] = (
            当前平均时间 * (总查询数 - 1) + 执行时间
        ) / 总查询数

    async def _安全关闭连接池(self) -> None:
        """安全关闭连接池"""
        if self._连接池:
            try:
                await self._连接池.close()
                数据库日志器.info("连接池已安全关闭")
            except Exception as e:
                数据库日志器.warning(f"关闭连接池时发生异常: {e}")
            finally:
                self._连接池 = None

    async def _强制重新初始化(self) -> None:
        """强制重新初始化连接池"""
        async with self._锁:
            try:
                数据库日志器.info("开始强制重新初始化PostgreSQL连接池...")

                # 保存旧连接池引用
                旧连接池 = self._连接池
                self._连接池 = None

                # 关闭旧连接池
                if 旧连接池:
                    try:
                        await 旧连接池.close()
                        await asyncio.sleep(0.1)
                    except Exception as close_error:
                        数据库日志器.warning(f"强制关闭连接池时发生异常: {close_error}")

                # 重新初始化
                await self.初始化数据库连接池()
                数据库日志器.info("PostgreSQL连接池重新初始化完成")

            except Exception as e:
                错误日志器.error(f"强制重新初始化失败: {str(e)}")
                # 确保连接池状态一致
                self._连接池 = None
                raise

    async def 获取连接池状态(self) -> Dict[str, Any]:
        """获取连接池状态信息"""
        状态信息 = {
            "连接池状态": self._连接池状态,
            "上次初始化时间": self._上次初始化时间.isoformat()
            if self._上次初始化时间
            else None,
            "初始化失败次数": self._初始化失败次数,
            "连接失败次数": self._连接失败次数,
            "连接统计": self._连接统计.copy(),
        }

        if self._连接池:
            状态信息.update(
                {
                    "最小连接数": self._连接池._minsize,
                    "最大连接数": self._连接池._maxsize,
                    "当前连接数": self._连接池.get_size(),
                    "空闲连接数": self._连接池.get_idle_size(),
                }
            )

        return 状态信息

    async def 关闭连接池(self) -> None:
        """关闭连接池"""
        await self._安全关闭连接池()
        self._连接池状态 = "已关闭"
        self._已初始化 = False
        数据库日志器.info("PostgreSQL连接池已关闭")

    @property
    def 已初始化(self) -> bool:
        """获取连接池是否已初始化"""
        return self._已初始化


# 创建全局连接池实例
Postgre_异步连接池实例 = Postgre_异步数据库连接池()
