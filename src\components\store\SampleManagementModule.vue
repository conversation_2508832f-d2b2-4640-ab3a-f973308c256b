<template>
  <div class="sample-management-module">
    <!-- 统计概览卡片 -->
    <div class="stats-row">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card class="stat-card">
            <a-statistic
              title="样品总数"
              :value="样品统计.总数"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <experiment-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card class="stat-card">
            <a-statistic
              title="待审核"
              :value="样品统计.待审核"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card class="stat-card">
            <a-statistic
              title="已发货"
              :value="样品统计.已发货"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <car-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card class="stat-card">
            <a-statistic
              title="已送达"
              :value="样品统计.已送达"
              :value-style="{ color: '#13c2c2' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 操作栏 -->
    <a-card class="action-card" :bordered="false">
      <div class="action-bar">
        <div class="search-filters">
          <a-input-search
            v-model:value="搜索关键词"
            placeholder="搜索收件人姓名"
            style="width: 300px"
            @search="执行搜索"
            allow-clear
          />
          <a-select
            v-model:value="筛选状态"
            placeholder="审核状态"
            style="width: 120px"
            @change="执行搜索"
            allow-clear
          >
            <a-select-option :value="null">全部状态</a-select-option>
            <a-select-option :value="0">待审核</a-select-option>
            <a-select-option :value="1">通过</a-select-option>
            <a-select-option :value="-1">拒绝</a-select-option>
          </a-select>
          <a-range-picker
            v-model:value="日期范围"
            :placeholder="['开始日期', '结束日期']"
            @change="执行搜索"
          />
        </div>

        <!-- 快递查询配额信息 -->
        <div class="quota-info" v-if="!快递查询配额.是否管理员">
          <a-space>
            <a-tag
              :color="快递查询配额.配额状态 === '充足' ? 'green' :
                     快递查询配额.配额状态 === '紧张' ? 'orange' : 'red'"
            >
              <template #icon>
                <search-outlined />
              </template>
              快递查询: {{ 快递查询配额.剩余次数 }}/{{ 快递查询配额.每日限额 }}
            </a-tag>
            <a-tooltip
              title="每日快递查询次数限制，管理员无限制。查询失败不计入次数。"
              placement="top"
            >
              <question-circle-outlined style="color: #999;" />
            </a-tooltip>
          </a-space>
        </div>

        <div class="action-buttons">
          <a-button @click="刷新样品列表" :loading="加载中">
            <template #icon>
              <reload-outlined />
            </template>
            刷新
          </a-button>
          <a-button type="primary" @click="显示样品申请弹窗">
            <template #icon>
              <plus-outlined />
            </template>
            新增样品申请
          </a-button>
          <a-button @click="导出样品数据">
            <template #icon>
              <download-outlined />
            </template>
            导出数据
          </a-button>
        </div>
      </div>
    </a-card>

    <!-- 样品列表 -->
    <a-card class="list-card" :bordered="false">
      <a-table
        :columns="表格列配置"
        :data-source="样品列表"
        :loading="加载中"
        :pagination="分页配置"
        row-key="id"
        size="middle"
        @change="处理表格变化"
      >
        <!-- 表格内容自定义渲染 -->
        <template #bodyCell="{ column, record }">
          <!-- 产品名称列 -->
          <template v-if="column.key === '产品名称'">
            <span v-if="record.产品名称">{{ record.产品名称 }}</span>
            <span v-else style="color: #999;">未关联产品</span>
          </template>

          <!-- 地址列 -->
          <template v-else-if="column.key === '地址'">
            <a-tooltip :title="record.地址">
              <span>{{ record.地址 || '-' }}</span>
            </a-tooltip>
          </template>

          <!-- 规格列 -->
          <template v-else-if="column.key === '规格'">
            <span>{{ record.规格 || '-' }}</span>
          </template>

          <!-- 用户审核状态列 -->
          <template v-else-if="column.key === '用户审核状态'">
            <a-tag :color="sampleService.获取审核状态颜色(record.用户审核状态 ?? 0)">
              {{ sampleService.获取审核状态文本(record.用户审核状态 ?? 0) }}
            </a-tag>
          </template>

          <!-- 团队审核状态列 -->
          <template v-else-if="column.key === '团队审核状态'">
            <a-tag :color="sampleService.获取审核状态颜色(record.团队审核状态 ?? 0)">
              {{ sampleService.获取审核状态文本(record.团队审核状态 ?? 0) }}
            </a-tag>
          </template>

          <!-- 快递状态列 -->
          <template v-else-if="column.key === '快递状态'">
            <a-tag :color="sampleService.获取快递状态颜色(record.快递状态 ?? 0)">
              {{ sampleService.获取快递状态文本(record.快递状态 ?? 0) }}
            </a-tag>
          </template>

          <!-- 快递单号列 -->
          <template v-else-if="column.key === '快递单号'">
            <span v-if="record.快递单号">{{ record.快递单号 }}</span>
            <span v-else style="color: #999;">-</span>
          </template>

          <!-- 创建时间列 -->
          <template v-else-if="column.key === '创建时间'">
            <span>{{ 格式化时间(record.创建时间) }}</span>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === '操作'">
            <a-space>
              <a-button size="small" @click="查看样品详情(record)">
                <eye-outlined />
                详情
              </a-button>
              <a-button
                v-if="需要审核(record)"
                size="small"
                type="primary"
                @click="审核样品(record)"
              >
                <check-circle-outlined />
                审核
              </a-button>
              <a-button
                v-if="可以发货(record)"
                size="small"
                @click="发货样品(record)"
              >
                <car-outlined />
                发货
              </a-button>
              <a-button
                v-if="record.快递单号"
                size="small"
                @click="更新物流(record)"
              >
                <search-outlined />
                物流
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 样品申请弹窗 -->
    <a-modal
      v-model:open="申请弹窗可见"
      title="样品申请"
      width="600px"
      @ok="提交样品申请"
      @cancel="关闭申请弹窗"
    >
      <a-form
        ref="申请表单引用"
        :model="申请表单数据"
        :rules="申请表单规则"
        layout="vertical"
      >
        <a-form-item label="选择产品" name="产品id">
          <a-select
            v-model:value="申请表单数据.产品id"
            placeholder="请选择要申请样品的产品"
            show-search
            :filter-option="(input, option) => option.label.toLowerCase().includes(input.toLowerCase())"
            @change="处理产品规格变化"
          >
            <a-select-option
              v-for="product in 产品列表"
              :key="product.id"
              :value="product.id"
              :label="product.产品名称"
            >
              {{ product.产品名称 }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="申请数量" name="申请数量">
          <a-input-number v-model:value="申请表单数据.申请数量" :min="1" :max="10" />
        </a-form-item>

        <a-form-item label="产品规格" name="规格">
          <div v-if="当前产品规格选项.length" class="spec-selection-container">
            <div class="spec-options">
              <div class="spec-description">
                <span style="color: #666; font-size: 12px;">
                  请选择需要的产品规格（可多选）：
                </span>
              </div>
              <a-checkbox-group
                v-model:value="申请表单数据.规格数组"
                class="spec-checkbox-group"
                @change="处理规格选择变化"
              >
                <a-checkbox
                  v-for="规格 in 当前产品规格选项"
                  :key="规格"
                  :value="规格"
                  class="spec-checkbox"
                >
                  {{ 规格 }}
                </a-checkbox>
              </a-checkbox-group>
            </div>
          </div>
          <div v-else class="no-specs-hint">
            <span style="color: #999; font-size: 12px;">
              请先选择产品
            </span>
          </div>
        </a-form-item>

        <a-form-item label="联系人选择" name="联系人选择">
          <a-select
            v-model:value="申请表单数据.选中联系人"
            placeholder="选择已有联系人或手动输入"
            allow-clear
            show-search
            :filter-option="false"
            @search="搜索联系人"
            @change="处理联系人选择变化"
            @clear="清空联系人选择"
          >
            <a-select-option value="manual">手动输入联系信息</a-select-option>
            <a-select-option
              v-for="联系人 in 联系人列表"
              :key="联系人.用户联系人id"
              :value="联系人.用户联系人id"
            >
              {{ 联系人.姓名 }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 当选择了联系人且有多个地址时，显示地址选择 -->
        <a-form-item
          v-if="当前联系人地址选项.length > 0"
          label="选择地址"
          name="地址选择"
        >
          <a-select
            v-model:value="申请表单数据.选中地址索引"
            placeholder="请选择收货地址"
            @change="处理地址选择变化"
          >
            <a-select-option
              v-for="(地址, index) in 当前联系人地址选项"
              :key="index"
              :value="index"
            >
              {{ 地址.显示文本 }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="收件人" name="申请人">
          <a-input
            v-model:value="申请表单数据.申请人"
            placeholder="请输入收件人姓名"
            :disabled="申请表单数据.选中联系人 && 申请表单数据.选中联系人 !== 'manual'"
          />
        </a-form-item>

        <a-form-item label="收货地址" name="收货地址">
          <a-textarea
            v-model:value="申请表单数据.收货地址"
            :rows="3"
            placeholder="请输入详细的收货地址"
            :disabled="申请表单数据.选中联系人 && 申请表单数据.选中联系人 !== 'manual'"
          />
        </a-form-item>

        <a-form-item label="联系方式" name="联系方式">
          <a-input
            v-model:value="申请表单数据.联系方式"
            placeholder="请输入联系电话"
            :disabled="申请表单数据.选中联系人 && 申请表单数据.选中联系人 !== 'manual'"
          />
        </a-form-item>

        <a-form-item label="申请原因" name="申请原因">
          <a-textarea v-model:value="申请表单数据.申请原因" :rows="4" placeholder="请说明申请样品的原因和用途" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 样品详情弹窗 -->
    <a-modal
      v-model:open="详情弹窗可见"
      title="样品详情"
      width="800px"
      :footer="null"
    >
      <div v-if="当前样品详情" class="sample-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="产品名称">
            <span v-if="当前样品详情.产品名称">{{ 当前样品详情.产品名称 }}</span>
            <span v-else style="color: #999;">未关联产品</span>
          </a-descriptions-item>
          <a-descriptions-item label="样品数量">{{ 当前样品详情.数量 || '-' }}</a-descriptions-item>
          <a-descriptions-item label="收件人">{{ 当前样品详情.收件人 || '-' }}</a-descriptions-item>
          <a-descriptions-item label="联系电话">{{ 当前样品详情.电话 || '-' }}</a-descriptions-item>
          <a-descriptions-item label="收货地址" :span="2">{{ 当前样品详情.地址 || '-' }}</a-descriptions-item>
          <a-descriptions-item label="规格">{{ 当前样品详情.规格 || '-' }}</a-descriptions-item>
          <a-descriptions-item label="寄样备注">{{ 当前样品详情.寄样备注 || '-' }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ 格式化时间(当前样品详情.创建时间) }}</a-descriptions-item>
          <a-descriptions-item label="用户审核状态">
            <a-tag :color="sampleService.获取审核状态颜色(当前样品详情.用户审核状态 ?? 0)">
              {{ sampleService.获取审核状态文本(当前样品详情.用户审核状态 ?? 0) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="团队审核状态">
            <a-tag :color="sampleService.获取审核状态颜色(当前样品详情.团队审核状态 ?? 0)">
              {{ sampleService.获取审核状态文本(当前样品详情.团队审核状态 ?? 0) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="快递状态">
            <a-tag :color="sampleService.获取快递状态颜色(当前样品详情.快递状态 ?? 0)">
              {{ sampleService.获取快递状态文本(当前样品详情.快递状态 ?? 0) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="快递单号" v-if="当前样品详情.快递单号">{{ 当前样品详情.快递单号 }}</a-descriptions-item>
          <a-descriptions-item label="快递公司" v-if="当前样品详情.快递公司">{{ 当前样品详情.快递公司 }}</a-descriptions-item>
          <a-descriptions-item label="审核备注" :span="2" v-if="当前样品详情.审核备注">{{ 当前样品详情.审核备注 }}</a-descriptions-item>
          <a-descriptions-item label="审核时间" v-if="当前样品详情.审核时间">{{ 格式化时间(当前样品详情.审核时间) }}</a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ 格式化时间(当前样品详情.更新时间) }}</a-descriptions-item>
        </a-descriptions>
        
        <!-- 审核记录 -->
        <div v-if="当前样品详情.审核记录" class="audit-history">
          <h4>审核记录</h4>
          <a-timeline>
            <a-timeline-item
              v-for="记录 in 当前样品详情.审核记录"
              :key="记录.时间"
              :color="记录.状态 === '通过' ? 'green' : '红色'"
            >
              <p>{{ 记录.操作 }} - {{ 记录.操作人 }}</p>
              <p>时间：{{ 格式化日期(记录.时间) }}</p>
              <p v-if="记录.备注">备注：{{ 记录.备注 }}</p>
            </a-timeline-item>
          </a-timeline>
        </div>
      </div>
    </a-modal>

    <!-- 审核弹窗 -->
    <a-modal
      v-model:open="审核弹窗可见"
      title="样品审核"
      width="500px"
      @ok="提交审核结果"
      @cancel="关闭审核弹窗"
    >
      <a-form
        ref="审核表单引用"
        :model="审核表单数据"
        :rules="审核表单规则"
        layout="vertical"
      >
        <a-form-item label="审核类型" name="审核类型">
          <a-radio-group v-model:value="审核表单数据.审核类型">
            <a-radio
              value="用户审核"
              :disabled="当前审核样品?.用户审核状态 === 1 || 当前审核样品?.用户审核状态 === -1"
            >
              用户审核
              <span v-if="当前审核样品?.用户审核状态 === 1" style="color: #52c41a; margin-left: 8px;">(已通过)</span>
              <span v-if="当前审核样品?.用户审核状态 === -1" style="color: #ff4d4f; margin-left: 8px;">(已拒绝)</span>
            </a-radio>
            <a-radio
              value="团队审核"
              :disabled="当前审核样品?.团队审核状态 === 1 || 当前审核样品?.团队审核状态 === -1"
            >
              团队审核
              <span v-if="当前审核样品?.团队审核状态 === 1" style="color: #52c41a; margin-left: 8px;">(已通过)</span>
              <span v-if="当前审核样品?.团队审核状态 === -1" style="color: #ff4d4f; margin-left: 8px;">(已拒绝)</span>
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="审核结果" name="审核结果">
          <a-radio-group v-model:value="审核表单数据.审核结果">
            <a-radio value="通过">通过</a-radio>
            <a-radio value="拒绝">拒绝</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="审核备注" name="审核备注">
          <a-textarea v-model:value="审核表单数据.审核备注" :rows="4" placeholder="请输入审核意见" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import {
  CarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  DownloadOutlined,
  ExperimentOutlined,
  EyeOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
  ReloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { onBeforeUnmount, onMounted, reactive, ref } from 'vue'

// 导入API服务
import productService from '@/services/productService'
import sampleService from '@/services/sampleService'

// 组件名称定义
defineOptions({
  name: 'SampleManagementModule'
})

// 响应式数据定义
const 加载中 = ref(false)
const 搜索关键词 = ref('')
const 筛选状态 = ref(null)
const 日期范围 = ref([])

// 弹窗状态
const 申请弹窗可见 = ref(false)
const 详情弹窗可见 = ref(false)
const 审核弹窗可见 = ref(false)

// 当前操作的样品
const 当前样品详情 = ref(null)
const 当前审核样品 = ref(null)

// 样品统计数据
const 样品统计 = reactive({
  总数: 0,
  待审核: 0,
  已发货: 0,
  已送达: 0
})

// 快递查询配额信息
const 快递查询配额 = reactive({
  每日限额: 30,
  今日已用: 0,
  剩余次数: 30,
  是否可查询: true,
  是否管理员: false,
  配额状态: '充足'
})

// 样品列表数据
const 样品列表 = ref([])
const 产品列表 = ref([])
const 联系人列表 = ref([])
const 当前联系人地址选项 = ref([])
const 当前产品规格选项 = ref([])

// 申请表单数据
const 申请表单数据 = reactive({
  产品id: null,
  申请人: '',
  申请数量: 1,
  规格: '',
  规格数组: [], // 新增：支持多选规格
  申请原因: '',
  收货地址: '',
  联系方式: '',
  选中联系人: null,
  选中地址索引: null
})

// 审核表单数据
const 审核表单数据 = reactive({
  审核类型: '',
  审核结果: '',
  审核备注: ''
})

// 表单引用
const 申请表单引用 = ref()
const 审核表单引用 = ref()

// 表单验证规则
const 申请表单规则 = {
  产品id: [{ required: true, message: '请选择要申请样品的产品' }],
  申请数量: [{ required: true, message: '请输入申请数量' }],
  规格: [
    {
      validator: (rule, value) => {
        // 如果有产品规格选项但没有选择任何规格，则验证失败
        if (当前产品规格选项.value.length > 0 && (!申请表单数据.规格数组 || 申请表单数据.规格数组.length === 0)) {
          return Promise.reject('请选择产品规格')
        }
        return Promise.resolve()
      }
    }
  ],
  申请人: [{ required: true, message: '请输入收件人姓名' }],
  申请原因: [{ required: true, message: '请输入申请原因' }],
  收货地址: [{ required: true, message: '请输入收货地址' }],
  联系方式: [{ required: true, message: '请输入联系方式' }]
}

const 审核表单规则 = {
  审核类型: [{ required: true, message: '请选择审核类型' }],
  审核结果: [{ required: true, message: '请选择审核结果' }],
  审核备注: [{ required: true, message: '请输入审核备注' }]
}

// 表格列配置 - 使用数据库实际字段名
const 表格列配置 = [
  {
    title: '产品名称',
    dataIndex: '产品名称',
    key: '产品名称',
    width: 200
  },
  {
    title: '收件人',
    dataIndex: '收件人',
    key: '收件人',
    width: 120
  },
  {
    title: '地址',
    dataIndex: '地址',
    key: '地址',
    width: 200,
    ellipsis: true
  },
  {
    title: '数量',
    dataIndex: '数量',
    key: '数量',
    width: 80,
    align: 'center'
  },
  {
    title: '规格',
    dataIndex: '规格',
    key: '规格',
    width: 100
  },
  {
    title: '用户审核',
    dataIndex: '用户审核状态',
    key: '用户审核状态',
    width: 100,
    align: 'center'
  },
  {
    title: '团队审核',
    dataIndex: '团队审核状态',
    key: '团队审核状态',
    width: 100,
    align: 'center'
  },
  {
    title: '快递状态',
    dataIndex: '快递状态',
    key: '快递状态',
    width: 100,
    align: 'center'
  },
  {
    title: '快递单号',
    dataIndex: '快递单号',
    key: '快递单号',
    width: 150
  },
  {
    title: '创建时间',
    dataIndex: '创建时间',
    key: '创建时间',
    width: 150
  },
  {
    title: '操作',
    key: '操作',
    width: 200,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const 分页配置 = reactive({
  current: 1,
  pageSize: 10,
  total: 156,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})





/**
 * 判断是否需要审核
 * @param {Object} record - 样品记录
 * @returns {boolean} 是否需要审核
 */
const 需要审核 = (record) => {
  if (!record) return false
  const 用户审核状态 = record.用户审核状态 ?? 0
  const 团队审核状态 = record.团队审核状态 ?? 0
  return 用户审核状态 === 0 || 团队审核状态 === 0
}

/**
 * 判断是否可以发货
 * @param {Object} record - 样品记录
 * @returns {boolean} 是否可以发货
 */
const 可以发货 = (record) => {
  if (!record) return false
  const 用户审核状态 = record.用户审核状态 ?? 0
  const 团队审核状态 = record.团队审核状态 ?? 0
  const 快递状态 = record.快递状态 ?? 0
  return 用户审核状态 === 1 &&
         团队审核状态 === 1 &&
         快递状态 === 0
}

/**
 * 格式化时间显示
 * @param {string} 时间字符串 - 时间字符串
 * @returns {string} 格式化后的时间
 */
const 格式化时间 = (时间字符串) => {
  if (!时间字符串) return '-'

  try {
    const date = new Date(时间字符串)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return 时间字符串
  }
}



/**
 * 格式化日期显示
 * @param {string} 日期字符串 - 原始日期字符串
 * @returns {string} 格式化后的日期
 */
const 格式化日期 = (日期字符串) => {
  if (!日期字符串) return '-'
  const 日期 = new Date(日期字符串)
  return `${日期.getFullYear()}-${(日期.getMonth() + 1).toString().padStart(2, '0')}-${日期.getDate().toString().padStart(2, '0')} ${日期.getHours().toString().padStart(2, '0')}:${日期.getMinutes().toString().padStart(2, '0')}`
}

// 组件是否已销毁的标志
const 组件已销毁 = ref(false)

/**
 * 加载样品列表
 */
const 加载样品列表 = async () => {
  if (组件已销毁.value) return

  try {
    加载中.value = true

    // 调用现有的样品列表API
    const response = await sampleService.获取样品列表({
      页码: 分页配置.current || 1,
      每页条数: 分页配置.pageSize || 10,
      收件人: 搜索关键词.value || undefined,  // 使用收件人字段进行搜索
      审核状态: 筛选状态.value !== null ? 筛选状态.value : undefined,
      快递状态: undefined // 可以后续添加快递状态筛选
    })

    // 检查组件是否已销毁
    if (组件已销毁.value) return

    if (response.status === 100) {
      const data = response.data || {}

      // 确保数据结构正确
      const 列表数据 = Array.isArray(data.列表) ? data.列表 : []
      样品列表.value = 列表数据

      // 更新分页信息
      if (data.分页 && typeof data.分页 === 'object') {
        分页配置.total = data.分页.总记录数 || 0
        分页配置.current = data.分页.当前页 || 1
        分页配置.pageSize = data.分页.每页条数 || 10
      }

      console.log('✅ 样品列表加载成功:', {
        样品数量: 样品列表.value.length,
        总数: data.分页?.总记录数
      })
    } else {
      console.warn('⚠️ 样品列表加载失败:', response.message)
      if (!组件已销毁.value) {
        message.error(response.message || '加载样品列表失败')
        样品列表.value = []
      }
    }

  } catch (error) {
    console.error('❌ 加载样品列表失败:', error)
    if (!组件已销毁.value) {
      message.error('加载样品列表失败，请稍后重试')
      样品列表.value = []
    }
  } finally {
    if (!组件已销毁.value) {
      加载中.value = false
    }
  }
}

/**
 * 刷新样品列表
 */
const 刷新样品列表 = async () => {
  try {
    message.loading('正在刷新样品列表...', 0.5)
    await 加载样品列表()
    message.success('样品列表刷新成功')
  } catch (error) {
    console.error('刷新样品列表失败:', error)
    message.error('刷新失败，请稍后重试')
  }
}

/**
 * 加载样品统计数据
 */
const 加载样品统计 = async () => {
  try {
    const response = await sampleService.获取样品统计()

    if (response.status === 100) {
      const stats = response.data || {}
      Object.assign(样品统计, {
        总数: stats.总数 || 0,        // 使用现有API的字段名
        待审核: stats.待审核 || 0,
        已发货: stats.已发货 || 0,
        已送达: stats.已签收 || 0  // 后端返回的是已签收，映射为已送达
      })

      console.log('✅ 样品统计数据加载成功:', stats)
    }
  } catch (error) {
    console.error('❌ 加载样品统计失败:', error)
  }
}

/**
 * 加载产品列表
 */
const 加载产品列表 = async () => {
  try {
    const response = await productService.getProductList({
      页码: 1,
      每页数量: 100  // 获取足够多的产品供选择
    })

    if (response.status === 100) {
      const data = response.data || {}
      产品列表.value = data.列表 || []
      console.log('✅ 产品列表加载成功:', 产品列表.value.length)
    } else {
      console.warn('⚠️ 产品列表加载失败:', response.message)
      产品列表.value = []
    }
  } catch (error) {
    console.error('❌ 加载产品列表失败:', error)
    产品列表.value = []
  }
}

/**
 * 执行搜索功能
 * 根据搜索关键词、状态筛选、日期范围等条件过滤样品列表
 */
const 执行搜索 = () => {
  加载样品列表()
}

/**
 * 显示样品申请弹窗
 */
const 显示样品申请弹窗 = async () => {
  申请弹窗可见.value = true
  // 重置表单数据
  Object.assign(申请表单数据, {
    产品id: null,
    申请人: '',
    申请数量: 1,
    规格: '',
    申请原因: '',
    收货地址: '',
    联系方式: '',
    选中联系人: null,
    选中地址索引: null
  })
  // 清空相关数据
  当前联系人地址选项.value = []
  当前产品规格选项.value = []
  // 加载产品列表和联系人列表
  await Promise.all([
    加载产品列表(),
    加载联系人列表()
  ])
}

/**
 * 加载联系人列表
 */
const 加载联系人列表 = async (关键词 = '') => {
  try {
    const response = await productService.获取样品申请联系人列表({ 关键词 })
    if (response.status === 100) {
      联系人列表.value = response.data || []
    } else {
      console.error('获取联系人列表失败:', response.message)
    }
  } catch (error) {
    console.error('❌ 获取联系人列表失败:', error)
  }
}

/**
 * 搜索联系人
 */
const 搜索联系人 = (关键词) => {
  加载联系人列表(关键词)
}

/**
 * 处理联系人选择变化
 */
const 处理联系人选择变化 = (联系人id) => {
  if (!联系人id || 联系人id === 'manual') {
    // 手动输入模式，清空自动填充的数据
    当前联系人地址选项.value = []
    申请表单数据.选中地址索引 = null
    return
  }

  // 查找选中的联系人
  const 选中联系人 = 联系人列表.value.find(c => c.用户联系人id === 联系人id)
  if (选中联系人 && 选中联系人.地址选项) {
    当前联系人地址选项.value = 选中联系人.地址选项

    // 如果只有一个地址，自动选择
    if (选中联系人.地址选项.length === 1) {
      申请表单数据.选中地址索引 = 0
      处理地址选择变化(0)
    } else {
      申请表单数据.选中地址索引 = null
    }
  }
}

/**
 * 处理地址选择变化
 */
const 处理地址选择变化 = (地址索引) => {
  if (地址索引 !== null && 当前联系人地址选项.value[地址索引]) {
    const 选中地址 = 当前联系人地址选项.value[地址索引]
    申请表单数据.申请人 = 选中地址.收件人
    申请表单数据.收货地址 = 选中地址.地址
    申请表单数据.联系方式 = 选中地址.电话
  }
}

/**
 * 清空联系人选择
 */
const 清空联系人选择 = () => {
  当前联系人地址选项.value = []
  申请表单数据.选中地址索引 = null
  申请表单数据.申请人 = ''
  申请表单数据.收货地址 = ''
  申请表单数据.联系方式 = ''
}

/**
 * 处理产品规格变化
 */
const 处理产品规格变化 = (产品id) => {
  申请表单数据.规格 = '' // 清空之前选择的规格
  申请表单数据.规格数组 = [] // 清空规格数组

  if (!产品id) {
    当前产品规格选项.value = []
    return
  }

  // 查找选中的产品
  const 选中产品 = 产品列表.value.find(p => p.id === 产品id)
  if (选中产品 && 选中产品.产品规格) {
    try {
      // 处理产品规格数据（可能是字符串或已解析的对象）
      let 规格数据 = 选中产品.产品规格
      
      // 如果是字符串，尝试解析JSON
      if (typeof 规格数据 === 'string') {
        规格数据 = JSON.parse(规格数据)
      }
      
      const 规格选项 = []

      // 处理新的字典格式 {规格列表: [...]}
      if (规格数据?.规格列表 && Array.isArray(规格数据.规格列表)) {
        规格选项.push(...规格数据.规格列表)
      }
      // 处理数组格式
      else if (Array.isArray(规格数据)) {
        规格选项.push(...规格数据)
      }
      // 处理对象格式，提取所有值
      else if (typeof 规格数据 === 'object' && 规格数据 !== null) {
        Object.values(规格数据).forEach(value => {
          if (Array.isArray(value)) {
            规格选项.push(...value)
          } else if (typeof value === 'string') {
            规格选项.push(value)
          }
        })
      }

      当前产品规格选项.value = [...new Set(规格选项)] // 去重
      console.log('🔧 产品规格选项更新:', 当前产品规格选项.value)
    } catch (error) {
      console.error('解析产品规格失败:', error, '原始数据:', 选中产品.产品规格)
      当前产品规格选项.value = []
    }
  } else {
    当前产品规格选项.value = []
  }
}

/**
 * 处理规格选择变化
 * 将多选的规格数组转换为用户友好的字符串格式
 */
const 处理规格选择变化 = (selectedSpecs) => {
  console.log('🔄 规格选择变化:', selectedSpecs)

  // 将选中的规格数组转换为逗号分隔的字符串
  if (selectedSpecs && selectedSpecs.length > 0) {
    申请表单数据.规格 = selectedSpecs.join(', ')
  } else {
    申请表单数据.规格 = ''
  }

  console.log('📝 同步到表单的规格数据:', 申请表单数据.规格)
}

/**
 * 关闭申请弹窗
 */
const 关闭申请弹窗 = () => {
  申请弹窗可见.value = false
  申请表单引用.value?.resetFields()
  // 清空相关数据
  当前联系人地址选项.value = []
  当前产品规格选项.value = []
  申请表单数据.规格数组 = [] // 重置规格数组
}

/**
 * 提交样品申请
 */
const 提交样品申请 = async () => {
  try {
    await 申请表单引用.value.validate()

    // 额外验证产品id
    if (!申请表单数据.产品id) {
      message.error('请选择要申请样品的产品')
      return
    }

    console.log('提交样品申请:', 申请表单数据)

    // 构建申请数据
    const 申请数据 = {
      产品id: 申请表单数据.产品id,
      收件人: 申请表单数据.申请人,
      地址: 申请表单数据.收货地址,
      电话: 申请表单数据.联系方式,
      数量: 申请表单数据.申请数量,
      规格: 申请表单数据.规格,
      寄样备注: 申请表单数据.申请原因
    }

    // 如果选择了联系人（非手动输入），添加联系人ID
    if (申请表单数据.选中联系人 && 申请表单数据.选中联系人 !== 'manual') {
      申请数据.用户联系人表id = 申请表单数据.选中联系人
    }

    // 调用样品申请API
    const response = await productService.申请样品(申请数据)

    if (response.status === 100) {
      message.success('样品申请提交成功')
      关闭申请弹窗()
      加载样品列表() // 刷新列表
      加载样品统计() // 刷新统计
    } else {
      message.error(response.message || '申请提交失败')
    }

  } catch (error) {
    console.error('❌ 申请提交失败:', error)
    message.error('申请提交失败，请检查输入信息')
  }
}

/**
 * 查看样品详情
 * @param {Object} 样品记录 - 样品数据记录
 */
const 查看样品详情 = async (样品记录) => {
  try {
    console.log('🔍 查看样品详情:', 样品记录)

    // 获取样品详细信息
    const response = await sampleService.获取样品详情(样品记录.id)

    if (response.status === 100) {
      当前样品详情.value = response.data || 样品记录
      console.log('✅ 获取样品详情成功:', response.data)
    } else {
      // 如果API失败，使用基础信息
      当前样品详情.value = { ...样品记录 }
      console.warn('⚠️ 获取样品详情失败，使用基础信息:', response.message)
    }

    详情弹窗可见.value = true
  } catch (error) {
    console.error('❌ 获取样品详情失败:', error)
    // 如果API失败，使用基础信息
    当前样品详情.value = { ...样品记录 }
    详情弹窗可见.value = true
    message.warning('获取详细信息失败，显示基础信息')
  }
}

/**
 * 审核样品
 * @param {Object} 样品记录 - 样品数据记录
 */
const 审核样品 = (样品记录) => {
  当前审核样品.value = { ...样品记录 }
  审核弹窗可见.value = true

  // 智能选择审核类型：优先选择未审核的类型
  let 默认审核类型 = ''
  if (样品记录.用户审核状态 === 0) {
    默认审核类型 = '用户审核'
  } else if (样品记录.团队审核状态 === 0) {
    默认审核类型 = '团队审核'
  }

  // 重置审核表单
  Object.assign(审核表单数据, {
    审核类型: 默认审核类型,
    审核结果: '',
    审核备注: ''
  })
}

/**
 * 关闭审核弹窗
 */
const 关闭审核弹窗 = () => {
  审核弹窗可见.value = false
  审核表单引用.value?.resetFields()
}

/**
 * 提交审核结果
 */
const 提交审核结果 = async () => {
  try {
    await 审核表单引用.value.validate()

    console.log('提交审核结果:', {
      样品id: 当前审核样品.value.样品id,
      ...审核表单数据
    })

    // 调用样品审核API
    const response = await sampleService.auditSample({
      样品id: 当前审核样品.value.样品id || 当前审核样品.value.id,
      审核状态: 审核表单数据.审核结果 === '通过' ? 1 : (审核表单数据.审核结果 === '拒绝' ? -1 : 0),
      审核类型: 审核表单数据.审核类型  // 使用用户选择的审核类型
    })

    if (response.status === 100) {
      message.success('审核结果提交成功')
      关闭审核弹窗()
      加载样品列表() // 刷新列表
      加载样品统计() // 刷新统计
    } else {
      message.error(response.message || '审核提交失败')
    }

  } catch (error) {
    console.error('❌ 审核提交失败:', error)
    message.error('审核提交失败，请检查输入信息')
  }
}

/**
 * 发货样品
 * @param {Object} 样品记录 - 样品数据记录
 */
const 发货样品 = async (样品记录) => {
  try {
    // 这里可以打开发货弹窗或直接处理
    const response = await sampleService.updateExpress({
      样品id: 样品记录.样品id || 样品记录.id,
      快递状态: 1, // 已发货
      快递状态变更时间: new Date().toISOString()
    })

    if (response.status === 100) {
      message.success('发货成功')
      加载样品列表() // 刷新列表
      加载样品统计() // 刷新统计
    } else {
      message.error(response.message || '发货失败')
    }
  } catch (error) {
    console.error('❌ 发货失败:', error)
    message.error('发货失败，请稍后重试')
  }
}

/**
 * 加载快递查询配额信息
 */
const 加载快递查询配额 = async () => {
  try {
    const response = await sampleService.getExpressQueryQuota()

    if (response && response.status === 100) {
      Object.assign(快递查询配额, response.data)
      console.log('✅ 快递查询配额信息加载成功:', response.data)
    } else {
      console.warn('⚠️ 获取快递查询配额信息失败:', response)
    }
  } catch (error) {
    console.error('❌ 加载快递查询配额信息失败:', error)
    // 不显示错误提示，避免影响用户体验
  }
}

/**
 * 更新物流信息
 * @param {Object} 样品记录 - 样品数据记录
 */
const 更新物流 = async (样品记录) => {
  try {
    if (!样品记录.快递单号) {
      message.error('该样品暂无快递单号')
      return
    }

    // 检查快递查询次数限制
    if (!快递查询配额.是否可查询) {
      message.error(`快递查询次数已达上限（${快递查询配额.每日限额}次），请明日再试`)
      return
    }

    // 查询物流信息
    const response = await sampleService.queryLogistics(样品记录.快递单号)

    if (response.status === 100) {
      message.success('物流信息更新成功')
      加载样品列表() // 刷新列表
      加载快递查询配额() // 刷新配额信息
    } else {
      // 检查是否是次数限制错误
      if (response.status === 429 || response.message?.includes('查询次数')) {
        message.error(response.message || '快递查询次数已达上限')
        加载快递查询配额() // 刷新配额信息
      } else {
        message.error(response.message || '物流信息更新失败')
      }
    }
  } catch (error) {
    console.error('❌ 物流信息更新失败:', error)

    // 检查是否是次数限制错误
    if (error.response?.data?.message?.includes('查询次数')) {
      message.error(error.response.data.message)
      加载快递查询配额() // 刷新配额信息
    } else {
      message.error('物流信息更新失败，请稍后重试')
    }
  }
}

/**
 * 导出样品数据
 */
const 导出样品数据 = async () => {
  try {
    const response = await sampleService.exportSamples({
      筛选条件: {
        收件人: 搜索关键词.value,
        审核状态: 筛选状态.value
      }
    })

    if (response.status === 100) {
      // 处理文件下载
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `样品数据_${new Date().toISOString().slice(0, 10)}.xlsx`
      link.click()
      window.URL.revokeObjectURL(url)

      message.success('导出成功')
    } else {
      message.error(response.message || '导出失败')
    }
  } catch (error) {
    console.error('❌ 导出样品数据失败:', error)
    message.error('导出失败，请稍后重试')
  }
}

/**
 * 处理表格变化（分页、排序等）
 * @param {Object} 分页信息 - 包含当前页、页大小等信息
 */
const 处理表格变化 = (分页信息) => {
  Object.assign(分页配置, 分页信息)
  加载样品列表()
}



/**
 * 组件挂载时初始化数据
 */
onMounted(() => {
  加载样品列表()
  加载样品统计()
  加载快递查询配额()
})

// 组件销毁前的清理
onBeforeUnmount(() => {
  组件已销毁.value = true
})
</script>

<style scoped>
/* 样品管理模块整体样式 */
.sample-management-module {
  padding: 0;
}

/* 统计卡片行样式 */
.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 操作卡片样式 */
.action-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-filters {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* 快递查询配额信息样式 */
.quota-info {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.quota-info .ant-tag {
  margin: 0;
  font-size: 13px;
  font-weight: 500;
}

/* 列表卡片样式 */
.list-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 产品图片样式 */
.product-image {
  border-radius: 4px;
  object-fit: cover;
}

/* 样品详情样式 */
.sample-detail {
  max-height: 600px;
  overflow-y: auto;
}

.audit-history {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.audit-history h4 {
  margin-bottom: 16px;
  color: #1d1d1d;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-filters .ant-input-search,
  .search-filters .ant-select,
  .search-filters .ant-picker {
    width: 100% !important;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  /* 移动端表格滚动 */
  .list-card :deep(.ant-table-wrapper) {
    overflow-x: auto;
  }
}

@media (max-width: 480px) {
  .sample-management-module {
    padding: 0 8px;
  }
  
  .stats-row .ant-col {
    margin-bottom: 12px;
  }
  
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .action-buttons .ant-btn {
    width: 100%;
  }
}

/* 表格样式优化 */
.list-card :deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

.list-card :deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

/* 标签样式优化 */
.list-card :deep(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 按钮样式优化 */
.action-buttons .ant-btn,
.list-card .ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

/* 弹窗样式优化 */
.ant-modal :deep(.ant-modal-header) {
  border-radius: 8px 8px 0 0;
}

.ant-modal :deep(.ant-modal-content) {
  border-radius: 8px;
}

/* 产品规格选择样式 - 与达人管理页面保持一致 */
.spec-selection-container {
  width: 100%;
}

.spec-options {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
}

.spec-description {
  margin-bottom: 12px;
}

.spec-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.spec-checkbox {
  margin: 0;
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  transition: all 0.2s;
}

.spec-checkbox:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.spec-checkbox.ant-checkbox-wrapper-checked {
  border-color: #1890ff;
  background: #e6f7ff;
}

.spec-checkbox.ant-checkbox-wrapper-checked:hover {
  border-color: #40a9ff;
  background: #bae7ff;
}

.no-specs-hint {
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
  text-align: center;
}
</style>