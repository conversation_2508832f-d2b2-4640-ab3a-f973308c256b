<template>
  <div class="talent-distribution-chart">
    <div 
      ref="chartContainer" 
      :style="{ height: height }"
      class="chart-container"
    ></div>
    
    <div v-if="loading" class="chart-loading">
      <a-spin size="large" />
    </div>
    
    <div v-if="!loading && (!data || data.length === 0)" class="chart-empty">
      <a-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import gvmService from '../../services/gvmService'

defineOptions({
  name: 'TalentDistributionChart'
})

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  height: {
    type: String,
    default: '300px'
  },
  loading: {
    type: Boolean,
    default: false
  },
  chartType: {
    type: String,
    default: 'pie', // 'pie' | 'doughnut'
    validator: (value) => ['pie', 'doughnut'].includes(value)
  }
})

const emit = defineEmits(['categoryClick'])

const chartContainer = ref(null)
let chartInstance = null

const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  updateChart()
  
  chartInstance.on('click', (params) => {
    emit('categoryClick', params.data)
  })
}

const updateChart = () => {
  if (!chartInstance || props.loading) return
  
  const colors = gvmService.getChartColors()
  const option = getChartOption(colors)
  
  chartInstance.setOption(option, true)
}

const getChartOption = (colors) => {
  if (!props.data || props.data.length === 0) {
    return {}
  }
  
  // 处理数据
  const pieData = props.data.map((item, index) => ({
    name: item.category || `分类${index + 1}`,
    value: item.count || 0,
    itemStyle: {
      color: colors.series[index % colors.series.length]
    }
  }))
  
  const total = pieData.reduce((sum, item) => sum + item.value, 0)
  
  return {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const percentage = ((params.value / total) * 100).toFixed(1)
        return `
          <div style="margin-bottom: 4px; font-weight: 600;">${params.name}</div>
          <div style="display: flex; align-items: center; margin-bottom: 2px;">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
            <span>数量: ${params.value}人</span>
          </div>
          <div style="font-size: 12px; color: #666;">
            占比: ${percentage}%
          </div>
        `
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'center',
      textStyle: {
        fontSize: 12
      },
      formatter: (name) => {
        const item = pieData.find(d => d.name === name)
        return `${name} (${item ? item.value : 0})`
      }
    },
    series: [
      {
        name: '达人分布',
        type: 'pie',
        radius: props.chartType === 'doughnut' ? ['40%', '70%'] : '70%',
        center: ['60%', '50%'],
        data: pieData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          position: 'outside',
          formatter: (params) => {
            const percentage = ((params.value / total) * 100).toFixed(1)
            return `${percentage}%`
          },
          fontSize: 12
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10
        },
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 2
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: (idx) => idx * 100
      }
    ],
    animation: true
  }
}

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

watch(() => props.data, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

watch(() => props.chartType, () => {
  nextTick(() => {
    updateChart()
  })
})

watch(() => props.loading, (newLoading) => {
  if (!newLoading) {
    nextTick(() => {
      updateChart()
    })
  }
})

onMounted(() => {
  nextTick(() => {
    initChart()
  })
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})

defineExpose({
  refreshChart: updateChart,
  getChartInstance: () => chartInstance
})
</script>

<style scoped>
.talent-distribution-chart {
  position: relative;
  width: 100%;
}

.chart-container {
  width: 100%;
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.chart-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
</style>
