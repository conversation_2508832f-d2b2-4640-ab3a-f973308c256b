<template>
  <a-modal
    v-model:open="modalVisible"
    title="智能体详情"
    width="800px"
    :footer="null"
    class="agent-detail-modal"
  >
    <div v-if="agent" class="agent-detail-content">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <a-avatar :size="32" style="margin-right: 8px; background-color: #1890ff;">
            {{ agent.智能体名称?.charAt(0) || 'A' }}
          </a-avatar>
          {{ agent.智能体名称 }}
          <div class="detail-tags">
            <a-tag :color="agent.是否公开 ? 'green' : 'blue'" size="small">
              {{ agent.是否公开 ? '公开' : '定制' }}
            </a-tag>
            <a-tag :color="getStatusColor(agent.状态)" size="small">
              {{ getStatusLabel(agent.状态) }}
            </a-tag>
          </div>
        </h3>
      </div>

      <!-- 描述信息 -->
      <div class="detail-section">
        <h4>智能体描述</h4>
        <p class="description-text">
          {{ agent.智能体描述 || '暂无描述信息' }}
        </p>
      </div>

      <!-- 配置信息 -->
      <div class="detail-section">
        <h4>配置信息</h4>
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="智能体ID">
            {{ agent.智能体id }}
          </a-descriptions-item>
          <a-descriptions-item label="使用模型">
            {{ agent.模型名称 || '未指定' }}
          </a-descriptions-item>
          <a-descriptions-item label="运行状态">
            <a-tag :color="getStatusColor(agent.状态)" size="small">
              {{ getStatusLabel(agent.状态) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="访问权限">
            <a-tag :color="agent.是否公开 ? 'green' : 'blue'" size="small">
              {{ agent.是否公开 ? '公开智能体' : '私有智能体' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDate(agent.创建时间) }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 操作按钮 -->
      <div class="detail-actions">
        <a-button type="primary" size="large" @click="handleUse">
          <RobotOutlined />
          使用此智能体
        </a-button>
        <a-button size="large" @click="handleEdit" style="margin-left: 12px;">
          编辑
        </a-button>
        <a-button size="large" @click="modalVisible = false" style="margin-left: 12px;">
          关闭
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { computed } from 'vue'
import { RobotOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  agent: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'edit', 'use'])

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const handleEdit = (event) => {
  event?.stopPropagation?.()
  emit('edit', props.agent)
  modalVisible.value = false
}

const handleUse = (event) => {
  event?.stopPropagation?.()
  emit('use', props.agent)
  modalVisible.value = false
}

const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  try {
    return new Date(dateStr).toLocaleString('zh-CN')
  } catch {
    return '未知'
  }
}

const getStatusLabel = (status) => {
  const statusMap = {
    'running': '运行中',
    'stopped': '已停止',
    'error': '异常',
    'pending': '待启动'
  }
  return statusMap[status] || '未知'
}

const getStatusColor = (status) => {
  const colorMap = {
    'running': 'success',
    'stopped': 'default',
    'error': 'error',
    'pending': 'processing'
  }
  return colorMap[status] || 'default'
}
</script>

<style scoped>
.agent-detail-modal :deep(.ant-modal-content) {
  border-radius: 12px;
}

.agent-detail-modal :deep(.ant-modal-header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
  padding: 20px 24px;
}

.agent-detail-modal :deep(.ant-modal-title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.agent-detail-content {
  padding: 8px 0;
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
}

.detail-tags {
  display: flex;
  gap: 4px;
}

.detail-section h4 {
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.description-text {
  color: #595959;
  line-height: 1.6;
  background: #fafafa;
  padding: 12px;
  border-radius: 6px;
  margin: 0;
}

.detail-actions {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .detail-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .detail-actions .ant-btn {
    width: 100%;
  }
}
</style>
