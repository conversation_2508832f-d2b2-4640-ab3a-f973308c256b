"""
用户联系人服务层 - 业务逻辑层
负责处理用户联系人相关的业务逻辑，调用数据访问层进行数据操作
基于三层分离架构设计
"""

from typing import Any, Dict, List, Optional
from uuid import UUID

from 数据.用户联系人数据访问层 import 用户联系人数据访问实例, 达人补充信息数据访问实例
from 服务.基础服务 import 数据服务基类


class 用户联系人服务(数据服务基类):
    """用户联系人业务服务"""

    def __init__(self):
        super().__init__("用户联系人服务", 用户联系人数据访问实例)
        self.用户联系人数据访问 = 用户联系人数据访问实例
        self.达人补充信息数据访问 = 达人补充信息数据访问实例

    def 清理寄样信息列表(self, 寄样信息: Optional[List]) -> Optional[List]:
        """
        清理寄样信息列表的公共方法

        Args:
            寄样信息: 原始寄样信息列表

        Returns:
            清理后的寄样信息列表
        """
        if 寄样信息 is None:
            return None

        清理后的寄样信息 = []
        for 信息 in 寄样信息:
            if isinstance(信息, dict):
                清理后的信息 = {
                    "收件人": self.清理字符串参数(信息.get("收件人", "")).strip(),
                    "地址": self.清理字符串参数(信息.get("地址", "")).strip(),
                    "电话": self.清理字符串参数(信息.get("电话", "")).strip(),
                }
                # 验证必填字段
                if (
                    清理后的信息["收件人"]
                    and 清理后的信息["地址"]
                    and 清理后的信息["电话"]
                ):
                    清理后的寄样信息.append(清理后的信息)

        return 清理后的寄样信息 if 清理后的寄样信息 else None

    async def 查询用户联系人列表(
        self, 用户id: int, 关键词: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        查询用户的联系人列表

        参数:
            用户id: 用户id
            关键词: 搜索关键词（姓名或联系方式）

        返回:
            联系人列表
        """
        # 参数验证
        if not 用户id or 用户id <= 0:
            return self.构建失败响应("用户id无效")

        try:
            # 调用数据访问层查询联系人列表
            结果 = await self.用户联系人数据访问.查询用户联系人_通过用户id(
                用户id, 关键词
            )

            return self.构建成功响应(结果, "查询联系人列表成功")

        except Exception as e:
            self.记录错误("查询联系人列表失败", e, 用户id=用户id)
            return self.构建失败响应(f"查询联系人列表失败: {str(e)}")

    async def 查询单个联系人(self, 联系人id: UUID, 用户id: int) -> Dict[str, Any]:
        """
        查询单个联系人详情

        参数:
            联系人id: 联系人UUID
            用户id: 用户id（用于权限验证）

        返回:
            联系人详情
        """
        # 参数验证
        if not 联系人id:
            return self.构建失败响应("联系人ID无效")
        if not 用户id or 用户id <= 0:
            return self.构建失败响应("用户id无效")

        try:
            # 查询联系人信息（包含关联的联系方式信息）
            联系人信息 = await self.用户联系人数据访问.查询用户联系人_包含联系方式信息(
                联系人id
            )

            if not 联系人信息:
                return self.构建失败响应("联系人不存在")

            # 验证联系人是否属于当前用户
            if 联系人信息["用户表id"] != 用户id:
                return self.构建失败响应("无权限访问此联系人")

            return self.构建成功响应(联系人信息, "查询联系人详情成功")

        except Exception as e:
            self.记录错误("查询联系人详情失败", e, 联系人id=联系人id, 用户id=用户id)
            return self.构建失败响应(f"查询联系人详情失败: {str(e)}")

    async def 更新联系人(
        self,
        联系人id: UUID,
        用户id: int,
        姓名: Optional[str] = None,
        寄样信息: Optional[List] = None,
    ) -> Dict[str, Any]:
        """
        更新联系人信息

        参数:
            联系人id: 联系人UUID
            用户id: 用户id（用于权限验证）
            姓名: 新的姓名（可选）
            寄样信息: 新的寄样信息列表（可选）

        返回:
            更新结果
        """
        # 参数验证
        if not 联系人id:
            return self.构建失败响应("联系人ID无效")
        if not 用户id or 用户id <= 0:
            return self.构建失败响应("用户id无效")

        # 检查是否有要更新的字段
        if 姓名 is None and 寄样信息 is None:
            return self.构建失败响应("请提供要更新的字段")

        try:
            # 首先验证联系人是否存在且属于当前用户
            联系人信息 = await self.用户联系人数据访问.查询用户联系人_通过ID(联系人id)

            if not 联系人信息:
                return self.构建失败响应("联系人不存在")

            if 联系人信息["用户表id"] != 用户id:
                return self.构建失败响应("无权限修改此联系人")

            # 清理字符串参数
            清理后的姓名 = None
            if 姓名 is not None:
                清理后的姓名 = self.清理字符串参数(姓名)
                if 清理后的姓名 == "":
                    return self.构建失败响应("联系人姓名不能为空")

            # 处理寄样信息列表
            清理后的寄样信息 = self.清理寄样信息列表(寄样信息)

            # 执行更新
            更新成功 = await self.用户联系人数据访问.更新用户联系人(
                联系人id, 清理后的姓名, 清理后的寄样信息
            )

            if 更新成功:
                self.记录信息("更新联系人成功", 联系人id=联系人id, 用户id=用户id)
                return self.构建成功响应(None, "更新联系人成功")
            else:
                return self.构建失败响应("更新联系人失败")

        except Exception as e:
            self.记录错误("更新联系人失败", e, 联系人id=联系人id, 用户id=用户id)
            return self.构建失败响应(f"更新联系人失败: {str(e)}")

    async def 更新联系人和联系方式(
        self,
        联系人id: UUID,
        用户id: int,
        姓名: Optional[str] = None,
        寄样信息: Optional[List] = None,
        联系方式信息: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """
        更新联系人信息和关联的联系方式信息

        参数:
            联系人id: 联系人UUID
            用户id: 用户id（用于权限验证）
            姓名: 新的姓名（可选）
            寄样信息: 新的寄样信息列表（可选）
            联系方式信息: 联系方式信息字典（可选）

        返回:
            更新结果
        """
        # 参数验证
        if not 联系人id:
            return self.构建失败响应("联系人ID无效")
        if not 用户id or 用户id <= 0:
            return self.构建失败响应("用户id无效")

        # 检查是否有要更新的字段
        if 姓名 is None and 寄样信息 is None and 联系方式信息 is None:
            return self.构建失败响应("请提供要更新的字段")

        try:
            # 首先验证联系人是否存在且属于当前用户
            联系人信息 = await self.用户联系人数据访问.查询用户联系人_包含联系方式信息(
                联系人id
            )

            if not 联系人信息:
                return self.构建失败响应("联系人不存在")

            if 联系人信息["用户表id"] != 用户id:
                return self.构建失败响应("无权限修改此联系人")

            # 更新联系人基本信息
            if 姓名 is not None or 寄样信息 is not None:
                清理后的姓名 = None
                if 姓名 is not None:
                    清理后的姓名 = self.清理字符串参数(姓名)
                    if 清理后的姓名 == "":
                        return self.构建失败响应("联系人姓名不能为空")

                清理后的寄样信息 = self.清理寄样信息列表(寄样信息)

                更新成功 = await self.用户联系人数据访问.更新用户联系人(
                    联系人id, 清理后的姓名, 清理后的寄样信息
                )

                if not 更新成功:
                    return self.构建失败响应("更新联系人基本信息失败")

            # 更新联系方式信息
            if 联系方式信息 is not None:
                关联信息 = 联系人信息.get("关联联系方式信息")
                if 关联信息 and 关联信息.get("补充信息id"):
                    # 更新现有的联系方式信息
                    from 数据.达人补充信息数据访问层 import 达人补充信息数据访问

                    更新成功 = await 达人补充信息数据访问.更新补充联系方式(
                        关联信息["补充信息id"],
                        联系方式=联系方式信息.get("联系方式"),
                        联系方式类型=联系方式信息.get("联系方式类型"),
                        个人备注=联系方式信息.get("个人备注"),
                        个人标签=联系方式信息.get("个人标签"),
                        补充信息=联系方式信息.get("补充信息"),
                    )

                    if not 更新成功:
                        return self.构建失败响应("更新联系方式信息失败")
                else:
                    # 没有关联联系方式信息，创建新的联系方式信息
                    if 联系方式信息.get("联系方式") and 联系方式信息.get(
                        "联系方式类型"
                    ):
                        from 数据.联系方式数据访问层 import 联系方式数据访问
                        from 数据.达人补充信息数据访问层 import 达人补充信息数据访问

                        # 先创建或查找联系方式表记录
                        联系方式表id = await 联系方式数据访问.创建或获取联系方式(
                            联系方式信息.get("联系方式"),
                            联系方式信息.get("联系方式类型"),
                        )

                        if not 联系方式表id:
                            return self.构建失败响应("创建联系方式记录失败")

                        # 创建新的补充信息记录
                        补充信息id = await 达人补充信息数据访问.添加补充联系方式(
                            用户达人关联表id=None,  # 暂时不关联达人
                            联系方式=联系方式信息.get("联系方式"),
                            联系方式类型=联系方式信息.get("联系方式类型"),
                            联系方式表id=联系方式表id,
                            个人备注=联系方式信息.get("个人备注"),
                            个人标签=联系方式信息.get("个人标签"),
                            补充信息=联系方式信息.get("补充信息"),
                        )

                        if 补充信息id:
                            # 关联到联系人
                            关联成功 = await 达人补充信息数据访问.更新补充联系方式(
                                补充信息id, 用户联系人表id=联系人id
                            )

                            if not 关联成功:
                                return self.构建失败响应("关联联系方式信息失败")
                        else:
                            return self.构建失败响应("创建联系方式信息失败")

            self.记录信息("更新联系人和联系方式成功", 联系人id=联系人id, 用户id=用户id)
            return self.构建成功响应(None, "更新联系人和联系方式成功")

        except Exception as e:
            self.记录错误(
                "更新联系人和联系方式失败", e, 联系人id=联系人id, 用户id=用户id
            )
            return self.构建失败响应(f"更新联系人和联系方式失败: {str(e)}")

    async def _创建联系人并关联已有达人信息(
        self, 用户id: int, 姓名: str, 寄样信息: Optional[List], 补充信息id: int
    ) -> Dict[str, Any]:
        """
        创建联系人并关联到已有达人补充信息
        """
        try:
            # 1. 验证补充信息是否属于当前用户
            from 数据.达人补充信息数据访问层 import 达人补充信息数据访问

            关联记录 = await 达人补充信息数据访问.查询关联记录_通过补充信息id(
                补充信息id
            )
            if not 关联记录 or 关联记录.get("用户id") != 用户id:
                return self.构建失败响应("补充信息不存在或无权限访问")

            # 2. 清理寄样信息并创建联系人
            清理后的寄样信息 = self.清理寄样信息列表(寄样信息)
            联系人数据 = await self.用户联系人数据访问.创建用户联系人(
                用户id, 姓名, 清理后的寄样信息
            )

            if not 联系人数据:
                return self.构建失败响应("创建联系人失败")

            # 3. 关联到达人补充信息
            联系人id = 联系人数据["用户联系人id"]
            关联成功 = await 达人补充信息数据访问.关联用户联系人(补充信息id, 联系人id)

            if not 关联成功:
                # 如果关联失败，需要删除已创建的联系人（回滚）
                await self.用户联系人数据访问.删除用户联系人(联系人id)
                return self.构建失败响应("关联达人信息失败")

            self.记录信息(
                "创建联系人并关联已有达人信息成功",
                联系人id=联系人id,
                补充信息id=补充信息id,
            )
            return self.构建成功响应(联系人数据, "创建联系人并关联成功")

        except Exception as e:
            self.记录错误("创建联系人并关联已有达人信息失败", e)
            return self.构建失败响应(f"创建联系人并关联已有达人信息失败: {str(e)}")

    async def 创建联系人并关联达人补充信息(
        self, 用户id: int, 请求数据: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        创建联系人并关联达人补充信息（完整业务流程）

        核心业务逻辑：
        1. 创建联系人数据到联系人表
        2. 检查联系方式表中是否已存在相同的联系方式+类型组合
        3. 如果存在：获取该记录的ID，保存到用户达人补充信息表的联系方式表ID字段
        4. 如果不存在：创建新的联系方式记录，然后保存其ID到用户达人补充信息表
        5. 创建用户达人补充信息表数据
        6. 通过用户联系人表ID将上述两个表关联起来

        参数:
            用户id: 用户ID
            请求数据: 包含联系人和达人信息的请求数据

        返回:
            创建结果
        """
        try:
            # 参数验证 - 兼容前端数据结构
            姓名 = 请求数据.get("姓名")
            寄样信息 = 请求数据.get("寄样信息")
            关联方式 = 请求数据.get("关联方式")
            达人联系方式信息 = 请求数据.get("达人联系方式信息", {})

            # 从达人联系方式信息中提取字段
            联系方式 = 达人联系方式信息.get("联系方式")
            联系方式类型 = 达人联系方式信息.get("联系方式类型")
            个人备注 = 达人联系方式信息.get("个人备注")
            个人标签 = 达人联系方式信息.get("个人标签")
            补充信息 = 达人联系方式信息.get("补充信息")

            # 必需参数验证
            if not 姓名 or not 姓名.strip():
                return self.构建失败响应("联系人姓名不能为空")

            # 根据关联方式决定验证逻辑
            if 关联方式 == "existing":
                # 选择已有达人联系方式模式
                补充信息id = 请求数据.get("补充信息id")
                if not 补充信息id:
                    return self.构建失败响应("请选择要关联的达人信息")

                # 调用关联已有达人信息的方法
                return await self._创建联系人并关联已有达人信息(
                    用户id, 姓名.strip(), 寄样信息, 补充信息id
                )

            elif 关联方式 == "create":
                # 创建新的达人联系方式模式
                if not 联系方式 or not 联系方式.strip():
                    return self.构建失败响应("联系方式不能为空")

                if not 联系方式类型 or not 联系方式类型.strip():
                    return self.构建失败响应("联系方式类型不能为空")

                # 继续执行创建新达人联系方式的逻辑
                pass

            else:
                return self.构建失败响应("无效的关联方式")

            # 只有创建新达人联系方式模式才继续执行下面的逻辑
            if 关联方式 != "create":
                return self.构建失败响应("内部逻辑错误：不应该到达这里")

            # 清理参数
            清理后的姓名 = self.清理字符串参数(姓名)
            清理后的联系方式 = self.清理字符串参数(联系方式)
            清理后的联系方式类型 = self.清理字符串参数(联系方式类型)
            清理后的个人备注 = self.清理字符串参数(个人备注) if 个人备注 else None
            清理后的补充信息 = self.清理字符串参数(补充信息) if 补充信息 else None
            清理后的寄样信息 = self.清理寄样信息列表(寄样信息)

            # 处理个人标签
            清理后的个人标签 = []
            if 个人标签 and isinstance(个人标签, list):
                清理后的个人标签 = [
                    self.清理字符串参数(标签)
                    for 标签 in 个人标签
                    if 标签 and str(标签).strip()
                ]

            # 步骤1: 创建联系人
            联系人数据 = await self.用户联系人数据访问.创建用户联系人(
                用户id, 清理后的姓名, 清理后的寄样信息
            )

            if not 联系人数据:
                return self.构建失败响应("创建联系人失败")

            联系人id = 联系人数据["用户联系人id"]

            try:
                # 步骤2: 检查或创建联系方式记录
                from 数据.线索数据操作 import 获取或创建联系方式并返回完整数据

                联系方式数据 = await 获取或创建联系方式并返回完整数据(
                    内容=清理后的联系方式,
                    类型=清理后的联系方式类型,
                    记录来源="用户创建联系人",
                )

                if not 联系方式数据:
                    # 回滚：删除已创建的联系人
                    await self.用户联系人数据访问.删除用户联系人(联系人id)
                    return self.构建失败响应("创建或获取联系方式失败")

                联系方式表id = 联系方式数据["id"]

                # 步骤3: 创建用户达人关联表记录
                from 数据.联系方式数据访问层 import 用户达人关联数据访问

                关联表id = await 用户达人关联数据访问.创建用户达人关联(
                    用户id=用户id,
                    达人id=None,  # 暂时为空，后续可以通过关联达人接口补充
                    平台="未知",
                    平台账号=清理后的联系方式,
                    备注="通过联系人创建",
                )

                # 步骤4: 创建用户达人补充信息记录
                from 数据.达人补充信息数据访问层 import 达人补充信息数据访问

                补充信息id = await 达人补充信息数据访问.添加补充联系方式(
                    用户达人关联表id=关联表id,
                    联系方式=清理后的联系方式,
                    联系方式类型=清理后的联系方式类型,
                    联系方式表id=联系方式表id,
                    个人备注=清理后的个人备注,
                    个人标签=清理后的个人标签,
                    补充信息=清理后的补充信息,
                )

                if not 补充信息id:
                    # 回滚：删除已创建的记录
                    await self.用户联系人数据访问.删除用户联系人(联系人id)
                    from 数据.Postgre_异步连接池 import (
                        Postgre_异步连接池实例 as 异步连接池实例,
                    )

                    await 异步连接池实例.执行更新(
                        "DELETE FROM 用户达人关联表 WHERE id = $1", (关联表id,)
                    )
                    return self.构建失败响应("创建达人补充信息失败")

                # 步骤5: 关联联系人到达人补充信息
                关联成功 = await 达人补充信息数据访问.关联用户联系人(
                    补充信息id, 联系人id
                )

                if not 关联成功:
                    # 回滚：删除所有已创建的记录
                    await self.用户联系人数据访问.删除用户联系人(联系人id)
                    from 数据.Postgre_异步连接池 import (
                        Postgre_异步连接池实例 as 异步连接池实例,
                    )

                    await 异步连接池实例.执行更新(
                        "DELETE FROM 用户达人补充信息表 WHERE id = $1", (补充信息id,)
                    )
                    await 异步连接池实例.执行更新(
                        "DELETE FROM 用户达人关联表 WHERE id = $1", (关联表id,)
                    )
                    return self.构建失败响应("关联联系人到达人补充信息失败")

                # 构建返回数据
                返回数据 = {
                    "用户联系人id": str(联系人id),
                    "姓名": 联系人数据["姓名"],
                    "补充信息id": 补充信息id,
                    "关联表id": 关联表id,
                    "联系方式表id": 联系方式表id,
                    "联系方式": 清理后的联系方式,
                    "联系方式类型": 清理后的联系方式类型,
                }

                self.记录信息(
                    "创建联系人并关联达人补充信息成功",
                    联系人id=联系人id,
                    补充信息id=补充信息id,
                    关联表id=关联表id,
                )

                return self.构建成功响应(返回数据, "创建联系人并关联达人补充信息成功")

            except Exception as e:
                # 发生异常时回滚联系人
                await self.用户联系人数据访问.删除用户联系人(联系人id)
                raise e

        except Exception as e:
            self.记录错误("创建联系人并关联达人补充信息失败", str(e))
            return self.构建失败响应(f"创建联系人并关联达人补充信息失败: {str(e)}")

    async def 搜索达人补充信息_用于关联(
        self,
        用户id: int,
        关键词: Optional[str] = None,
        页码: int = 1,
        每页数量: int = 20,
    ) -> Dict[str, Any]:
        """
        搜索达人补充信息用于联系人关联

        参数:
            用户id: 用户id
            关键词: 搜索关键词
            页码: 页码
            每页数量: 每页数量

        返回:
            搜索结果
        """
        try:
            from 数据.达人补充信息数据访问层 import 达人补充信息数据访问

            结果 = await 达人补充信息数据访问.搜索达人补充信息_用于关联(
                用户id, 关键词, 页码, 每页数量
            )

            self.记录信息(
                "搜索达人补充信息成功", 用户id=用户id, 总数=结果.get("总数", 0)
            )
            return self.构建成功响应(结果, "搜索成功")

        except Exception as e:
            self.记录错误("搜索达人补充信息失败", e, 用户id=用户id)
            return self.构建失败响应(f"搜索失败: {str(e)}")

    async def 删除联系人(self, 联系人id: UUID, 用户id: int) -> Dict[str, Any]:
        """
        删除联系人

        参数:
            联系人id: 联系人UUID
            用户id: 用户id（用于权限验证）

        返回:
            删除结果
        """
        # 参数验证
        if not 联系人id:
            return self.构建失败响应("联系人ID无效")
        if not 用户id or 用户id <= 0:
            return self.构建失败响应("用户id无效")

        try:
            # 首先验证联系人是否存在且属于当前用户
            联系人信息 = await self.用户联系人数据访问.查询用户联系人_通过ID(联系人id)

            if not 联系人信息:
                return self.构建失败响应("联系人不存在")

            if 联系人信息["用户表id"] != 用户id:
                return self.构建失败响应("无权限删除此联系人")

            # 执行删除
            删除成功 = await self.用户联系人数据访问.删除用户联系人(联系人id)

            if 删除成功:
                self.记录信息("删除联系人成功", 联系人id=联系人id, 用户id=用户id)
                return self.构建成功响应(None, "删除联系人成功")
            else:
                return self.构建失败响应("删除联系人失败")

        except Exception as e:
            self.记录错误("删除联系人失败", e, 联系人id=联系人id, 用户id=用户id)
            return self.构建失败响应(f"删除联系人失败: {str(e)}")


# 创建服务实例
用户联系人服务实例 = 用户联系人服务()
