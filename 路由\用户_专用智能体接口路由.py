"""
用户端 - 聊天内容分析助手专用接口

功能：
1. 为聊天内容分析提供简化的专用接口
2. 隐藏复杂的自定义变量配置
3. 提供业务友好的参数命名
4. 直接使用已有智能体服务
"""

from datetime import datetime
from enum import Enum
from typing import Literal, Optional

from fastapi import APIRouter, Body, Depends
from pydantic import BaseModel, Field, validator

from 依赖项.认证 import 获取当前用户
from 数据模型.响应模型 import 统一响应模型
from 日志 import 应用日志器 as 专用智能体路由日志器
from 服务.LangChain_智能体服务 import LangChain智能体服务实例

# 创建路由器
用户专用智能体接口路由 = APIRouter(tags=["用户-专用智能体接口"])

# 聊天内容分析助手智能体id - 使用数据库中的公开智能体
聊天内容分析助手_智能体id = 1


class 聊天分析维度枚举(str, Enum):
    """聊天分析维度枚举"""

    情感分析 = "情感分析"
    意图识别 = "意图识别"
    关键词提取 = "关键词提取"
    话题分类 = "话题分类"
    客户满意度 = "客户满意度"


class 聊天内容分析请求模型(BaseModel):
    """聊天内容分析请求模型"""

    聊天内容: str = Field(
        ..., description="要分析的聊天内容", min_length=1, max_length=10000
    )
    分析维度: 聊天分析维度枚举 = Field(
        聊天分析维度枚举.情感分析, description="分析维度"
    )
    聊天记录: Optional[str] = Field(
        None, description="相关的聊天记录上下文", max_length=5000
    )
    上下文信息: Optional[str] = Field(
        None, description="额外的上下文信息", max_length=2000
    )
    输出详细程度: Literal["简要", "详细", "完整"] = Field(
        "详细", description="输出详细程度"
    )

    @validator("聊天内容")
    def 验证聊天内容(cls, v):
        if not v or not v.strip():
            raise ValueError("聊天内容不能为空")
        return v.strip()


@用户专用智能体接口路由.post("/chat-content-analyzer", summary="聊天内容分析助手")
async def 聊天内容分析助手接口(
    请求数据: 聊天内容分析请求模型 = Body(...), 用户: dict = Depends(获取当前用户)
):
    """
    聊天内容分析专用接口

    功能：
    - 情感分析：分析聊天内容的情感倾向
    - 意图识别：识别用户的真实意图
    - 关键词提取：提取重要关键词
    - 话题分类：对聊天内容进行话题分类
    - 客户满意度：评估客户满意度
    """
    try:
        专用智能体路由日志器.info(
            f"🔍 聊天内容分析请求 - 用户id: {用户['id']}, 分析维度: {请求数据.分析维度}"
        )

        # 构建自定义变量
        自定义变量 = {
            "chat_content": 请求数据.聊天内容,
            "analysis_dimension": 请求数据.分析维度,
            "chat_history": 请求数据.聊天记录 or "",
            "context_info": 请求数据.上下文信息 or "",
            "detail_level": 请求数据.输出详细程度,
            "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "analysis_type": "chat_content_analysis",
        }

        专用智能体路由日志器.info(f"🔧 传入自定义变量: {list(自定义变量.keys())}")
        专用智能体路由日志器.debug(f"🔍 自定义变量详情: {自定义变量}")

        # 构建优化的用户消息
        用户消息 = (
            f"请对以下聊天内容进行{请求数据.分析维度}分析：\n\n{请求数据.聊天内容}"
        )
        if 请求数据.聊天记录:
            用户消息 += f"\n\n聊天记录：{请求数据.聊天记录}"
        if 请求数据.上下文信息:
            用户消息 += f"\n\n上下文信息：{请求数据.上下文信息}"


        # 生成会话id
        会话id = (
            f"chat_analysis_{用户['id']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )

        # 调用智能体服务，传入自定义变量
        对话结果 = await LangChain智能体服务实例.智能体对话(
            智能体id=聊天内容分析助手_智能体id,
            用户表id=用户["id"],
            用户消息=用户消息,
            会话id=会话id,
            测试模式=False,
            自定义变量=自定义变量,
        )

        if 对话结果.get("status") == 100:
            # 成功响应
            响应数据 = {
                "分析结果": 对话结果["data"]["智能体回复"],
                "分析维度": 请求数据.分析维度,
                "处理时长": 对话结果["data"]["处理时长"],
                "会话id": 会话id,
                "分析时间": datetime.now().isoformat(),
            }

            专用智能体路由日志器.info(f"✅ 聊天内容分析成功 - 用户id: {用户['id']}")
            return 统一响应模型.成功(响应数据, "聊天内容分析完成")
        else:
            # 失败响应
            错误信息 = 对话结果.get("message", "分析失败")
            专用智能体路由日志器.error(f"❌ 聊天内容分析失败: {错误信息}")
            return 统一响应模型.失败(2002, f"聊天内容分析失败: {错误信息}")

    except Exception as e:
        专用智能体路由日志器.error(f"❌ 聊天内容分析接口异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(2003, f"聊天内容分析接口异常: {str(e)}")
