<template>
  <div class="agent-overview">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>LangChain智能体概览</h2>
      <p>智能体使用情况总览、性能分析和趋势监控</p>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="今日对话数"
              :value="仪表盘数据.今日统计?.今日对话数 || 0"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <MessageOutlined />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-text">较昨日</span>
              <span class="trend-value positive">+12%</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="活跃智能体"
              :value="仪表盘数据.今日统计?.今日使用智能体 || 0"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <RobotOutlined />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-text">总计</span>
              <span class="trend-value">{{ 智能体总数 }}个</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="活跃用户"
              :value="仪表盘数据.今日统计?.今日活跃用户 || 0"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-text">本周</span>
              <span class="trend-value">{{ 仪表盘数据.本周统计?.本周活跃用户 || 0 }}人</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="令牌消耗"
              :value="仪表盘数据.今日统计?.今日令牌消耗 || 0"
              :value-style="{ color: '#eb2f96' }"
            >
              <template #prefix>
                <ApiOutlined />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-text">本月</span>
              <span class="trend-value">{{ 格式化数字(仪表盘数据.本月统计?.本月令牌消耗 || 0) }}</span>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <a-row :gutter="16">
        <!-- 对话趋势图 -->
        <a-col :span="16">
          <a-card title="对话趋势分析" class="chart-card">
            <template #extra>
              <a-radio-group v-model:value="趋势图表类型" size="small">
                <a-radio-button value="对话数量">对话数量</a-radio-button>
                <a-radio-button value="用户数量">用户数量</a-radio-button>
                <a-radio-button value="智能体数量">智能体数量</a-radio-button>
              </a-radio-group>
            </template>
            <div ref="趋势图表容器" style="height: 300px;"></div>
          </a-card>
        </a-col>
        
        <!-- 热门智能体排行 -->
        <a-col :span="8">
          <a-card title="热门智能体排行" class="chart-card">
            <template #extra>
              <a-button type="link" size="small" @click="跳转智能体管理">
                查看全部
              </a-button>
            </template>
            <div class="hot-agents-list">
              <div 
                v-for="(agent, index) in 仪表盘数据.热门智能体?.slice(0, 8)" 
                :key="agent.智能体id"
                class="hot-agent-item"
                @click="查看智能体详情(agent.智能体id)"
              >
                <div class="rank" :class="getRankClass(index)">{{ index + 1 }}</div>
                <div class="agent-info">
                  <div class="agent-name">{{ agent.智能体名称 }}</div>
                  <div class="agent-stats">
                    <span class="conversation-count">{{ agent.对话次数 }}次对话</span>
                    <span class="user-count">{{ agent.用户数量 }}用户</span>
                  </div>
                </div>
                <div class="agent-trend">
                  <ArrowUpOutlined class="trend-icon positive" />
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 性能分析 -->
    <div class="performance-section">
      <a-row :gutter="16">
        <!-- 性能指标 -->
        <a-col :span="12">
          <a-card title="性能指标" class="performance-card">
            <div class="performance-metrics">
              <div class="metric-item">
                <div class="metric-label">平均响应时间</div>
                <div class="metric-value">
                  <span class="value">{{ 计算平均响应时间() }}</span>
                  <span class="unit">ms</span>
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-label">平均令牌消耗</div>
                <div class="metric-value">
                  <span class="value">{{ 计算平均令牌消耗() }}</span>
                  <span class="unit">tokens</span>
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-label">成功率</div>
                <div class="metric-value">
                  <span class="value">99.8</span>
                  <span class="unit">%</span>
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-label">并发处理能力</div>
                <div class="metric-value">
                  <span class="value">128</span>
                  <span class="unit">req/s</span>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 活跃用户 -->
        <a-col :span="12">
          <a-card title="活跃用户" class="users-card">
            <template #extra>
              <a-button type="link" size="small" @click="跳转用户管理">
                查看全部
              </a-button>
            </template>
            <div class="active-users-list">
              <div 
                v-for="user in 仪表盘数据.活跃用户?.slice(0, 6)" 
                :key="user.用户id"
                class="user-item"
              >
                <a-avatar :size="32" class="user-avatar">
                  {{ (user.昵称 || user.用户名 || '用户')?.charAt(0) }}
                </a-avatar>
                <div class="user-info">
                  <div class="user-name">{{ user.昵称 || user.用户名 || '未知用户' }}</div>
                  <div class="user-stats">{{ user.对话次数 }}次对话</div>
                </div>
                <div class="user-agents">
                  <a-tag size="small">{{ user.使用智能体数 }}个智能体</a-tag>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <a-card title="快速操作">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-button type="primary" block @click="跳转创建智能体">
              <template #icon><PlusOutlined /></template>
              创建智能体
            </a-button>
          </a-col>
          <a-col :span="6">
            <a-button block @click="跳转智能体管理">
              <template #icon><SettingOutlined /></template>
              管理智能体
            </a-button>
          </a-col>
          <a-col :span="6">
            <a-button block @click="跳转使用统计">
              <template #icon><BarChartOutlined /></template>
              详细统计
            </a-button>
          </a-col>
          <a-col :span="6">
            <a-button block @click="刷新数据">
              <template #icon><ReloadOutlined /></template>
              刷新数据
            </a-button>
          </a-col>
        </a-row>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import {
  MessageOutlined,
  RobotOutlined,
  UserOutlined,
  ApiOutlined,
  ArrowUpOutlined,
  PlusOutlined,
  SettingOutlined,
  BarChartOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'

import langchainService from '@/services/langchainService'

const router = useRouter()

// 响应式数据
const 仪表盘数据 = ref({})
const 智能体总数 = ref(0)
const 趋势图表类型 = ref('对话数量')
const 加载中 = ref(false)

// 图表相关
const 趋势图表容器 = ref(null)
let 趋势图表实例 = null

// 计算属性和方法
const 格式化数字 = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num?.toString() || '0'
}

const getRankClass = (index) => {
  if (index === 0) return 'rank-1'
  if (index === 1) return 'rank-2'
  if (index === 2) return 'rank-3'
  return ''
}

const 计算平均响应时间 = () => {
  // 从热门智能体数据中计算平均响应时间
  const agents = 仪表盘数据.value.热门智能体 || []
  if (agents.length === 0) return '0'
  
  const total = agents.reduce((sum, agent) => sum + (agent.平均处理时长 || 0), 0)
  return Math.round(total / agents.length)
}

const 计算平均令牌消耗 = () => {
  const agents = 仪表盘数据.value.热门智能体 || []
  if (agents.length === 0) return '0'
  
  const total = agents.reduce((sum, agent) => sum + (agent.总令牌消耗 || 0), 0)
  const totalConversations = agents.reduce((sum, agent) => sum + (agent.对话次数 || 0), 0)
  
  if (totalConversations === 0) return '0'
  return Math.round(total / totalConversations)
}

// 页面方法
const 初始化页面 = async () => {
  try {
    await Promise.all([
      加载仪表盘数据(),
      加载智能体总数()
    ])
  } catch (error) {
    message.error('页面初始化失败')
  }
}

// 数据缓存
const 数据缓存 = new Map()
const 缓存过期时间 = 2 * 60 * 1000 // 2分钟

const 加载仪表盘数据 = async (强制刷新 = false) => {
  try {
    加载中.value = true

    // 检查缓存
    const 缓存键 = 'dashboard_data'
    const 缓存数据 = 数据缓存.get(缓存键)
    const 现在 = Date.now()

    if (!强制刷新 && 缓存数据 && (现在 - 缓存数据.时间戳) < 缓存过期时间) {
      仪表盘数据.value = 缓存数据.数据
      nextTick(() => {
        渲染趋势图表()
      })
      return
    }

    console.log('🔍 开始加载仪表盘数据')
    const response = await langchainService.获取统计仪表盘数据()
    console.log('📊 仪表盘数据响应:', response)

    if (response.status === 100) {
      仪表盘数据.value = response.data

      // 更新缓存
      数据缓存.set(缓存键, {
        数据: response.data,
        时间戳: 现在
      })

      // 保存最后更新时间
      localStorage.setItem('langchain_overview_last_update', 现在.toString())

      console.log('✅ 仪表盘数据加载成功:', response.data)
      nextTick(() => {
        渲染趋势图表()
      })
    } else {
      throw new Error(response.message || '获取数据失败')
    }
  } catch (error) {
    console.error('❌ 加载仪表盘数据失败:', error)

    // 如果有缓存数据，使用缓存数据
    const 缓存数据 = 数据缓存.get('dashboard_data')
    if (缓存数据) {
      仪表盘数据.value = 缓存数据.数据
      message.warning('使用缓存数据，请稍后刷新')
      nextTick(() => {
        渲染趋势图表()
      })
    } else {
      message.error('加载仪表盘数据失败，请刷新重试')
    }
  } finally {
    加载中.value = false
  }
}

const 加载智能体总数 = async () => {
  try {
    const response = await langchainService.获取智能体列表({
      页码: 1,
      每页数量: 1
    })
    if (response.status === 100) {
      智能体总数.value = response.data.总数量 || 0
    }
  } catch (error) {
    console.error('加载智能体总数失败:', error)
  }
}

const 渲染趋势图表 = () => {
  if (!趋势图表容器.value || !仪表盘数据.value.最近趋势) return

  if (趋势图表实例) {
    趋势图表实例.dispose()
  }

  趋势图表实例 = echarts.init(趋势图表容器.value)

  const 趋势数据 = 仪表盘数据.value.最近趋势 || []
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: 趋势数据.map(item => item.日期),
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [
      {
        name: 趋势图表类型.value,
        type: 'line',
        data: 趋势数据.map(item => {
          switch (趋势图表类型.value) {
            case '对话数量': return item.对话数量
            case '用户数量': return item.用户数量
            case '智能体数量': return item.智能体数量
            default: return item.对话数量
          }
        }),
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#1890ff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
              { offset: 1, color: 'rgba(24, 144, 255, 0.05)' }
            ]
          }
        }
      }
    ]
  }

  趋势图表实例.setOption(option)
}

// 导航方法
const 跳转创建智能体 = () => {
  router.push('/langchain/agents/create')
}

const 跳转智能体管理 = () => {
  router.push('/langchain/agents')
}

const 跳转使用统计 = () => {
  router.push('/langchain/usage-statistics')
}

const 跳转用户管理 = () => {
  router.push('/users')
}

const 查看智能体详情 = (智能体id) => {
  router.push(`/langchain/agents/${智能体id}/edit`)
}

const 刷新数据 = async () => {
  try {
    // 清除缓存，强制刷新
    数据缓存.clear()
    await Promise.all([
      加载仪表盘数据(true),
      加载智能体总数()
    ])
    message.success('数据已刷新')
  } catch (error) {
    message.error('刷新数据失败')
  }
}

// 防抖函数
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 防抖的图表渲染
const 防抖渲染趋势图表 = debounce(() => {
  渲染趋势图表()
}, 300)

// 监听趋势图表类型变化
watch(() => 趋势图表类型.value, () => {
  防抖渲染趋势图表()
})

// 页面可见性监听
let 页面可见性监听器 = null

// 生命周期
onMounted(() => {
  初始化页面()

  // 添加页面可见性监听，页面重新可见时刷新数据
  页面可见性监听器 = () => {
    if (!document.hidden && 仪表盘数据.value.基础统计) {
      // 页面重新可见时，如果数据超过5分钟则自动刷新
      const 最后更新时间 = localStorage.getItem('langchain_overview_last_update')
      const 现在 = Date.now()
      if (!最后更新时间 || 现在 - parseInt(最后更新时间) > 5 * 60 * 1000) {
        刷新数据()
      }
    }
  }

  document.addEventListener('visibilitychange', 页面可见性监听器)
})

// 组件卸载时清理
onUnmounted(() => {
  if (页面可见性监听器) {
    document.removeEventListener('visibilitychange', 页面可见性监听器)
  }

  // 清理图表实例
  if (趋势图表实例) {
    趋势图表实例.dispose()
    趋势图表实例 = null
  }
})
</script>

<style scoped>
.agent-overview {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.statistics-cards {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-trend {
  margin-top: 8px;
  font-size: 12px;
}

.trend-text {
  color: #8c8c8c;
  margin-right: 8px;
}

.trend-value {
  font-weight: 500;
}

.trend-value.positive {
  color: #52c41a;
}

.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.hot-agents-list {
  max-height: 320px;
  overflow-y: auto;
}

.hot-agent-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.hot-agent-item:hover {
  background-color: #f8f9fa;
  border-radius: 6px;
  margin: 0 -8px;
  padding: 12px 8px;
}

.hot-agent-item:last-child {
  border-bottom: none;
}

.rank {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #1890ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
}

.rank.rank-1 {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #d48806;
}

.rank.rank-2 {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #595959;
}

.rank.rank-3 {
  background: linear-gradient(135deg, #cd7f32, #deb887);
  color: #8c4a00;
}

.agent-info {
  flex: 1;
}

.agent-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.agent-stats {
  font-size: 12px;
  color: #8c8c8c;
}

.conversation-count {
  margin-right: 12px;
}

.user-count {
  color: #1890ff;
}

.agent-trend {
  display: flex;
  align-items: center;
}

.trend-icon {
  font-size: 14px;
}

.trend-icon.positive {
  color: #52c41a;
}

.performance-section {
  margin-bottom: 24px;
}

.performance-card,
.users-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.performance-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.metric-item {
  text-align: center;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.metric-label {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.metric-value {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
}

.metric-value .value {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.metric-value .unit {
  font-size: 12px;
  color: #8c8c8c;
}

.active-users-list {
  max-height: 240px;
  overflow-y: auto;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.user-item:last-child {
  border-bottom: none;
}

.user-avatar {
  background: #1890ff;
  margin-right: 12px;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.user-stats {
  font-size: 12px;
  color: #8c8c8c;
}

.user-agents {
  display: flex;
  align-items: center;
}

.quick-actions .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .statistics-cards .ant-col {
    margin-bottom: 16px;
  }

  .charts-section .ant-col {
    margin-bottom: 16px;
  }

  .performance-section .ant-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .agent-overview {
    padding: 16px;
  }

  .performance-metrics {
    grid-template-columns: 1fr;
  }
}
</style>
