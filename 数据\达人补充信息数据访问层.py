"""
达人补充信息数据访问层
负责处理用户达人补充信息表的数据访问操作

特性：
1. 补充联系方式的增删改查
2. 权限验证和数据安全
3. 分页查询和搜索
4. 与联系方式表的关联管理
"""

from typing import Any, Dict, List, Optional
from uuid import UUID

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器


class 达人补充信息数据访问:
    """达人补充信息数据访问类"""

    @staticmethod
    async def 添加补充联系方式(
        用户达人关联表id: int,
        联系方式: str,
        联系方式类型: str,
        联系方式表id: Optional[int] = None,
        个人备注: Optional[str] = None,
        个人标签: Optional[List[str]] = None,
        补充信息: Optional[str] = None,
    ) -> Optional[int]:
        """
        添加补充联系方式

        Args:
            用户达人关联表id: 用户达人关联表id
            联系方式: 联系方式内容
            联系方式类型: 联系方式类型
            联系方式表id: 联系方式表id（可选）
            个人备注: 个人备注
            个人标签: 个人标签列表
            补充信息: 补充信息

        Returns:
            新创建的补充信息id或None
        """
        try:
            插入SQL = """
            INSERT INTO 用户达人补充信息表
            (用户达人关联表id, 联系方式, 联系方式类型, 联系方式表id, 个人备注, 个人标签, 补充信息, 创建时间, 更新时间)
            VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
            RETURNING id
            """

            # 处理个人标签 - 转换为JSON字符串
            import json

            标签JSON = json.dumps(个人标签 if 个人标签 else [], ensure_ascii=False)

            结果 = await 异步连接池实例.执行查询(
                插入SQL,
                (
                    用户达人关联表id,
                    联系方式,
                    联系方式类型,
                    联系方式表id,
                    个人备注,
                    标签JSON,
                    补充信息,
                ),
            )

            if 结果:
                补充信息id = 结果[0]["id"]
                数据库日志器.info(f"添加补充联系方式成功: ID={补充信息id}")
                return 补充信息id
            else:
                return None

        except Exception as e:
            错误日志器.error(
                f"添加补充联系方式失败: 关联表id={用户达人关联表id}, 错误={str(e)}"
            )
            return None

    @staticmethod
    async def 检查补充信息权限(
        补充信息id: int, 用户id: int
    ) -> Optional[Dict[str, Any]]:
        """
        检查补充信息权限

        Args:
            补充信息id: 补充信息id
            用户id: 用户id

        Returns:
            权限信息或None
        """
        try:
            检查权限SQL = """
            SELECT si.id, si.用户达人关联表id FROM 用户达人补充信息表 si
            JOIN 用户达人关联表 ur ON si.用户达人关联表id = ur.id
            WHERE si.id = $1 AND ur.用户id = $2 AND ur.状态 = 1
            """

            结果 = await 异步连接池实例.执行查询(检查权限SQL, (补充信息id, 用户id))

            if 结果:
                return 结果[0]
            else:
                return None

        except Exception as e:
            错误日志器.error(
                f"检查补充信息权限失败: 补充信息id={补充信息id}, 用户id={用户id}, 错误={str(e)}"
            )
            return None

    @staticmethod
    async def 更新补充联系方式(
        补充信息id: int,
        联系方式: Optional[str] = None,
        联系方式类型: Optional[str] = None,
        个人备注: Optional[str] = None,
        个人标签: Optional[List[str]] = None,
        补充信息: Optional[str] = None,
        用户联系人表id: Optional[UUID] = None,
        微信信息表id: Optional[int] = None,
    ) -> bool:
        """
        更新补充联系方式

        Args:
            补充信息id: 补充信息id
            联系方式: 联系方式内容
            联系方式类型: 联系方式类型
            个人备注: 个人备注
            个人标签: 个人标签列表
            补充信息: 补充信息
            用户联系人表id: 用户联系人表id
            微信信息表id: 微信信息表id

        Returns:
            是否更新成功
        """
        try:
            # 构建动态更新字段
            更新字段 = []
            参数列表 = []
            参数索引 = 1

            if 联系方式 is not None:
                更新字段.append(f"联系方式 = ${参数索引}")
                参数列表.append(联系方式)
                参数索引 += 1

            if 联系方式类型 is not None:
                更新字段.append(f"联系方式类型 = ${参数索引}")
                参数列表.append(联系方式类型)
                参数索引 += 1

            if 个人备注 is not None:
                更新字段.append(f"个人备注 = ${参数索引}")
                参数列表.append(个人备注)
                参数索引 += 1

            if 个人标签 is not None:
                更新字段.append(f"个人标签 = ${参数索引}")
                # 处理个人标签 - 转换为JSON字符串
                import json

                标签JSON = json.dumps(个人标签 if 个人标签 else [], ensure_ascii=False)
                参数列表.append(标签JSON)
                参数索引 += 1

            if 补充信息 is not None:
                更新字段.append(f"补充信息 = ${参数索引}")
                参数列表.append(补充信息)
                参数索引 += 1

            if 用户联系人表id is not None:
                更新字段.append(f"用户联系人表id = ${参数索引}")
                参数列表.append(用户联系人表id)
                参数索引 += 1

            if 微信信息表id is not None:
                更新字段.append(f"微信信息表id = ${参数索引}")
                参数列表.append(微信信息表id)
                参数索引 += 1

            if not 更新字段:
                return True  # 没有要更新的字段

            # 添加更新时间
            更新字段.append("更新时间 = NOW()")

            # 添加WHERE条件的参数
            参数列表.append(补充信息id)

            更新SQL = f"""
            UPDATE 用户达人补充信息表
            SET {", ".join(更新字段)}
            WHERE id = ${参数索引}
            """

            影响行数 = await 异步连接池实例.执行更新(更新SQL, 参数列表)

            if 影响行数 > 0:
                数据库日志器.info(f"更新补充联系方式成功: ID={补充信息id}")
                return True
            else:
                数据库日志器.warning(f"更新补充联系方式未影响任何行: ID={补充信息id}")
                return False

        except Exception as e:
            错误日志器.error(f"更新补充联系方式失败: ID={补充信息id}, 错误={str(e)}")
            return False

    @staticmethod
    async def 删除补充联系方式(补充信息id: int) -> bool:
        """
        删除补充联系方式

        Args:
            补充信息id: 补充信息id

        Returns:
            是否删除成功
        """
        try:
            删除SQL = """
            DELETE FROM 用户达人补充信息表
            WHERE id = $1
            """

            影响行数 = await 异步连接池实例.执行更新(删除SQL, (补充信息id,))

            if 影响行数 > 0:
                数据库日志器.info(f"删除补充联系方式成功: ID={补充信息id}")
                return True
            else:
                数据库日志器.warning(f"删除补充联系方式未影响任何行: ID={补充信息id}")
                return False

        except Exception as e:
            错误日志器.error(f"删除补充联系方式失败: ID={补充信息id}, 错误={str(e)}")
            return False

    @staticmethod
    async def 查询补充信息列表(
        用户id: int,
        页码: int = 1,
        每页数量: int = 20,
        关键词: Optional[str] = None,
        平台: Optional[str] = None,
        联系方式类型: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        查询补充信息列表

        Args:
            用户id: 用户id
            页码: 页码
            每页数量: 每页数量
            关键词: 搜索关键词
            平台: 平台筛选
            联系方式类型: 联系方式类型筛选

        Returns:
            查询结果
        """
        try:
            # 重构查询逻辑：从用户达人关联表开始，确保搜索所有认领的达人
            # 构建基础查询条件
            where_conditions = ["u.用户id = $1", "u.状态 = 1"]
            params = [用户id]
            param_index = 2

            # 构建JOIN表 - 使用INNER JOIN确保只返回有补充联系方式的达人记录
            join_tables = """
            INNER JOIN 用户达人补充信息表 s ON u.id = s.用户达人关联表id
            LEFT JOIN 达人表 t ON u.达人id = t.id AND u.平台 = '抖音'
            LEFT JOIN 微信达人表 wt ON u.达人id = wt.id AND u.平台 = '微信'
            """

            # 关键词搜索 - 搜索范围包括补充信息和达人基础信息
            keyword_condition = ""
            if 关键词:
                # 构建搜索条件列表
                search_conditions = [
                    f"s.联系方式 ILIKE ${param_index}",
                    f"s.个人备注 ILIKE ${param_index}",
                    f"s.补充信息 ILIKE ${param_index}",
                    f"t.昵称 ILIKE ${param_index}",
                    f"t.account_douyin ILIKE ${param_index}",
                    f"wt.昵称 ILIKE ${param_index}",
                    f'wt."finderUsername" ILIKE ${param_index}',
                ]

                keyword_condition = f"AND ({' OR '.join(search_conditions)})"
                params.append(f"%{关键词}%")
                param_index += 1

            # 平台筛选
            if 平台:
                where_conditions.append(f"u.平台 = ${param_index}")
                params.append(平台)
                param_index += 1

            # 联系方式类型筛选
            if 联系方式类型:
                where_conditions.append(f"s.联系方式类型 = ${param_index}")
                params.append(联系方式类型)
                param_index += 1

            # 查询总数 - 从用户达人关联表开始
            count_sql = f"""
            SELECT COUNT(DISTINCT u.id) as total
            FROM 用户达人关联表 u
            {join_tables}
            WHERE {" AND ".join(where_conditions)}
            {keyword_condition}
            """

            总数结果 = await 异步连接池实例.执行查询(count_sql, params)
            总数 = 总数结果[0]["total"] if 总数结果 else 0

            # 分页查询数据
            offset = (页码 - 1) * 每页数量
            limit_param_index = param_index
            offset_param_index = param_index + 1
            params.extend([每页数量, offset])

            data_sql = f"""
            SELECT
                u.id as 关联id,
                u.达人id,
                u.平台,
                u.平台账号,
                u.认领时间,
                s.id as 补充信息id,
                s.联系方式,
                s.联系方式类型,
                s.个人备注,
                s.个人标签,
                s.补充信息,
                s.用户联系人表id,
                s.微信信息表id,
                s.更新时间 as 联系方式更新时间,
                -- 关联的联系人信息
                uc.姓名 as 联系人姓名,
                uc.寄样地址 as 联系人寄样地址,
                uc.创建时间 as 联系人创建时间,
                -- 关联的微信信息
                wi.微信号 as 关联微信号,
                wi.昵称 as 关联微信昵称,
                wi.微信头像 as 关联微信头像,
                wi.修改时间 as 微信信息修改时间
            FROM 用户达人关联表 u
            {join_tables}
            LEFT JOIN 用户联系人表 uc ON s.用户联系人表id = uc.用户联系人id
            LEFT JOIN 微信信息表 wi ON s.微信信息表id = wi.id
            WHERE {" AND ".join(where_conditions)}
            {keyword_condition}
            ORDER BY COALESCE(s.更新时间, u.认领时间) DESC, u.认领时间 DESC
            LIMIT ${limit_param_index} OFFSET ${offset_param_index}
            """

            数据结果 = await 异步连接池实例.执行查询(data_sql, params)

            return {"列表": 数据结果, "总数": 总数, "页码": 页码, "每页数量": 每页数量}

        except Exception as e:
            错误日志器.error(f"查询补充信息列表失败: 用户id={用户id}, 错误={str(e)}")
            return {"列表": [], "总数": 0, "页码": 页码, "每页数量": 每页数量}

    @staticmethod
    async def 查询或创建补充信息记录(用户达人关联表id: int) -> Optional[int]:
        """
        查询或创建用户达人补充信息记录

        Args:
            用户达人关联表id: 用户达人关联表id

        Returns:
            补充信息id或None
        """
        try:
            # 先查询是否存在
            查询补充信息SQL = """
            SELECT id FROM 用户达人补充信息表
            WHERE 用户达人关联表id = $1
            LIMIT 1
            """

            查询结果 = await 异步连接池实例.执行查询(
                查询补充信息SQL, (用户达人关联表id,)
            )

            if 查询结果:
                return 查询结果[0]["id"]
            else:
                # 如果不存在补充信息记录，创建一个基础记录 - 需要先创建默认联系方式
                from 数据.线索数据操作 import 获取或创建联系方式并返回完整数据

                # 创建默认联系方式记录
                默认联系方式数据 = await 获取或创建联系方式并返回完整数据(
                    内容="待补充", 类型="未知", 记录来源="系统自动创建"
                )

                if not 默认联系方式数据:
                    错误日志器.error(
                        f"创建默认联系方式失败: 用户达人关联表id={用户达人关联表id}"
                    )
                    return None

                默认联系方式id = 默认联系方式数据["id"]

                创建补充信息SQL = """
                INSERT INTO 用户达人补充信息表
                (用户达人关联表id, 联系方式, 联系方式类型, 联系方式表id, 个人备注, 个人标签, 创建时间, 更新时间)
                VALUES ($1, '待补充', '未知', $2, '通过寄样申请自动创建', '[]', NOW(), NOW())
                RETURNING id
                """

                创建结果 = await 异步连接池实例.执行查询(
                    创建补充信息SQL, (用户达人关联表id, 默认联系方式id)
                )

                if 创建结果:
                    补充信息id = 创建结果[0]["id"]
                    数据库日志器.info(f"自动创建补充信息记录: ID={补充信息id}")
                    return 补充信息id
                else:
                    return None

        except Exception as e:
            错误日志器.error(
                f"查询或创建补充信息记录失败: 关联表id={用户达人关联表id}, 错误={str(e)}"
            )
            return None

    @staticmethod
    async def 查询关联记录_通过补充信息id(补充信息id: int) -> Optional[Dict[str, Any]]:
        """
        通过补充信息id查询关联记录

        Args:
            补充信息id: 补充信息id

        Returns:
            关联记录信息或None
        """
        try:
            查询关联记录SQL = """
            SELECT 
                s.id as 补充信息id,
                s.用户达人关联表id,
                u.用户id,
                u.达人id,
                u.平台,
                u.平台账号,
                u.认领时间,
                u.状态
            FROM 用户达人补充信息表 s
            JOIN 用户达人关联表 u ON s.用户达人关联表id = u.id
            WHERE s.id = $1
            """

            结果 = await 异步连接池实例.执行查询(查询关联记录SQL, (补充信息id,))

            if 结果:
                return 结果[0]
            else:
                return None

        except Exception as e:
            错误日志器.error(
                f"查询关联记录失败: 补充信息id={补充信息id}, 错误={str(e)}"
            )
            return None

    @staticmethod
    async def 搜索达人补充信息_用于关联(
        用户id: int, 关键词: Optional[str] = None, 页码: int = 1, 每页数量: int = 20
    ) -> Dict[str, Any]:
        """
        搜索达人补充信息用于联系人关联

        Args:
            用户id: 用户id
            关键词: 搜索关键词
            页码: 页码
            每页数量: 每页数量

        Returns:
            搜索结果
        """
        try:
            # 构建查询条件
            where_conditions = ["u.用户id = $1", "u.状态 = 1"]
            params = [用户id]

            # 关键词搜索
            keyword_condition = ""
            if 关键词 and 关键词.strip():
                keyword_condition = """
                AND (
                    s.联系方式 ILIKE $2
                    OR s.联系方式类型 ILIKE $2
                    OR s.个人备注 ILIKE $2
                    OR u.平台账号 ILIKE $2
                )
                """
                params.append(f"%{关键词.strip()}%")

            # 查询总数
            count_sql = f"""
            SELECT COUNT(DISTINCT s.id) as total
            FROM 用户达人补充信息表 s
            INNER JOIN 用户达人关联表 u ON s.用户达人关联表id = u.id
            WHERE {" AND ".join(where_conditions)}
            {keyword_condition}
            """

            总数结果 = await 异步连接池实例.执行查询(count_sql, params)
            总数 = 总数结果[0]["total"] if 总数结果 else 0

            # 分页查询数据
            offset = (页码 - 1) * 每页数量
            params.extend([每页数量, offset])

            data_sql = f"""
            SELECT
                s.id as 补充信息id,
                s.联系方式,
                s.联系方式类型,
                s.个人备注,
                s.个人标签,
                s.补充信息,
                s.用户联系人表id,
                u.平台,
                u.平台账号,
                u.认领时间
            FROM 用户达人补充信息表 s
            INNER JOIN 用户达人关联表 u ON s.用户达人关联表id = u.id
            WHERE {" AND ".join(where_conditions)}
            {keyword_condition}
            ORDER BY s.更新时间 DESC
            LIMIT ${len(params) - 1} OFFSET ${len(params)}
            """

            数据结果 = await 异步连接池实例.执行查询(data_sql, params)

            return {"列表": 数据结果, "总数": 总数, "页码": 页码, "每页数量": 每页数量}

        except Exception as e:
            错误日志器.error(f"搜索达人补充信息失败: 用户id={用户id}, 错误={str(e)}")
            return {"列表": [], "总数": 0, "页码": 页码, "每页数量": 每页数量}

    @staticmethod
    async def 关联用户联系人(补充信息id: int, 联系人id: str) -> bool:
        """
        关联用户联系人到补充信息

        Args:
            补充信息id: 补充信息id
            联系人id: 联系人UUID

        Returns:
            是否关联成功
        """
        try:
            更新SQL = """
            UPDATE 用户达人补充信息表
            SET 用户联系人表id = $1, 更新时间 = NOW()
            WHERE id = $2
            """

            影响行数 = await 异步连接池实例.执行更新(更新SQL, (联系人id, 补充信息id))
            return 影响行数 > 0

        except Exception as e:
            错误日志器.error(
                f"关联用户联系人失败: 补充信息id={补充信息id}, 联系人id={联系人id}, 错误={str(e)}"
            )
            return False

    @staticmethod
    async def 创建完整达人信息链(
        用户id: int,
        联系方式: str,
        联系方式类型: str,
        个人备注: Optional[str] = None,
        个人标签: Optional[List[str]] = None,
        补充信息: Optional[str] = None,
        联系人id: Optional[str] = None,
    ) -> Optional[int]:
        """
        创建完整的达人信息链（事务操作）
        包括：用户达人关联表 -> 联系方式表 -> 用户达人补充信息表 -> 关联联系人

        Args:
            用户id: 用户id
            联系方式: 联系方式内容
            联系方式类型: 联系方式类型
            个人备注: 个人备注
            个人标签: 个人标签列表
            补充信息: 补充信息
            联系人id: 要关联的联系人id

        Returns:
            补充信息id或None
        """
        try:
            # 1. 创建用户达人关联表记录
            关联记录SQL = """
            INSERT INTO 用户达人关联表 (用户id, 达人id, 平台, 平台账号, 认领时间, 状态, 备注)
            VALUES ($1, NULL, '未知', $2, NOW(), 1, '通过联系人创建')
            RETURNING id
            """

            关联结果 = await 异步连接池实例.执行查询(关联记录SQL, (用户id, 联系方式))

            if not 关联结果:
                return None

            关联表id = 关联结果[0]["id"]

            # 2. 创建联系方式记录
            from 数据.联系方式数据访问层 import 获取或创建联系方式并返回完整数据

            联系方式数据 = await 获取或创建联系方式并返回完整数据(
                内容=联系方式,
                类型=联系方式类型,
                记录来源="用户创建",
            )

            联系方式表id = 联系方式数据.get("id") if 联系方式数据 else None

            # 3. 创建达人补充信息记录
            补充信息id = await 达人补充信息数据访问.添加补充联系方式(
                用户达人关联表id=关联表id,
                联系方式=联系方式,
                联系方式类型=联系方式类型,
                联系方式表id=联系方式表id,
                个人备注=个人备注,
                个人标签=个人标签,
                补充信息=补充信息,
            )

            if not 补充信息id:
                # 回滚：删除关联表记录
                await 异步连接池实例.执行更新(
                    "DELETE FROM 用户达人关联表 WHERE id = $1", (关联表id,)
                )
                return None

            # 4. 如果提供了联系人id，则关联联系人
            if 联系人id:
                关联成功 = await 达人补充信息数据访问.关联用户联系人(
                    补充信息id, 联系人id
                )

                if not 关联成功:
                    # 回滚：删除所有已创建的记录
                    await 异步连接池实例.执行更新(
                        "DELETE FROM 用户达人补充信息表 WHERE id = $1", (补充信息id,)
                    )
                    await 异步连接池实例.执行更新(
                        "DELETE FROM 用户达人关联表 WHERE id = $1", (关联表id,)
                    )
                    return None

            return 补充信息id

        except Exception as e:
            错误日志器.error(f"创建完整达人信息链失败: 用户id={用户id}, 错误={str(e)}")
            return None

    @staticmethod
    async def 检查用户达人关联权限(
        用户id: int, 达人id: int
    ) -> Optional[Dict[str, Any]]:
        """
        检查用户是否认领了指定达人

        Args:
            用户id: 用户id
            达人id: 达人id

        Returns:
            关联信息字典或None
        """
        try:
            检查关联SQL = """
            SELECT id FROM 用户达人关联表
            WHERE 用户id = $1 AND 达人id = $2 AND 状态 = 1
            LIMIT 1
            """
            关联结果 = await 异步连接池实例.执行查询(检查关联SQL, (用户id, 达人id))

            if 关联结果:
                return {"关联表id": 关联结果[0]["id"], "有权限": True}
            else:
                return None

        except Exception as e:
            错误日志器.error(
                f"检查用户达人关联权限失败: 用户id={用户id}, 达人id={达人id}, 错误={str(e)}"
            )
            return None

    @staticmethod
    async def 更新关联表达人id(关联表id: int, 达人id: int, 用户id: int) -> bool:
        """
        更新用户达人关联表的达人id字段

        Args:
            关联表id: 关联表id
            达人id: 达人id
            用户id: 用户id

        Returns:
            更新是否成功
        """
        try:
            更新关联表SQL = """
            UPDATE 用户达人关联表
            SET 达人id = $1
            WHERE id = $2 AND 用户id = $3 AND 达人id IS NULL
            """

            更新结果 = await 异步连接池实例.执行更新(
                更新关联表SQL, (达人id, 关联表id, 用户id)
            )

            return 更新结果 > 0

        except Exception as e:
            错误日志器.error(
                f"更新关联表达人id失败: 关联表id={关联表id}, 达人id={达人id}, 用户id={用户id}, 错误={str(e)}"
            )
            return False


# 创建数据访问层实例
达人补充信息数据访问实例 = 达人补充信息数据访问()
