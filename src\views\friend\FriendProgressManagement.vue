<template>
  <div class="progress-management-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h3>产品对接进度管理</h3>
      <p>跟踪和管理微信好友的产品对接进度，支持看板和列表两种视图。</p>
    </div>

    <!-- 全局筛选 -->
    <a-card class="global-filter-card">
      <a-form-item label="选择我方微信" help="请先选择一个微信账号，下方将展示该账号下的所有产品对接数据。">
        <a-select 
          v-model:value="selectedWeChatId" 
          placeholder="选择一个微信账号以加载数据"
          allowClear
          showSearch
          optionFilterProp="children"
          @change="handleWeChatAccountChange"
          :loading="loadingWeChatAccounts"
          style="width: 350px;"
        >
          <a-select-option 
            v-for="account in myWeChatAccounts" 
            :key="account.id" 
            :value="account.id"
          >
            {{ account.昵称 }} ({{ account.微信号 }})
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-card>

    <!-- 视图切换 -->
    <div v-if="selectedWeChatId">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <a-tab-pane key="kanban" tab="看板视图">
          <FriendProgressKanban 
            :wechatId="selectedWeChatId"
            :kanbanData="kanbanData"
            :loading="loadingKanban"
            @refresh="fetchKanbanData"
          />
        </a-tab-pane>
        <a-tab-pane key="list" tab="列表视图">
          <FriendProgressList 
            :wechatId="selectedWeChatId"
            :listData="listData"
            :loading="loadingList"
            :pagination="pagination"
            @search="handleListSearch"
            @reset="handleListReset"
            @create="showCreateModal"
            @edit="editProgress"
            @delete="deleteProgress"
            @tableChange="handleTableChange"
          />
        </a-tab-pane>
      </a-tabs>
    </div>
    <div v-else>
      <a-empty description="请先在上方选择一个微信账号" />
    </div>

    <!-- 创建/编辑弹窗 (共享) -->
    <a-modal
      v-model:open="isModalVisible"
      :title="modalTitle"
      width="600px"
      @ok="handleModalSubmit"
      @cancel="handleModalCancel"
      :confirmLoading="isSubmitting"
    >
      <!-- 表单内容 -->
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { message, Modal } from 'ant-design-vue';
import FriendProgressKanban from './FriendProgressKanban.vue';
import FriendProgressList from './FriendProgressList.vue';
import { wechatService } from '@/services/friend';
import progressKanbanService from '@/services/friend/progressKanbanService';
// progressService 已删除 - 产品对接进度功能已移除

// state
const activeTab = ref('kanban');
const selectedWeChatId = ref(null);
const myWeChatAccounts = ref([]);
const loadingWeChatAccounts = ref(false);

// Kanban State
const kanbanData = ref([]);
const loadingKanban = ref(false);

// List State
const listData = ref([]);
const loadingList = ref(false);
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
});
const listFilterParams = ref({});

// Modal State
const isModalVisible = ref(false);
const isEditMode = ref(false);
const modalTitle = computed(() => (isEditMode.value ? '编辑对接进度' : '新增对接'));
const isSubmitting = ref(false);
const editFormRef = ref();
const editForm = ref({});

// --- Methods ---

// 初始化加载
onMounted(async () => {
  loadingWeChatAccounts.value = true;
  try {
    const res = await wechatService.getUserWeChatAccounts();
    // 直接使用服务返回的 `微信账号列表`，因为服务层已经处理好了数据结构
    myWeChatAccounts.value = res.微信账号列表.map(acc => ({
      id: acc.微信id,
      微信号: acc.微信号,
      昵称: acc.昵称 || acc.微信号
    }));
  } catch (error) {
    message.error(error.message || '获取微信账号列表时发生未知错误');
  } finally {
    loadingWeChatAccounts.value = false;
  }
});

const handleWeChatAccountChange = (value) => {
  if (value) {
    selectedWeChatId.value = value;
    // 默认加载当前激活的tab数据
    if (activeTab.value === 'kanban') {
      fetchKanbanData();
    } else {
      fetchListData();
    }
  } else {
    // 清空数据
    selectedWeChatId.value = null;
    kanbanData.value = [];
    listData.value = [];
  }
};

  // 获取看板数据
const fetchKanbanData = async () => {
  if (!selectedWeChatId.value) return;
  loadingKanban.value = true;
  try {
    const res = await progressKanbanService.getBoardData({ 微信id: selectedWeChatId.value });
    if (res.status === 100) {
      // 后端API响应数据在data字段中
      kanbanData.value = res.data;
      console.log('看板数据加载成功', kanbanData.value);
    } else {
      message.error(res.message || '获取看板数据失败');
      kanbanData.value = [];
    }
  } catch (error) {
    console.error('看板数据请求错误详情:', error);
    message.error('请求看板数据时出错');
    kanbanData.value = [];
  } finally {
    loadingKanban.value = false;
  }
};

// 获取列表数据
const fetchListData = async () => {
  if (!selectedWeChatId.value) return;
  loadingList.value = true;
  const params = {
    微信id: selectedWeChatId.value,
    页码: pagination.value.current,
    每页条数: pagination.value.pageSize,
    ...listFilterParams.value,
  };
  try {
    // progressService 已删除 - 产品对接进度功能已移除
    message.warning('产品对接进度功能已移除');
    listData.value = [];
    pagination.value.total = 0;
  } catch (error) {
    console.error('列表数据请求错误:', error);
    message.error('请求列表数据时出错');
    listData.value = [];
  } finally {
    loadingList.value = false;
  }
};

// 重新加载当前视图的数据
const refreshCurrentView = () => {
  if (activeTab.value === 'kanban') {
    fetchKanbanData();
  } else {
    fetchListData();
  }
};

// --- Modal Handlers ---
const showCreateModal = () => {
  isEditMode.value = false;
  editForm.value = { 我方微信号id: selectedWeChatId.value };
  isModalVisible.value = true;
};

const editProgress = (record) => {
  isEditMode.value = true;
  editForm.value = { ...record };
  isModalVisible.value = true;
};

const deleteProgress = (id) => {
  Modal.confirm({
    title: '确定要删除这条对接记录吗？',
    content: '删除后将无法恢复，请谨慎操作。',
    okText: '确定删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        // progressService 已删除 - 产品对接进度功能已移除
        message.warning('产品对接进度功能已移除');
      } catch (error) {
        message.error('请求删除时出错');
      }
    },
  });
};

const handleModalSubmit = async () => {
  isSubmitting.value = true;
  try {
    // progressService 已删除 - 产品对接进度功能已移除
    message.warning('产品对接进度功能已移除');
    isModalVisible.value = false;
  } catch (error) {
    message.error('请求时发生错误');
  } finally {
    isSubmitting.value = false;
  }
};

const handleModalCancel = () => {
  isModalVisible.value = false;
};

// --- List View Handlers ---
const handleListSearch = (filters) => {
  listFilterParams.value = filters;
  pagination.value.current = 1;
  fetchListData();
};

const handleListReset = () => {
  listFilterParams.value = {};
  pagination.value.current = 1;
  fetchListData();
};

const handleTableChange = (pager) => {
  pagination.value.current = pager.current;
  pagination.value.pageSize = pager.pageSize;
  fetchListData();
};

</script>

<style lang="less" scoped>
.progress-management-page {
  .page-header {
    margin-bottom: 20px;
    h3 {
      font-size: 20px;
      font-weight: 500;
    }
    p {
      color: #888;
    }
  }
  .global-filter-card {
    margin-bottom: 16px;
  }
}
</style> 