<template>
  <div class="commission-analysis-chart">
    <div 
      ref="chartContainer" 
      :style="{ height: height }"
      class="chart-container"
    ></div>
    
    <div v-if="loading" class="chart-loading">
      <a-spin size="large" />
    </div>
    
    <div v-if="!loading && !hasData" class="chart-empty">
      <a-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import gvmService from '../../services/gvmService'

defineOptions({
  name: 'CommissionAnalysisChart'
})

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  },
  height: {
    type: String,
    default: '300px'
  },
  loading: {
    type: Boolean,
    default: false
  },
  chartType: {
    type: String,
    default: 'doughnut', // 'doughnut' | 'bar'
    validator: (value) => ['doughnut', 'bar'].includes(value)
  }
})

const chartContainer = ref(null)
let chartInstance = null

const hasData = computed(() => {
  return props.data && (props.data.预估佣金 > 0 || props.data.实际佣金 > 0)
})

const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || props.loading || !hasData.value) return
  
  const colors = gvmService.getChartColors()
  const option = props.chartType === 'doughnut' 
    ? getDoughnutOption(colors) 
    : getBarOption(colors)
  
  chartInstance.setOption(option, true)
}

const getDoughnutOption = (colors) => {
  const 预估佣金 = props.data.预估佣金 || 0
  const 实际佣金 = props.data.实际佣金 || 0
  const 佣金差额 = Math.abs(预估佣金 - 实际佣金)
  
  return {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const percentage = params.percent
        const value = gvmService.formatAmount(params.value)
        return `${params.name}: ${value} (${percentage}%)`
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'center',
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '佣金分析',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold',
            formatter: (params) => {
              return `${params.name}\n${gvmService.formatAmount(params.value)}`
            }
          }
        },
        labelLine: {
          show: false
        },
        data: [
          {
            value: 实际佣金,
            name: '实际佣金',
            itemStyle: { color: colors.success }
          },
          {
            value: 佣金差额,
            name: 预估佣金 > 实际佣金 ? '预估超出' : '实际超出',
            itemStyle: { 
              color: 预估佣金 > 实际佣金 ? colors.warning : colors.primary 
            }
          }
        ],
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: () => Math.random() * 200
      }
    ]
  }
}

const getBarOption = (colors) => {
  const 预估佣金 = props.data.预估佣金 || 0
  const 实际佣金 = props.data.实际佣金 || 0
  
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params) => {
        let html = '<div style="margin-bottom: 4px;">佣金对比</div>'
        params.forEach(param => {
          const value = gvmService.formatAmount(param.value)
          html += `
            <div style="display: flex; align-items: center; margin-bottom: 2px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="margin-right: 8px;">${param.seriesName}:</span>
              <span style="font-weight: 600;">${value}</span>
            </div>
          `
        })
        return html
      }
    },
    legend: {
      data: ['预估佣金', '实际佣金'],
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['佣金对比'],
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => gvmService.formatAmount(value)
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [
      {
        name: '预估佣金',
        type: 'bar',
        data: [预估佣金],
        itemStyle: {
          color: colors.warning,
          borderRadius: [4, 4, 0, 0]
        },
        barWidth: '40%'
      },
      {
        name: '实际佣金',
        type: 'bar',
        data: [实际佣金],
        itemStyle: {
          color: colors.success,
          borderRadius: [4, 4, 0, 0]
        },
        barWidth: '40%'
      }
    ],
    animation: true,
    animationDuration: 1000
  }
}

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

watch(() => props.data, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

watch(() => props.chartType, () => {
  nextTick(() => {
    updateChart()
  })
})

watch(() => props.loading, (newLoading) => {
  if (!newLoading) {
    nextTick(() => {
      updateChart()
    })
  }
})

onMounted(() => {
  nextTick(() => {
    initChart()
  })
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})

defineExpose({
  refreshChart: updateChart,
  getChartInstance: () => chartInstance
})
</script>

<style scoped>
.commission-analysis-chart {
  position: relative;
  width: 100%;
}

.chart-container {
  width: 100%;
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.chart-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
</style>
