import { reactive, computed } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 智能体表单状态管理
 */
export function useAgentForm() {
  // 表单数据 - 支持六大核心功能
  const 智能体表单 = reactive({
    // 基础配置
    智能体名称: '',
    智能体描述: '',
    是否启用: true,
    langchain_模型配置表id: null,
    温度参数: 0.7,
    最大令牌数: 4000,
    记忆窗口大小: 10,

    // 提示词配置
    系统提示词: '',
    用户提示词: '',
    角色设定: '',
    行为规范: '',

    // 1. RAG检索功能配置
    启用rag: false,
    知识库列表: [],
    检索策略: 'similarity',
    嵌入模型id: null,
    相似度阈值: 0.7,
    最大检索数量: 5,
    查询优化配置: {
      启用查询优化: false,
      查询优化策略: 'rewrite',
      查询优化模型id: null,
      查询优化提示词: ''
    },

    // 2. 工具动态加载配置
    启用工具调用: false,
    工具列表: [],
    工具调用策略: 'auto',
    最大并发工具数: 3,
    工具调用超时: 30,

    // 3. 工具循环调用配置（通过工具调用策略控制）
    // 4. 线程参数注入配置（自动处理，无需前端配置）

    // 5. JSON格式化输出配置
    输出格式: 'text',
    自定义回复格式: null,

    // 6. 自定义变量支持配置
    自定义变量: []
  })

  // 表单状态
  const 表单状态 = reactive({
    加载中: false,
    保存中: false,
    验证错误: {},
    最近保存状态: null
  })

  // 计算属性
  const 表单是否有效 = computed(() => {
    return 智能体表单.智能体名称?.trim() &&
           智能体表单.langchain_模型配置表id &&
           Object.keys(表单状态.验证错误).length === 0
  })

  const 表单是否已修改 = computed(() => {
    // 这里可以添加与原始数据的比较逻辑
    return true
  })

  // 重置表单
  const 重置表单 = () => {
    Object.assign(智能体表单, {
      智能体名称: '',
      智能体描述: '',
      是否启用: true,
      对话模型id: null,
      温度参数: 0.7,
      系统提示词: '',
      用户提示词: '',
      角色设定: '',
      行为规范: '',
      知识库列表: [],
      启用rag: false,
      检索策略: 'similarity',
      嵌入模型id: null,
      相似度阈值: 0.7,
      最大检索数量: 5,
      启用工具调用: false,
      工具列表: [],
      输出格式: 'text',
      输出模式: 'text',
      json_schema: null,
      自定义变量: [],
      查询优化配置: {
        启用查询优化: false,
        查询优化策略: 'rewrite',
        查询优化模型id: null,
        查询优化提示词: ''
      }
    })
    表单状态.验证错误 = {}
    message.success('表单已重置')
  }

  // 更新表单字段
  const 更新表单字段 = (字段名, 值) => {
    智能体表单[字段名] = 值
    // 清除该字段的验证错误
    if (表单状态.验证错误[字段名]) {
      delete 表单状态.验证错误[字段名]
    }
  }

  // 批量更新表单
  const 批量更新表单 = (数据) => {
    if (!数据 || typeof 数据 !== 'object') {
      console.warn('批量更新表单：数据无效', 数据)
      return
    }

    try {
      Object.assign(智能体表单, 数据)
    } catch (error) {
      console.error('❌ 批量更新表单失败:', error)
      throw error
    }
  }

  // 设置验证错误
  const 设置验证错误 = (字段名, 错误信息) => {
    表单状态.验证错误[字段名] = 错误信息
  }

  // 清除验证错误
  const 清除验证错误 = (字段名) => {
    if (字段名) {
      delete 表单状态.验证错误[字段名]
    } else {
      表单状态.验证错误 = {}
    }
  }

  // 设置保存状态
  const 设置保存状态 = (状态) => {
    表单状态.最近保存状态 = 状态
    setTimeout(() => {
      表单状态.最近保存状态 = null
    }, 5000)
  }

  return {
    智能体表单,
    表单状态,
    表单是否有效,
    表单是否已修改,
    重置表单,
    更新表单字段,
    批量更新表单,
    设置验证错误,
    清除验证错误,
    设置保存状态
  }
}
