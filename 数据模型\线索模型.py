from datetime import datetime
from typing import Optional, Dict, Any, List

from pydantic import BaseModel, Field, ConfigDict


# 从项目已有的响应模型导入

class 联系方式基类(BaseModel):
    """联系方式基础模型，用于请求和响应中嵌入"""
    内容: str = Field(..., max_length=30, description="联系方式的具体内容，如手机号、微信号")
    类型: Optional[str] = Field(None, max_length=20, description="联系方式的类型，如：电话、微信、邮箱")
    来源: Optional[str] = Field(None, max_length=20, description="此联系方式记录的来源，如：用户添加、爬虫获取")

class 联系方式创建请求模型(联系方式基类):
    """联系方式创建请求模型"""
    pass

class 联系方式详情模型(联系方式基类):
    """联系方式详情响应模型"""
    id: int
    创建时间: datetime
    更新时间: datetime

    _model_config = ConfigDict(from_attributes=True)

class 线索数据库ORM模型基类(BaseModel):
    """从数据库ORM对象转换的基础模型，包含线索表通用字段"""
    id: int
    联系方式id: Optional[int] = None
    信息: Optional[str] = Field(None, description="存储线索的动态信息的JSON字符串") # 注意：数据库层面是JSON字符串
    线索来源: Optional[str] = None
    更新用户: Optional[int] = None
    创建时间: datetime
    更新时间: datetime

    _model_config = ConfigDict(from_attributes=True)

class 线索上传请求模型(BaseModel):
    """上传新线索时的请求体模型"""
    联系方式: 联系方式创建请求模型 = Field(..., description="关联的联系方式信息，将用于获取或创建联系方式记录")
    线索来源: Optional[str] = Field(None, description="这条线索本身的来源，例如：朋友推荐、市场活动A、官网注册等")
    额外信息: Optional[Dict[str, Any]] = Field(None, description="线索的其他动态信息，将以JSON格式存储，例如：{""名称"":""张三"", ""年龄"":30}")

class 线索详情响应模型(BaseModel):
    """获取单个线索详情时的响应体模型"""
    id: int
    联系方式id: Optional[int] = None
    信息: Optional[Dict[str, Any]] = Field(None, description="线索的动态信息，已从JSON字符串解析为字典")
    线索来源: Optional[str] = None
    更新用户: Optional[int] = None
    创建时间: datetime
    更新时间: datetime
    关联联系方式: Optional[联系方式详情模型] = Field(None, description="嵌套展示的联系方式完整详情")

    # 新增字段
    更新前信息: Optional[Dict[str, Any]] = Field(None, description="当线索被更新时，此字段表示更新前的信息内容。")
    本次请求的应用信息: Optional[Dict[str, Any]] = Field(None, description="当线索被更新时，此字段表示本次请求中提供的、用于更新的额外信息。")

    _model_config = ConfigDict(from_attributes=True)

class 线索列表项响应模型(线索数据库ORM模型基类):
    """获取线索列表时，列表中单个线索项的响应体模型"""
    信息: Optional[Dict[str, Any]] = Field(None, description="线索的动态信息，已从JSON字符串解析为字典") # 覆盖基类中的信息字段类型
    关联_联系方式内容: Optional[str] = Field(None, description="关联的联系方式内容，用于列表快速预览") 
    联系方式简略: Optional[str] = Field(None, description="用于前端展示的联系方式简略信息")

    # model_config = ConfigDict(from_attributes=True) # 如果是从ORM对象（包含join的额外字段）转换，可能需要这个

class 线索更新请求模型(BaseModel):
    """更新已存在线索时的请求体模型"""
    线索id: int = Field(..., description="要更新的目标线索的ID")
    联系方式: Optional[联系方式创建请求模型] = Field(None, description="可选：更新关联的联系方式信息。如果提供，则会获取或创建新的联系方式记录并关联。")
    线索来源: Optional[str] = Field(None, description="可选：更新线索本身的来源")
    额外信息: Optional[Dict[str, Any]] = Field(None, description="可选：更新线索的额外信息。提供此字段将完全覆盖旧的额外信息。")

class 获取线索详情请求模型(BaseModel):
    """获取单个线索详情的请求体模型"""
    线索id: int = Field(..., description="要获取详情的目标线索的ID")

class 分页请求模型(BaseModel):
    """通用分页请求模型"""
    页码: int = Field(1, ge=1, description="当前页码，从1开始")
    每页数量: int = Field(10, ge=1, le=100, description="每页希望获取的记录数量，范围1-100")

class 获取线索列表请求模型(分页请求模型):
    """获取线索列表的请求体模型，包含分页和搜索参数"""
    关键词: Optional[str] = Field(None, description="搜索关键词，同时在联系方式和JSON信息字段中进行全文搜索")
    起始id: Optional[int] = Field(0, description="筛选ID大于此值的记录，默认为0表示不限制起始ID")

    # 翻页标识字段，用于区分是否为翻页操作
    is_pagination: Optional[bool] = Field(False, description="是否为翻页操作，用于记录翻页次数")

    """TODO: 添加文档字符串"""
# 用于路由层指定复杂响应类型，例如列表响应
class 线索列表响应数据(BaseModel):
    """线索列表响应数据模型"""
    列表: List[线索列表项响应模型]
    总数: int
    页码: int
    每页数量: int

# 确保统一响应模型可以正确泛型化
# 例如：统一响应模型[线索详情响应模型]
# 例如：统一响应模型[线索列表响应数据] 

class 线索基础模型(BaseModel):
    """线索基础信息模型"""
    id: int = Field(..., description="线索ID")
    用户id: int = Field(..., description="线索创建用户id")

class 新增线索请求模型(BaseModel):
    """新增线索请求参数模型"""
    线索信息: Dict[str, Any] = Field(..., description="线索详细信息")

class 更新线索请求模型(BaseModel):
    """更新线索请求参数模型"""
    线索ID: int = Field(..., description="线索ID")
    线索信息: Dict[str, Any] = Field(..., description="线索详细信息")

class 删除线索请求模型(BaseModel):
    线索ID: int = Field(..., description="线索ID")

class 查询线索请求模型(BaseModel):
    页码: int = Field(1, description="页码")
    每页条数: int = Field(20, description="每页记录数")
    线索状态: Optional[int] = Field(None, description="线索状态")
    搜索关键词: Optional[str] = Field(None, description="搜索关键词")

class 线索列表响应模型(BaseModel):
    线索列表: List[线索详情响应模型] = Field(..., description="线索列表")
    总数量: int = Field(..., description="总记录数")
    当前页码: int = Field(..., description="当前页码")
    每页条数: int = Field(..., description="每页记录数")
    总页数: int = Field(..., description="总页数")

# 批量操作相关模型
class 批量删除线索请求模型(BaseModel):
    线索ID列表: List[int] = Field(..., description="要删除的线索ID列表")

class 批量更新线索状态请求模型(BaseModel):
    线索ID列表: List[int] = Field(..., description="线索ID列表")
    新状态: int = Field(..., description="新的线索状态")

# 线索统计相关模型
class 线索统计响应模型(BaseModel):
    总线索数: int = Field(0, description="总线索数量")
    今日新增: int = Field(0, description="今日新增线索数")
    本周新增: int = Field(0, description="本周新增线索数")
    本月新增: int = Field(0, description="本月新增线索数")
    各状态统计: Dict[str, int] = Field(default_factory=dict, description="各状态线索统计")

# 线索导入导出相关模型
class 线索导入请求模型(BaseModel):
    线索数据列表: List[Dict[str, Any]] = Field(..., description="要导入的线索数据列表")

class 线索导出请求模型(BaseModel):
    """线索导出参数模型"""
    线索ID列表: Optional[List[int]] = Field(None, description="指定导出的线索ID，为空则导出所有")
    导出格式: str = Field("excel", description="导出格式：excel、csv")
    包含字段: Optional[List[str]] = Field(None, description="指定导出字段，为空则导出所有字段") 