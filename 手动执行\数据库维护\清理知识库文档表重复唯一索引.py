"""
清理 langchain_知识库文档表 上关于 uuid 列的重复唯一索引脚本

用途：
- 保留对外唯一标识列 `uuid` 的单一唯一索引，删除多余的重复唯一索引，避免维护和查询计划上的干扰。

注意：
- 本脚本仅执行索引清理，不修改外键结构；业务仍保持“对外用 uuid、内部表关联用 id”的策略。
- 运行前请确认数据库连接配置正确。
"""

from __future__ import annotations

import asyncio
from typing import List, Dict

# 解决直接运行脚本时无法找到项目内模块的问题：将项目根目录加入 sys.path
from pathlib import Path
import sys

_PROJECT_ROOT = Path(__file__).resolve().parents[2]
if str(_PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(_PROJECT_ROOT))

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例


async def 异步获取_uuid相关索引() -> List[Dict]:
    """查询 langchain_知识库文档表 上与 uuid 相关的索引列表"""
    查询SQL = (
        """
        SELECT indexname, indexdef
        FROM pg_indexes
        WHERE schemaname = 'public'
          AND tablename = 'langchain_知识库文档表'
          AND (indexdef ILIKE '%(uuid)%' OR indexname IN ('文档id','文档uuid'))
        ORDER BY indexname
        """
    )
    结果 = await 异步连接池实例.执行查询(查询SQL)
    return list(结果) if 结果 else []


async def 异步删除重复唯一索引() -> None:
    """删除命名为 "文档id" 的重复唯一索引，保留 "文档uuid" 唯一索引"""
    # 确保连接池已初始化
    if not 异步连接池实例.已初始化:
        await 异步连接池实例.初始化数据库连接池()

    现有索引 = await 异步获取_uuid相关索引()
    print("现有与uuid相关的索引：")
    for 行 in 现有索引:
        print(f"- {行['indexname']}: {行['indexdef']}")

    # 仅删除命名为 "文档id" 的索引
    待删除索引名 = '文档id'
    命中 = any(行["indexname"] == 待删除索引名 for 行 in 现有索引)
    if not 命中:
        print("未发现需要删除的重复索引，无需操作。")
        return

    删除SQL = 'DROP INDEX IF EXISTS public."文档id"'
    await 异步连接池实例.执行更新(删除SQL)
    print('已删除重复唯一索引: public."文档id"')

    # 再次输出索引现状
    清理后 = await 异步获取_uuid相关索引()
    print("清理后与uuid相关的索引：")
    for 行 in 清理后:
        print(f"- {行['indexname']}: {行['indexdef']}")


async def 主() -> None:
    """脚本入口"""
    await 异步删除重复唯一索引()


if __name__ == "__main__":
    asyncio.run(主())


