"""
达人数据访问层 - 统一的达人数据访问接口
基于三层分离架构设计，整合抖音和微信达人数据访问

特性：
1. 统一的达人数据访问接口
2. 支持抖音和微信平台
3. 完整的达人认领管理
4. 联系方式补充信息管理
5. 寄样申请数据管理
6. 使用PostgreSQL原生语法和参数占位符
"""

from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 数据.抖音达人数据 import 搜索达人 as 搜索抖音达人, 获取达人详情 as 获取抖音达人详情
from 数据.微信达人数据 import 异步获取微信达人列表, 异步获取微信达人详情
from 日志 import 数据库日志器, 错误日志器


class 达人基础数据访问:
    """达人基础数据访问类"""

    @staticmethod
    async def 获取达人信息_通过ID和平台(达人id: Optional[int], 平台: str, 平台账号: Optional[str] = None) -> Dict[str, Any]:
        """
        根据达人id和平台获取达人信息
        
        Args:
            达人id: 达人的ID（可能为None）
            平台: 平台类型（抖音、微信）
            平台账号: 平台账号（当达人id为None时使用）
            
        Returns:
            包含达人信息的字典
        """
        try:
            # 如果达人id为None，返回平台账号信息
            if 达人id is None:
                return {
                    "昵称": 平台账号 or "未知",
                    "账号": 平台账号 or "",
                    "头像": "",
                    "粉丝数": 0,
                    "uid_number": ""
                }

            if 平台 == "抖音":
                查询SQL = """
                SELECT 昵称, account_douyin as 账号, avatar as 头像, 粉丝数, uid_number
                FROM 达人表
                WHERE id = $1
                """
            elif 平台 == "微信":
                查询SQL = """
                SELECT 昵称, finderUsername as 账号, 头像, 粉丝数文本 as 粉丝数
                FROM 微信达人表
                WHERE id = $1
                """
            else:
                return {"昵称": "未知平台", "账号": "", "头像": "", "粉丝数": 0}

            结果 = await 异步连接池实例.执行查询(查询SQL, (达人id,))
            
            if 结果:
                达人信息 = 结果[0]
                return {
                    "昵称": 达人信息.get("昵称", ""),
                    "账号": 达人信息.get("账号", ""),
                    "头像": 达人信息.get("头像", ""),
                    "粉丝数": 达人信息.get("粉丝数", 0),
                    "uid_number": 达人信息.get("uid_number", "")
                }
            else:
                return {"昵称": "达人不存在", "账号": "", "头像": "", "粉丝数": 0}

        except Exception as e:
            错误日志器.error(f"获取达人信息失败: 达人id={达人id}, 平台={平台}, 错误={str(e)}")
            return {"昵称": "获取失败", "账号": "", "头像": "", "粉丝数": 0}

    @staticmethod
    async def 获取用户团队信息(用户id: int) -> Dict[str, Any]:
        """
        获取用户的团队信息

        Args:
            用户id: 用户id

        Returns:
            团队信息字典
        """
        try:
            团队查询SQL = """
                SELECT 团队id, 加入时间, 状态
                FROM 用户团队关联表
                WHERE 用户id = $1 AND 状态 = '正常'
                ORDER BY 加入时间 DESC
                LIMIT 1
            """

            团队结果 = await 异步连接池实例.执行查询(团队查询SQL, (用户id,))

            if 团队结果:
                当前团队id = 团队结果[0]["团队id"]
                数据库日志器.debug(f"用户 {用户id} 的团队信息: 团队ID={当前团队id}")
                return {
                    "status": "success",
                    "data": {"团队id": 当前团队id},
                    "message": "获取团队信息成功"
                }
            else:
                数据库日志器.warning(f"用户 {用户id} 没有加入任何团队，使用兼容模式")
                return {
                    "status": "success",
                    "data": {"团队id": None},
                    "message": "用户未加入任何团队"
                }

        except Exception as e:
            错误日志器.error(f"获取用户团队信息失败: 用户id={用户id}, 错误={str(e)}")
            return {
                "status": "error",
                "data": {"团队id": None},
                "message": f"获取团队信息失败: {str(e)}"
            }

    @staticmethod
    async def 检查达人认领状态(用户id: int, 达人id: int) -> bool:
        """
        检查用户是否已认领该达人
        
        Args:
            用户id: 用户id
            达人id: 达人id
            
        Returns:
            是否已认领
        """
        try:
            检查关联SQL = """
            SELECT id FROM 用户达人关联表
            WHERE 用户id = $1 AND 达人id = $2 AND 状态 = 1
            LIMIT 1
            """
            
            结果 = await 异步连接池实例.执行查询(检查关联SQL, (用户id, 达人id))
            return len(结果) > 0
            
        except Exception as e:
            错误日志器.error(f"检查达人认领状态失败: 用户id={用户id}, 达人id={达人id}, 错误={str(e)}")
            return False

    @staticmethod
    async def 验证达人所有权(用户id: int, 达人id: int) -> Dict[str, Any]:
        """
        验证达人是否属于当前用户，并获取关联表id

        Args:
            用户id: 用户id
            达人id: 达人id

        Returns:
            验证结果字典
        """
        try:
            验证达人SQL = """
            SELECT ur.id as 关联表id FROM 用户达人关联表 ur
            WHERE ur.用户id = $1 AND ur.达人id = $2 AND ur.状态 = 1
            LIMIT 1
            """

            验证结果 = await 异步连接池实例.执行查询(验证达人SQL, (用户id, 达人id))

            if 验证结果:
                return {
                    "status": "success",
                    "data": {"关联表id": 验证结果[0]["关联表id"]},
                    "message": "验证通过"
                }
            else:
                return {
                    "status": "error",
                    "data": None,
                    "message": "没有权限或达人不存在"
                }

        except Exception as e:
            错误日志器.error(f"验证达人所有权失败: 用户id={用户id}, 达人id={达人id}, 错误={str(e)}")
            return {
                "status": "error",
                "data": None,
                "message": f"验证失败: {str(e)}"
            }

    @staticmethod
    async def 获取达人详情_通过ID(达人id: int, 平台: str = "抖音") -> Optional[Dict[str, Any]]:
        """
        通过ID获取达人详细信息
        
        Args:
            达人id: 达人id
            平台: 平台类型
            
        Returns:
            达人详细信息或None
        """
        try:
            if 平台 == "抖音":
                达人详情SQL = """
                SELECT id, 昵称, account_douyin, avatar, 粉丝数, uid_number
                FROM 达人表 
                WHERE id = $1
                """
            elif 平台 == "微信":
                达人详情SQL = """
                SELECT id, 昵称, finderUsername as account_douyin, 头像 as avatar, 粉丝数文本 as 粉丝数
                FROM 微信达人表 
                WHERE id = $1
                """
            else:
                return None
                
            结果 = await 异步连接池实例.执行查询(达人详情SQL, (达人id,))
            
            if 结果:
                return 结果[0]
            else:
                return None
                
        except Exception as e:
            错误日志器.error(f"获取达人详情失败: 达人id={达人id}, 平台={平台}, 错误={str(e)}")
            return None

    @staticmethod
    async def 验证达人存在性(达人id: int) -> Optional[Dict[str, Any]]:
        """
        验证达人是否存在并获取基本信息

        Args:
            达人id: 达人id

        Returns:
            达人基本信息或None
        """
        try:
            验证达人SQL = """
            SELECT id, 昵称, uid_number
            FROM 达人表
            WHERE id = $1
            LIMIT 1
            """

            达人记录 = await 异步连接池实例.执行查询(验证达人SQL, (达人id,))

            if 达人记录:
                return 达人记录[0]
            else:
                return None

        except Exception as e:
            错误日志器.error(f"验证达人存在性失败: 达人id={达人id}, 错误={str(e)}")
            return None

    @staticmethod
    async def 获取更新后达人信息(达人id: int) -> Optional[Dict[str, Any]]:
        """
        获取更新后的达人详细信息

        Args:
            达人id: 达人id

        Returns:
            达人详细信息或None
        """
        try:
            更新后达人SQL = """
            SELECT id, 昵称, account_douyin, avatar, 粉丝数, 关注数, uid_number, introduction
            FROM 达人表
            WHERE id = $1
            LIMIT 1
            """

            更新后达人记录 = await 异步连接池实例.执行查询(更新后达人SQL, (达人id,))

            if 更新后达人记录:
                return 更新后达人记录[0]
            else:
                return None

        except Exception as e:
            错误日志器.error(f"获取更新后达人信息失败: 达人id={达人id}, 错误={str(e)}")
            return None

    @staticmethod
    async def 检查用户达人关联状态(用户id: int, 达人id: int) -> bool:
        """
        检查用户是否已经关联了指定达人

        Args:
            用户id: 用户id
            达人id: 达人id

        Returns:
            是否已关联
        """
        try:
            检查关联SQL = """
            SELECT id FROM 用户达人关联表
            WHERE 用户id = $1 AND 达人id = $2 AND 状态 = 1
            LIMIT 1
            """

            现有关联 = await 异步连接池实例.执行查询(检查关联SQL, (用户id, 达人id))

            return len(现有关联) > 0

        except Exception as e:
            错误日志器.error(f"检查用户达人关联状态失败: 用户id={用户id}, 达人id={达人id}, 错误={str(e)}")
            return False

    @staticmethod
    async def 获取达人详细信息_用于返回(达人id: int) -> Optional[Dict[str, Any]]:
        """
        获取达人详细信息用于接口返回

        Args:
            达人id: 达人id

        Returns:
            达人详细信息或None
        """
        try:
            达人详情SQL = """
            SELECT id, 昵称, account_douyin, avatar, 粉丝数, uid_number
            FROM 达人表
            WHERE id = $1
            LIMIT 1
            """

            达人详情结果 = await 异步连接池实例.执行查询(达人详情SQL, (达人id,))

            if 达人详情结果:
                return 达人详情结果[0]
            else:
                return None

        except Exception as e:
            错误日志器.error(f"获取达人详细信息失败: 达人id={达人id}, 错误={str(e)}")
            return None


class 达人认领数据访问:
    """达人认领相关数据访问类"""

    @staticmethod
    async def 检查达人认领关联(用户id: int, 达人id: int) -> Optional[int]:
        """
        检查达人认领关联
        
        Args:
            用户id: 用户id
            达人id: 达人id
            
        Returns:
            关联表id或None
        """
        try:
            检查关联SQL = """
            SELECT id FROM 用户达人关联表 
            WHERE 用户id = $1 AND 达人id = $2 AND 状态 = 1
            LIMIT 1
            """
            
            结果 = await 异步连接池实例.执行查询(检查关联SQL, (用户id, 达人id))
            
            if 结果:
                return 结果[0]["id"]
            else:
                return None
                
        except Exception as e:
            错误日志器.error(f"检查达人认领关联失败: 用户id={用户id}, 达人id={达人id}, 错误={str(e)}")
            return None

    @staticmethod
    async def 更新达人关联表_达人id(关联表id: int, 用户id: int, 新达人id: int) -> bool:
        """
        更新用户达人关联表的达人id字段
        
        Args:
            关联表id: 关联表id
            用户id: 用户id
            新达人id: 新的达人id
            
        Returns:
            是否更新成功
        """
        try:
            更新关联表SQL = """
            UPDATE 用户达人关联表 
            SET 达人id = $1
            WHERE id = $2 AND 用户id = $3 AND 达人id IS NULL
            """
            
            影响行数 = await 异步连接池实例.执行更新(更新关联表SQL, (新达人id, 关联表id, 用户id))
            
            if 影响行数 > 0:
                数据库日志器.info(f"更新达人关联表成功: 关联表id={关联表id}, 新达人id={新达人id}")
                return True
            else:
                数据库日志器.warning(f"更新达人关联表未影响任何行: 关联表id={关联表id}")
                return False
                
        except Exception as e:
            错误日志器.error(f"更新达人关联表失败: 关联表id={关联表id}, 新达人id={新达人id}, 错误={str(e)}")
            return False


# 创建数据访问层实例
达人基础数据访问实例 = 达人基础数据访问()
达人认领数据访问实例 = 达人认领数据访问()
