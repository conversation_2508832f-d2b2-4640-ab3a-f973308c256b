<template>
  <a-modal
    v-model:open="modalVisible"
    title="基于模板创建智能体"
    width="720px"
    :confirm-loading="creating"
    @ok="handleCreate"
    @cancel="handleCancel"
    class="agent-create-modal"
  >
    <a-form layout="vertical" :model="formData" ref="formRef">
      <a-form-item label="选择模板" required>
        <a-select
          v-model:value="formData.模板智能体id"
          placeholder="请选择模板智能体"
          :loading="loadingTemplates"
          @change="handleTemplateChange"
          :options="templateOptions"
          show-search
          :filter-option="(input,opt)=>opt.label.toLowerCase().includes(input.toLowerCase())"
        />
      </a-form-item>

      <a-form-item label="智能体名称" required>
        <a-input v-model:value="formData.智能体名称" placeholder="请输入智能体名称" />
      </a-form-item>

      <a-form-item label="智能体描述">
        <a-textarea v-model:value="formData.智能体描述" :rows="2" placeholder="可选" />
      </a-form-item>

      <a-form-item label="关联知识库（可选）">
        <a-select
          mode="multiple"
          v-model:value="formData.知识库列表"
          :options="knowledgeBaseOptions"
          placeholder="选择要关联的知识库"
          :loading="loadingKBs"
        />
      </a-form-item>

      <div v-if="templateVariables.length > 0">
        <div style="font-weight:600;margin:12px 0 8px;">自定义变量</div>
        <a-row :gutter="12">
          <a-col :span="12" v-for="item in templateVariables" :key="item.变量名">
            <a-form-item :label="item.变量名">
              <a-input v-if="item.变量类型==='string'" v-model:value="formData.变量值[item.变量名]" :placeholder="item.变量描述 || '请输入'" />
              <a-input-number v-else-if="item.变量类型==='number'" v-model:value="formData.变量值[item.变量名]" style="width:100%" />
              <a-switch v-else-if="item.变量类型==='boolean'" v-model:checked="formData.变量值[item.变量名]" />
              <a-textarea v-else v-model:value="formData.变量值[item.变量名]" :rows="2" :placeholder="'JSON/文本'" />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import api from '@/services/api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  templateOptions: {
    type: Array,
    default: () => []
  },
  knowledgeBaseOptions: {
    type: Array,
    default: () => []
  },
  loadingTemplates: {
    type: Boolean,
    default: false
  },
  loadingKBs: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const formRef = ref()
const creating = ref(false)
const templateVariables = ref([])

const formData = reactive({
  模板智能体id: null,
  智能体名称: '',
  智能体描述: '',
  知识库列表: [],
  变量值: {}
})

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听visible变化
watch(() => props.visible, (visible) => {
  if (!visible) {
    resetForm()
  }
})

// 处理模板变更
const handleTemplateChange = async (tid) => {
  templateVariables.value = []
  formData.变量值 = {}
  if (!tid) return
  
  try {
    const res = await api.post('/user/langchain/agents/detail', { 智能体id: tid })
    if (res?.status === 100 && res?.data) {
      const vars = res.data.自定义变量 || []
      templateVariables.value = vars
      // 预填默认值
      const initVals = {}
      vars.forEach(v => { initVals[v.变量名] = v.默认值 })
      formData.变量值 = initVals
      if (!formData.智能体名称) {
        formData.智能体名称 = (res.data.智能体名称 || '') + '（我的）'
      }
    }
  } catch (e) {
    console.error(e)
  }
}

// 创建处理
const handleCreate = async (event) => {
  try {
    event?.preventDefault?.()

    if (!formData.模板智能体id || !formData.智能体名称) {
      message.error('请选择模板并填写名称')
      return
    }

    creating.value = true
    const payload = {
      模板智能体id: formData.模板智能体id,
      智能体名称: formData.智能体名称.trim(),
      智能体描述: formData.智能体描述 || '',
      知识库列表: formData.知识库列表 || [],
      变量值: formData.变量值 || {}
    }

    const res = await api.post('/user/langchain/agents/create-from-template', payload)
    if (res?.status === 100) {
      message.success('创建成功')
      emit('success')
    } else {
      message.error(res?.message || '创建失败')
    }
  } catch (e) {
    console.error(e)
    message.error('创建失败')
  } finally {
    creating.value = false
  }
}

// 取消处理
const handleCancel = () => {
  modalVisible.value = false
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    模板智能体id: null,
    智能体名称: '',
    智能体描述: '',
    知识库列表: [],
    变量值: {}
  })
  templateVariables.value = []
  formRef.value?.resetFields()
}
</script>

<style scoped>
.agent-create-modal :deep(.ant-modal-content) {
  border-radius: 12px;
  overflow: hidden;
}

.agent-create-modal :deep(.ant-modal-header) {
  background: linear-gradient(135deg, #52c41a 0%, #1890ff 100%);
  border-bottom: none;
  padding: 20px 24px;
}

.agent-create-modal :deep(.ant-modal-title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}
</style>
