<template>
  <div class="team-layout" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
    <div class="main-content">
      <!-- 左侧导航栏 -->
      <div class="team-sidebar" :class="{ collapsed: sidebarCollapsed }">
        <!-- 侧边栏标题区域 -->
        <div class="sidebar-title">
          <div class="title-content">
            <h3 v-if="!sidebarCollapsed">团队管理</h3>
            <a-button
              type="text"
              @click="toggleSidebar"
              class="collapse-btn"
            >
              <template #icon>
                <MenuFoldOutlined v-if="!sidebarCollapsed" />
                <MenuUnfoldOutlined v-else />
              </template>
            </a-button>
          </div>
        </div>

        <!-- 自定义菜单实现 -->
        <div class="custom-team-menu">
          <div
            v-for="menuItem in teamMenuItems"
            :key="menuItem.key"
            :class="[
              'custom-team-menu-item',
              { 'active': selectedKeys.includes(menuItem.key) },
              { 'collapsed': sidebarCollapsed }
            ]"
            @click="handleCustomTeamMenuClick(menuItem.key)"
          >
            <div class="team-menu-item-content">
              <span class="team-menu-icon">
                <component :is="getTeamIconComponent(menuItem.icon)" />
              </span>
              <span v-if="!sidebarCollapsed" class="team-menu-label">{{ menuItem.label }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧主要内容区域 -->
      <div class="team-main">
        <!-- 内容区域 -->
        <div class="team-content">
          <router-view v-if="isMounted && !isDestroyed" />
        </div>
      </div>
    </div>

    <!-- 小屏幕下侧边栏收起时的浮动展开按钮 -->
    <a-button
      v-if="sidebarCollapsed"
      type="primary"
      @click="toggleSidebar"
      class="floating-menu-btn"
    >
      <template #icon>
        <MenuUnfoldOutlined />
      </template>
    </a-button>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  DashboardOutlined,
  TeamOutlined,
  BarChartOutlined,
  UserOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons-vue'

// 组件状态管理
const isMounted = ref(false)
const isDestroyed = ref(false)

const route = useRoute()
const router = useRouter()

// 当前激活的菜单项
const activeTab = ref('dashboard')
const selectedKeys = ref(['dashboard'])

// 侧边栏折叠状态
const sidebarCollapsed = ref(false)

// 团队菜单项配置
const teamMenuItems = ref([
  {
    key: 'dashboard',
    icon: 'BarChartOutlined',
    label: '数据看板'
  },
  {
    key: 'overview',
    icon: 'DashboardOutlined',
    label: '团队总览'
  }
])

// 团队图标组件映射
const teamIconComponents = {
  DashboardOutlined,
  TeamOutlined,
  BarChartOutlined,
  UserOutlined
}

// 获取团队图标组件
const getTeamIconComponent = (iconName) => {
  return teamIconComponents[iconName] || DashboardOutlined
}

// 路由名称映射
const routeMap = {
  'TeamOverview': 'overview',
  'TeamDashboard': 'dashboard'
}

// 更新选中的菜单项
const updateSelectedKeys = () => {
  if (!route.name) return
  const routeName = route.name
  const key = routeMap[routeName] || 'dashboard'
  selectedKeys.value = [key]
  activeTab.value = key
}

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 处理菜单点击
const handleCustomTeamMenuClick = (key) => {
  if (isDestroyed.value || !isMounted.value) return

  selectedKeys.value = [key]
  activeTab.value = key

  const routeNames = {
    'overview': 'TeamOverview',
    'dashboard': 'TeamDashboard'
  }

  const routeName = routeNames[key]
  if (routeName && routeName !== route.name && !isDestroyed.value && isMounted.value) {
    router.push({ name: routeName })
  }
}

// 监听路由变化 - 只在组件挂载后开始监听
let routeWatcher = null

// 组件挂载时初始化
onMounted(async () => {
  await nextTick()
  isMounted.value = true

  // 在组件挂载后开始监听路由变化
  routeWatcher = watch(() => route.name, updateSelectedKeys, { immediate: true })
})

// 组件卸载时清理
onBeforeUnmount(() => {
  isDestroyed.value = true
  if (routeWatcher) {
    routeWatcher()
    routeWatcher = null
  }
})

defineOptions({
  name: 'TeamLayout'
})
</script>

<style scoped>
.team-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 主体内容区域 */
.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧导航栏 */
.team-sidebar {
  width: 256px;
  background: #fff;
  border-right: 1px solid #e8e8e8;
  transition: width 0.3s ease;
  overflow: hidden;
}

.team-sidebar.collapsed {
  width: 80px;
}

/* 侧边栏标题区域 */
.sidebar-title {
  height: 56px;
  background: #fafafa;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid #e8e8e8;
}

.title-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.sidebar-title h3 {
  color: #262626;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.collapse-btn {
  color: #666;
  border: none;
  box-shadow: none;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 4px;
}

.collapse-btn:hover {
  color: #1890ff;
  background-color: #f0f0f0;
}

/* 自定义团队菜单样式 - 与达人管理和好友管理保持一致 */
.custom-team-menu {
  height: calc(100vh - 64px - 56px);
  overflow-y: auto;
  padding: 8px 0;
}

.custom-team-menu-item {
  position: relative;
  margin: 0 8px 4px 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.custom-team-menu-item:hover {
  background-color: #f0f0f0;
}

.custom-team-menu-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.custom-team-menu-item.active::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #1890ff;
  border-radius: 1.5px 0 0 1.5px;
}

.team-menu-item-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  min-height: 48px;
}

.team-menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 12px;
  font-size: 16px;
}

.custom-team-menu-item.collapsed .team-menu-icon {
  margin-right: 0;
}

.team-menu-label {
  flex: 1;
  font-size: 14px;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 内容区域 */
.team-content {
  flex: 1;
  background: transparent;
  min-height: 600px;
}

/* 右侧主要内容区域 */
.team-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24px 24px 24px 24px;
  background: #ffffff;
}

/* 浮动菜单按钮 */
.floating-menu-btn {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1001;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .team-layout {
    position: relative;
    overflow-x: hidden;
  }

  .team-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .team-sidebar:not(.collapsed) {
    transform: translateX(0);
  }

  .team-main {
    margin-left: 0 !important;
    width: 100% !important;
    padding: 16px;
    position: relative;
    z-index: 1;
  }

  .floating-menu-btn {
    display: block;
  }

  /* 添加遮罩层，当侧边栏展开时 */
  .team-layout:not(.sidebar-collapsed)::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 999;
  }
}
</style>
