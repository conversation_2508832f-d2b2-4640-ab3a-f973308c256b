"""
GVM统计服务
提供用户、团队、店铺等维度的销售额统计功能

特性：
1. 用户销售额统计
2. 团队销售额统计
3. 店铺销售额统计
4. 达人业绩统计
5. 支持时间范围筛选
6. 完整的错误处理和日志记录
"""

from datetime import datetime, timedelta
from typing import Any, Dict, Optional

from 数据.店铺订单数据 import 店铺订单数据层
from 日志 import 接口日志器, 错误日志器
from 状态 import 通用


class GVM统计服务:
    """GVM统计服务类"""

    def __init__(self):
        self.数据层 = 店铺订单数据层()

    async def 获取用户销售额统计(
        self,
        用户id: int,
        时间范围: str = "30d",
        开始时间: Optional[str] = None,
        结束时间: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        获取用户销售额统计

        参数:
            用户id: 用户id
            时间范围: 时间范围（7d, 30d, 90d, all）
            开始时间: 自定义开始时间
            结束时间: 自定义结束时间

        返回:
            Dict: 统计结果
        """
        try:
            接口日志器.info(f"获取用户销售额统计: 用户id={用户id}, 时间范围={时间范围}")

            # 处理时间范围
            计算开始时间, 计算结束时间 = self._处理时间范围(
                时间范围, 开始时间, 结束时间
            )

            # 调用数据层统计
            统计结果 = await self.数据层.统计用户销售额(
                用户id, 计算开始时间, 计算结束时间
            )

            # 计算衍生指标
            统计结果["佣金率"] = (
                统计结果["实际佣金"] / 统计结果["总销售额"] * 100
                if 统计结果["总销售额"] > 0
                else 0
            )

            # 订单转化率需要有总订单数才能计算，这里暂时不计算
            # 统计结果["订单转化率"] = 已完成订单数 / 总订单数 * 100

            return {
                "status": 通用.成功,
                "message": "获取用户销售额统计成功",
                "data": {
                    "用户id": 用户id,
                    "时间范围": 时间范围,
                    "统计时间": datetime.now().isoformat(),
                    "统计数据": 统计结果,
                },
            }

        except Exception as e:
            错误日志器.error(f"获取用户销售额统计失败: {e}", exc_info=True)
            return {
                "status": 通用.服务器错误,
                "message": f"获取用户销售额统计失败: {str(e)}",
                "data": None,
            }

    async def 获取团队销售额统计(
        self,
        团队id: int,
        时间范围: str = "30d",
        开始时间: Optional[str] = None,
        结束时间: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        获取团队销售额统计

        参数:
            团队id: 团队id
            时间范围: 时间范围（7d, 30d, 90d, all）
            开始时间: 自定义开始时间
            结束时间: 自定义结束时间

        返回:
            Dict: 统计结果
        """
        try:
            接口日志器.info(f"获取团队销售额统计: 团队id={团队id}, 时间范围={时间范围}")

            # 处理时间范围
            计算开始时间, 计算结束时间 = self._处理时间范围(
                时间范围, 开始时间, 结束时间
            )

            # 调用数据层统计
            统计结果 = await self.数据层.统计团队销售额(
                团队id, 计算开始时间, 计算结束时间
            )

            # 计算衍生指标
            统计结果["佣金率"] = (
                统计结果["实际佣金"] / 统计结果["总销售额"] * 100
                if 统计结果["总销售额"] > 0
                else 0
            )

            统计结果["人均销售额"] = 统计结果["总销售额"] / max(
                1, 统计结果["参与成员数"]
            )

            统计结果["团队效率"] = 统计结果["订单数量"] / max(1, 统计结果["参与成员数"])

            return {
                "status": 通用.成功,
                "message": "获取团队销售额统计成功",
                "data": {
                    "团队id": 团队id,
                    "时间范围": 时间范围,
                    "统计时间": datetime.now().isoformat(),
                    "统计数据": 统计结果,
                },
            }

        except Exception as e:
            错误日志器.error(f"获取团队销售额统计失败: {e}", exc_info=True)
            return {
                "status": 通用.服务器错误,
                "message": f"获取团队销售额统计失败: {str(e)}",
                "data": None,
            }

    def _处理时间范围(
        self,
        时间范围: str,
        开始时间: Optional[str] = None,
        结束时间: Optional[str] = None,
    ) -> tuple[Optional[datetime], Optional[datetime]]:
        """
        处理时间范围参数

        参数:
            时间范围: 预设时间范围
            开始时间: 自定义开始时间
            结束时间: 自定义结束时间

        返回:
            tuple: (计算后的开始时间, 计算后的结束时间)
        """
        # 如果提供了自定义时间，优先使用
        if 开始时间 and 结束时间:
            try:
                # 将字符串转换为datetime对象
                开始_dt = datetime.strptime(开始时间, "%Y-%m-%d")
                结束_dt = datetime.strptime(结束时间, "%Y-%m-%d")
                # 结束时间设置为当天的23:59:59
                结束_dt = 结束_dt.replace(hour=23, minute=59, second=59)
                return 开始_dt, 结束_dt
            except ValueError:
                # 如果解析失败，使用默认时间范围
                pass

        # 根据预设时间范围计算
        现在 = datetime.now()

        if 时间范围 == "1d":
            开始 = 现在 - timedelta(days=1)
        elif 时间范围 == "7d":
            开始 = 现在 - timedelta(days=7)
        elif 时间范围 == "30d":
            开始 = 现在 - timedelta(days=30)
        elif 时间范围 == "90d":
            开始 = 现在 - timedelta(days=90)
        elif 时间范围 == "all":
            return None, None
        else:
            # 默认30天
            开始 = 现在 - timedelta(days=30)

        return 开始, 现在


# 创建服务实例
GVM统计服务实例 = GVM统计服务()
