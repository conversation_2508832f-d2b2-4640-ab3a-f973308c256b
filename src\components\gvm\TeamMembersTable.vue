<template>
  <div class="team-members-table">
    <a-table
      :columns="columns"
      :data-source="data"
      :loading="loading"
      :pagination="pagination"
      :scroll="{ x: 1000 }"
      row-key="id"
      size="middle"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <!-- 成员信息列 -->
        <template v-if="column.key === 'member'">
          <div class="member-info">
            <a-avatar :size="32" :src="record.avatar">
              {{ record.name?.charAt(0) }}
            </a-avatar>
            <div class="member-details">
              <div class="member-name">
                <a @click="handleMemberClick(record)">{{ record.name }}</a>
              </div>
              <div class="member-role">{{ record.role || '团队成员' }}</div>
            </div>
          </div>
        </template>
        
        <!-- 销售额列 -->
        <template v-else-if="column.key === 'sales'">
          <div class="sales-cell">
            <span class="sales-value">{{ formatAmount(record.sales) }}</span>
            <div v-if="record.salesGrowth" class="growth-indicator" :class="record.salesGrowth.trend">
              <component :is="getGrowthIcon(record.salesGrowth.trend)" />
              {{ record.salesGrowth.display }}
            </div>
          </div>
        </template>
        
        <!-- 订单数列 -->
        <template v-else-if="column.key === 'orders'">
          <div class="orders-cell">
            <span class="orders-value">{{ record.orders }}单</span>
            <a-progress
              :percent="getOrdersProgress(record.orders)"
              :stroke-color="getProgressColor(record.orders)"
              :show-info="false"
              size="small"
              class="orders-progress"
            />
          </div>
        </template>
        
        <!-- 佣金列 -->
        <template v-else-if="column.key === 'commission'">
          <span class="commission-value">{{ formatAmount(record.commission) }}</span>
        </template>
        
        <!-- 贡献度列 -->
        <template v-else-if="column.key === 'contribution'">
          <div class="contribution-cell">
            <a-progress
              :percent="record.contribution"
              :stroke-color="getContributionColor(record.contribution)"
              size="small"
            />
          </div>
        </template>
        
        <!-- 状态列 -->
        <template v-else-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        
        <!-- 操作列 -->
        <template v-else-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="handleView(record)">
              查看
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu @click="handleMenuClick($event, record)">
                  <a-menu-item key="detail">详细分析</a-menu-item>
                  <a-menu-item key="compare">对比分析</a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="message">发送消息</a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">
                更多 <DownOutlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ArrowUpOutlined, ArrowDownOutlined, MinusOutlined, DownOutlined } from '@ant-design/icons-vue'
import gvmService from '../../services/gvmService'

defineOptions({
  name: 'TeamMembersTable'
})

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['change', 'member-click', 'view', 'menu-click'])

// 表格列配置
const columns = [
  {
    title: '成员',
    key: 'member',
    width: 180,
    fixed: 'left'
  },
  {
    title: '销售额',
    key: 'sales',
    dataIndex: 'sales',
    width: 140,
    sorter: true,
    align: 'right'
  },
  {
    title: '订单数',
    key: 'orders',
    dataIndex: 'orders',
    width: 120,
    sorter: true,
    align: 'center'
  },
  {
    title: '佣金收入',
    key: 'commission',
    dataIndex: 'commission',
    width: 120,
    sorter: true,
    align: 'right'
  },
  {
    title: '贡献度',
    key: 'contribution',
    dataIndex: 'contribution',
    width: 120,
    sorter: true,
    align: 'center'
  },
  {
    title: '状态',
    key: 'status',
    dataIndex: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right',
    align: 'center'
  }
]

// 计算最大订单数（用于进度条）
const maxOrders = computed(() => {
  if (!props.data || props.data.length === 0) return 100
  return Math.max(...props.data.map(item => item.orders || 0))
})

// 格式化金额
const formatAmount = (amount) => {
  return gvmService.formatAmount(amount)
}

// 获取增长趋势图标
const getGrowthIcon = (trend) => {
  switch (trend) {
    case 'up':
      return ArrowUpOutlined
    case 'down':
      return ArrowDownOutlined
    default:
      return MinusOutlined
  }
}

// 获取订单进度百分比
const getOrdersProgress = (orders) => {
  return Math.min((orders / maxOrders.value) * 100, 100)
}

// 获取进度条颜色
const getProgressColor = (orders) => {
  const progress = getOrdersProgress(orders)
  if (progress >= 80) return '#52c41a'
  if (progress >= 50) return '#faad14'
  return '#f5222d'
}

// 获取贡献度颜色
const getContributionColor = (contribution) => {
  if (contribution >= 20) return '#52c41a'
  if (contribution >= 10) return '#faad14'
  return '#1890ff'
}

// 获取状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case 'active':
      return 'green'
    case 'inactive':
      return 'red'
    case 'pending':
      return 'orange'
    default:
      return 'default'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'active':
      return '活跃'
    case 'inactive':
      return '不活跃'
    case 'pending':
      return '待激活'
    default:
      return '未知'
  }
}

// 事件处理
const handleTableChange = (pagination, filters, sorter) => {
  emit('change', { pagination, filters, sorter })
}

const handleMemberClick = (member) => {
  emit('member-click', member)
}

const handleView = (member) => {
  emit('view', member)
}

const handleMenuClick = ({ key }, member) => {
  emit('menu-click', { action: key, member })
}
</script>

<style scoped>
.team-members-table {
  width: 100%;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.member-details {
  flex: 1;
  min-width: 0;
}

.member-name {
  font-weight: 600;
  margin-bottom: 2px;
}

.member-name a {
  color: #1890ff;
  text-decoration: none;
}

.member-name a:hover {
  text-decoration: underline;
}

.member-role {
  font-size: 12px;
  color: #8c8c8c;
}

.sales-cell {
  text-align: right;
}

.sales-value {
  display: block;
  font-weight: 600;
  font-size: 14px;
}

.growth-indicator {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 2px;
  font-size: 12px;
  margin-top: 2px;
}

.growth-indicator.up {
  color: #52c41a;
}

.growth-indicator.down {
  color: #f5222d;
}

.growth-indicator.stable {
  color: #8c8c8c;
}

.orders-cell {
  text-align: center;
}

.orders-value {
  display: block;
  font-weight: 600;
  margin-bottom: 4px;
}

.orders-progress {
  width: 60px;
  margin: 0 auto;
}

.commission-value {
  font-weight: 600;
  color: #52c41a;
}

.contribution-cell {
  padding: 0 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .member-info {
    gap: 8px;
  }
  
  .member-name {
    font-size: 13px;
  }
  
  .member-role {
    font-size: 11px;
  }
  
  .sales-value,
  .orders-value {
    font-size: 13px;
  }
  
  .growth-indicator {
    font-size: 11px;
  }
}
</style>
