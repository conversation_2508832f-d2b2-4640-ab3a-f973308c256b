<template>
  <div class="agents-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" tip="正在加载智能体列表..." />
    </div>

    <!-- 空状态 -->
    <div v-else-if="agents.length === 0" class="empty-state">
      <a-empty
        description="暂无可用的智能体"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      >
        <a-button type="primary" @click="$emit('refresh')">
          重新加载
        </a-button>
      </a-empty>
    </div>

    <!-- 智能体卡片列表 -->
    <div v-else class="agents-grid">
      <a-card
        v-for="agent in agents"
        :key="getAgentId(agent)"
        class="agent-card"
        :hoverable="true"
      >
        <template #title>
          <div class="agent-title">
            <a-avatar :size="32" style="margin-right: 8px; background-color: #1890ff;">
              {{ agent.智能体名称?.charAt(0) || 'A' }}
            </a-avatar>
            <span class="agent-name">{{ agent.智能体名称 }}</span>
          </div>
        </template>

        <template #extra>
          <div class="agent-tags">
            <a-tag :color="agent.是否公开 ? 'green' : 'blue'" size="small">
              {{ agent.是否公开 ? '公开' : '定制' }}
            </a-tag>
          </div>
        </template>

        <div class="agent-content">
          <p class="agent-description">
            {{ agent.智能体描述 || '暂无描述' }}
          </p>

          <div class="agent-details">
            <div class="detail-item">
              <span class="label">模型：</span>
              <span class="value">{{ agent.模型名称 || '未指定' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">状态：</span>
              <a-tag :color="getStatusColor(agent.状态)" size="small">
                {{ getStatusLabel(agent.状态) }}
              </a-tag>
            </div>
            <div class="detail-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ formatDate(agent.创建时间) }}</span>
            </div>
          </div>

          <div class="agent-actions">
            <a-button
              type="primary"
              size="small"
              @click.stop="handleUseAgent(agent)"
            >
              <RobotOutlined />
              使用智能体
            </a-button>
            <a-button
              size="small"
              style="margin-left: 8px;"
              @click.stop="handleEditAgent(agent)"
            >
              编辑
            </a-button>
            <a-button
              size="small"
              @click.stop="handleViewAgent(agent)"
              style="margin-left: 8px;"
            >
              <EyeOutlined />
              查看详情
            </a-button>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 分页 -->
    <div v-if="!loading && agents.length > 0" class="pagination-container">
      <a-pagination
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
        :show-size-changer="true"
        :show-quick-jumper="true"
        :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
        @change="handlePageChange"
        @show-size-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { Empty } from 'ant-design-vue'
import { RobotOutlined, EyeOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  agents: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  total: {
    type: Number,
    default: 0
  },
  currentPage: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 12
  }
})

// Emits
const emit = defineEmits(['edit', 'view', 'use', 'page-change', 'refresh'])

// 方法
const handlePageChange = (page, size) => {
  emit('page-change', page, size)
}

// 防抖处理，避免重复点击
let isProcessing = false

const handleUseAgent = async (agent) => {
  if (isProcessing) return
  isProcessing = true
  try {
    emit('use', agent)
  } finally {
    setTimeout(() => { isProcessing = false }, 1000)
  }
}

const handleEditAgent = async (agent) => {
  if (isProcessing) return
  isProcessing = true
  try {
    emit('edit', agent)
  } finally {
    setTimeout(() => { isProcessing = false }, 500)
  }
}

const handleViewAgent = async (agent) => {
  if (isProcessing) return
  isProcessing = true
  try {
    emit('view', agent)
  } finally {
    setTimeout(() => { isProcessing = false }, 500)
  }
}

const getAgentId = (agent) => {
  return agent?.智能体id || agent?.id || agent?.langchain_智能体配置表id
}

const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  try {
    return new Date(dateStr).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch {
    return '未知'
  }
}

const getStatusLabel = (status) => {
  const statusMap = {
    'running': '运行中',
    'stopped': '已停止',
    'error': '异常',
    'pending': '待启动'
  }
  return statusMap[status] || '未知'
}

const getStatusColor = (status) => {
  const colorMap = {
    'running': 'success',
    'stopped': 'default',
    'error': 'error',
    'pending': 'processing'
  }
  return colorMap[status] || 'default'
}
</script>

<style scoped>
.agents-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  color: #8c8c8c;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* 智能体网格 */
.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.agent-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.agent-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.agent-title {
  display: flex;
  align-items: center;
}

.agent-name {
  font-weight: 500;
  font-size: 16px;
}

.agent-tags {
  display: flex;
  gap: 4px;
}

.agent-content {
  padding: 0;
}

.agent-description {
  color: #595959;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  min-height: 42px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.agent-details {
  background: #fafafa;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  font-size: 13px;
  color: #8c8c8c;
  font-weight: 500;
}

.detail-item .value {
  font-size: 13px;
  color: #262626;
}

.agent-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
}

.agent-actions .ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .agents-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .agents-container {
    padding: 16px;
  }

  .agents-grid {
    grid-template-columns: 1fr;
  }
}
</style>
