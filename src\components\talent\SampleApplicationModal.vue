<template>
  <a-modal
    v-model:open="visible"
    title="寄样申请"
    width="800px"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <div class="sample-application-form">
      <!-- 达人信息展示 -->
      <div class="talent-info-section">
        <h4>
          <user-outlined />
          达人信息
        </h4>
        <a-descriptions :column="2" size="small" bordered>
          <a-descriptions-item label="达人昵称">
            {{ talentInfo?.昵称 || talentInfo?.nickname || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="平台">
            {{ talentInfo?.平台 || talentInfo?.platform || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="粉丝数">
            {{ formatFollowerCount(talentInfo?.粉丝数 || talentInfo?.follower_count) }}
          </a-descriptions-item>
          <a-descriptions-item label="联系方式">
            {{ talentInfo?.联系方式 || talentInfo?.contact || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 产品选择 -->
      <div class="product-selection-section">
        <h4>
          <appstore-outlined />
          选择产品
        </h4>
        <a-select
          v-model:value="formData.产品id"
          placeholder="请选择要寄送的产品"
          style="width: 100%"
          :loading="productsLoading"
          show-search
          :filter-option="filterProduct"
          @change="handleProductChange"
        >
          <a-select-option
            v-for="product in productList"
            :key="product.产品id"
            :value="product.产品id"
          >
            <div class="product-option">
              <div class="product-name">{{ product.产品名称 }}</div>
              <div class="product-desc">{{ product.产品描述 || '暂无描述' }}</div>
            </div>
          </a-select-option>
        </a-select>
      </div>

      <!-- 产品规格选择 -->
      <div v-if="selectedProduct && hasProductSpecs" class="spec-selection-section">
        <h4>
          <setting-outlined />
          选择规格
        </h4>
        <div class="spec-options">
          <!-- 新的数组格式规格选择 -->
          <div v-if="Array.isArray(selectedProduct.产品规格) || (selectedProduct.产品规格?.规格列表 && Array.isArray(selectedProduct.产品规格.规格列表))" class="spec-array-selection">
            <div class="spec-description">
              <span style="color: #666; font-size: 12px;">
                请选择需要的产品规格（可多选）：
              </span>
            </div>
            <a-checkbox-group
              v-model:value="selectedSpecArray"
              class="spec-checkbox-group"
            >
              <a-checkbox
                v-for="spec in availableSpecs"
                :key="spec.value"
                :value="spec.value"
                class="spec-checkbox"
              >
                {{ spec.label }}
              </a-checkbox>
            </a-checkbox-group>
          </div>

          <!-- 兼容旧的对象格式规格选择 -->
          <div v-else class="spec-object-selection">
            <a-select
              v-model:value="formData.产品规格"
              placeholder="请选择产品规格"
              style="width: 100%"
              :options="availableSpecs"
            />
          </div>
        </div>
      </div>

      <!-- 数量选择 -->
      <div v-if="selectedProduct" class="quantity-selection-section">
        <h4>
          <number-outlined />
          选择数量
        </h4>
        <a-input-number
          v-model:value="formData.数量"
          :min="1"
          :max="10"
          placeholder="请选择数量"
          style="width: 200px"
        />
        <span class="quantity-hint">（建议数量：1-3个）</span>
      </div>

      <!-- 联系人信息显示 -->
      <div class="contact-info-section">
        <h4>
          <contacts-outlined />
          联系人信息
        </h4>
        <div class="contact-info-display">
          <a-descriptions :column="1" size="small">
            <a-descriptions-item label="联系人">
              {{ getContactName() }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 地址选择（如果有多个地址） -->
        <a-form-item
          v-if="当前联系人地址选项.length > 1"
          label="选择收货地址"
          name="地址选择"
        >
          <a-select
            v-model:value="formData.选中地址索引"
            placeholder="请选择收货地址"
            @change="处理地址选择变化"
          >
            <a-select-option
              v-for="(地址, index) in 当前联系人地址选项"
              :key="index"
              :value="index"
            >
              {{ 地址.显示文本 }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 单个地址时的提示 -->
        <div v-else-if="当前联系人地址选项.length === 1" class="single-address-info">
          <a-alert
            message="收货地址"
            :description="当前联系人地址选项[0]?.显示文本"
            type="info"
            show-icon
          />
        </div>
      </div>

      <!-- 收件信息 -->
      <div class="recipient-info-section">
        <h4>
          <environment-outlined />
          收件信息
        </h4>
        <a-form
          :model="formData"
          :rules="formRules"
          layout="vertical"
          ref="formRef"
        >
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="收件人" name="收件人">
                <a-input
                  v-model:value="formData.收件人"
                  placeholder="请输入收件人姓名"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="收件电话" name="收件电话">
                <a-input
                  v-model:value="formData.收件电话"
                  placeholder="请输入收件人电话"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item label="收件地址" name="收件地址">
            <a-textarea
              v-model:value="formData.收件地址"
              placeholder="请输入详细的收件地址"
              :rows="3"
            />
          </a-form-item>
          <a-form-item label="申请备注" name="申请备注">
            <a-textarea
              v-model:value="formData.申请备注"
              placeholder="请输入申请备注（可选）"
              :rows="2"
            />
          </a-form-item>
        </a-form>
      </div>
    </div>

    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        提交申请
      </a-button>
    </template>
  </a-modal>
</template>

<script setup>
import productService from '@/services/productService'
import sampleService from '@/services/sampleService'
import {
  AppstoreOutlined,
  ContactsOutlined,
  EnvironmentOutlined,
  NumberOutlined,
  SettingOutlined,
  UserOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, nextTick, reactive, ref, watch } from 'vue'

// Props
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  talentInfo: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:open', 'success'])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const productsLoading = ref(false)
const formRef = ref()

// 产品相关数据
const productList = ref([])
const selectedProduct = ref(null)
const selectedSpecs = ref({})
const selectedSpecArray = ref([])

// 地址相关数据
const 当前联系人地址选项 = ref([])
const 完整联系人列表 = ref([])

// 表单数据
const formData = reactive({
  达人id: null,
  产品id: null,
  产品规格: null,
  数量: 1,
  收件人: '',
  收件地址: '',
  收件电话: '',
  申请备注: '',
  选中地址索引: null
})

// 表单验证规则
const formRules = {
  收件人: [
    { required: true, message: '请输入收件人姓名', trigger: 'blur' }
  ],
  收件电话: [
    { required: true, message: '请输入收件电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  收件地址: [
    { required: true, message: '请输入收件地址', trigger: 'blur' }
  ]
}

// 计算属性
const hasProductSpecs = computed(() => {
  if (!selectedProduct.value?.产品规格) return false

  // 支持新的数组格式
  if (Array.isArray(selectedProduct.value.产品规格)) {
    return selectedProduct.value.产品规格.length > 0
  }

  // 支持新的字典格式 {规格列表: [...]}
  if (selectedProduct.value.产品规格?.规格列表 && Array.isArray(selectedProduct.value.产品规格.规格列表)) {
    return selectedProduct.value.产品规格.规格列表.length > 0
  }

  // 兼容旧的对象格式
  if (typeof selectedProduct.value.产品规格 === 'object') {
    return Object.keys(selectedProduct.value.产品规格).length > 0
  }

  return false
})

const availableSpecs = computed(() => {
  if (!hasProductSpecs.value) return []

  const specs = selectedProduct.value.产品规格

  // 新的数组格式：直接返回规格数组
  if (Array.isArray(specs)) {
    return specs.map(spec => ({
      value: spec,
      label: spec
    }))
  }

  // 新的字典格式 {规格列表: [...]}
  if (specs?.规格列表 && Array.isArray(specs.规格列表)) {
    return specs.规格列表.map(spec => ({
      value: spec,
      label: spec
    }))
  }

  // 兼容旧的对象格式
  const specOptions = []
  Object.keys(specs).forEach(key => {
    const value = specs[key]
    if (Array.isArray(value)) {
      value.forEach(item => {
        specOptions.push({
          value: `${key}: ${item}`,
          label: `${key}: ${item}`
        })
      })
    } else if (value) {
      specOptions.push({
        value: `${key}: ${value}`,
        label: `${key}: ${value}`
      })
    }
  })

  return specOptions
})

// 监听弹窗显示状态
watch(() => props.open, (newVal) => {
  visible.value = newVal
  if (newVal) {
    initModal()
  }
})

watch(visible, (newVal) => {
  emit('update:open', newVal)
})

/**
 * 监听规格选择变化
 * 当用户选择规格时，自动同步数据到formData.产品规格
 * 确保提交时规格数据不为null
 */
watch([selectedSpecArray, selectedSpecs], ([newSpecArray, newSpecs]) => {
  if (!selectedProduct.value) return

  let specsData = null
  
  // 处理数组格式的规格选择
  if (Array.isArray(selectedProduct.value.产品规格) || 
      (selectedProduct.value.产品规格?.规格列表 && Array.isArray(selectedProduct.value.产品规格.规格列表))) {
    if (newSpecArray && newSpecArray.length > 0) {
      specsData = JSON.stringify(newSpecArray)
    }
  } else {
    // 处理对象格式的规格选择
    if (newSpecs && Object.keys(newSpecs).length > 0) {
      specsData = sampleService.格式化产品规格(newSpecs)
    }
  }
  
  // 同步到formData，确保提交时有正确的规格数据
  formData.产品规格 = specsData
  
  // 调试日志
  console.log('🔄 规格选择变化同步:', {
    selectedSpecArray: newSpecArray,
    selectedSpecs: newSpecs,
    同步到formData的规格: specsData
  })
}, { deep: true })

// 初始化弹窗
const initModal = async () => {
  console.log('🚀 初始化寄样申请弹窗')
  console.log('📋 传入的达人信息完整数据结构:', JSON.stringify(props.talentInfo, null, 2))

  // 详细分析达人数据字段
  console.log('🔍 达人基础信息字段分析:', {
    昵称: props.talentInfo?.昵称,
    nickname: props.talentInfo?.nickname,
    平台: props.talentInfo?.平台,
    platform: props.talentInfo?.platform,
    粉丝数: props.talentInfo?.粉丝数,
    follower_count: props.talentInfo?.follower_count,
    联系方式: props.talentInfo?.联系方式,
    contact: props.talentInfo?.contact,
    达人id: props.talentInfo?.达人id,
    id: props.talentInfo?.id,
    关联id: props.talentInfo?.关联id
  })

  // 详细分析联系人信息
  console.log('📞 联系人信息字段分析:', {
    用户联系人表id: props.talentInfo?.用户联系人表id,
    联系人姓名: props.talentInfo?.联系人姓名,
    我的联系方式列表: props.talentInfo?.我的联系方式列表
  })

  formData.达人id = props.talentInfo?.达人id || props.talentInfo?.id
  resetForm()
  // 并行加载产品列表和当前联系人地址
  await Promise.all([
    loadUserProducts(),
    加载当前联系人地址()
  ])
}

// 重置表单
const resetForm = () => {
  formData.产品id = null
  formData.产品规格 = null
  formData.数量 = 1
  formData.收件人= ''
  formData.收件地址 = ''
  formData.收件电话 = ''
  formData.申请备注 = ''
  formData.选中地址索引 = null
  selectedProduct.value = null
  selectedSpecs.value = {}
  selectedSpecArray.value = []
  // 清空地址相关数据
  当前联系人地址选项.value = []
  完整联系人列表.value = []

  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 加载用户产品列表
const loadUserProducts = async () => {
  try {
    productsLoading.value = true
    const response = await sampleService.获取用户产品列表({
      页码: 1,
      每页数量: 100
    })
    
    if (response.status === 100) {
      // 后端返回的数据结构中，产品列表在 data.列表 字段中
      productList.value = response.data.列表 || []
      console.log('产品列表加载成功:', productList.value)
    } else {
      message.error(response.message || '获取产品列表失败')
    }
  } catch (error) {
    console.error('加载产品列表失败:', error)
    message.error('加载产品列表失败')
  } finally {
    productsLoading.value = false
  }
}

/**
 * 加载当前联系人的地址列表
 */
const 加载当前联系人地址 = async () => {
  try {
    // 从达人详情的联系方式列表中获取联系人ID
    let 联系人id = null

    const contactList = props.talentInfo?.我的联系方式列表 || []
    if (contactList.length > 0) {
      const firstContact = contactList[0]
      const supplementList = firstContact.补充联系方式列表 || []
      if (supplementList.length > 0) {
        // 这里需要获取用户联系人表id，但当前数据结构中没有
        // 暂时使用兜底逻辑
      }
    }

    // 兜底逻辑：尝试多种方式获取联系人ID
    联系人id = props.talentInfo?.用户联系人表id || props.talentInfo?.contactInfo?.用户联系人表id

    console.log('🔍 尝试获取联系人ID:', {
      从talentInfo直接获取: props.talentInfo?.用户联系人表id,
      从contactInfo获取: props.talentInfo?.contactInfo?.用户联系人表id,
      最终使用的联系人id: 联系人id
    })

    if (!联系人id) {
      console.warn('⚠️ 当前记录没有关联的联系人ID')
      return
    }

    // 使用指定联系人ID查询，只返回当前联系人的信息
    const response = await productService.获取样品申请联系人列表({
      关键词: '',
      用户联系人表id: 联系人id
    })

    if (response.status === 100) {
      const 联系人列表 = response.data || []

      // 存储完整的联系人列表数据（现在应该只有一个联系人）
      完整联系人列表.value = 联系人列表

      if (联系人列表.length > 0) {
        const 当前联系人 = 联系人列表[0]  // 直接取第一个（也是唯一的）联系人

        if (当前联系人 && 当前联系人.地址选项) {
          当前联系人地址选项.value = 当前联系人.地址选项

          // 如果只有一个地址，自动选择并填充表单
          if (当前联系人.地址选项.length === 1) {
            formData.选中地址索引 = 0
            处理地址选择变化(0)
          }

          console.log('✅ 当前联系人地址加载成功:', 当前联系人地址选项.value)
          console.log('✅ 当前联系人信息:', {
            用户联系人id: 当前联系人.用户联系人id,
            姓名: 当前联系人.姓名,
            地址数量: 当前联系人.地址选项.length
          })
        } else {
          console.warn('⚠️ 当前联系人没有地址信息')
        }
      } else {
        console.warn('⚠️ 未找到指定的联系人信息')
      }
    } else {
      console.error('获取联系人地址失败:', response.message)
    }
  } catch (error) {
    console.error('❌ 获取联系人地址失败:', error)
  }
}

/**
 * 处理地址选择变化
 */
const 处理地址选择变化 = (地址索引) => {
  if (地址索引 !== null && 当前联系人地址选项.value[地址索引]) {
    const 选中地址 = 当前联系人地址选项.value[地址索引]
    formData.收件人 = 选中地址.收件人
    formData.收件地址 = 选中地址.地址
    formData.收件电话 = 选中地址.电话
    console.log('📍 地址选择变化:', 选中地址)
  }
}

// 产品筛选
const filterProduct = (input, option) => {
  const product = productList.value.find(p => p.产品id === option.value)
  if (!product) return false
  
  const searchText = input.toLowerCase()
  return product.产品名称.toLowerCase().includes(searchText) ||
         (product.产品描述 && product.产品描述.toLowerCase().includes(searchText))
}

/**
 * 处理产品选择变化事件
 * 当用户在下拉框中选择了新的产品时触发此函数
 * @param {string|number} productId - 选中的产品id
 */
const handleProductChange = (productId) => {
  // 从产品列表中找到对应的产品对象
  selectedProduct.value = productList.value.find(p => p.产品id === productId)
  
  // 重置规格选择相关的状态
  selectedSpecs.value = {}        // 清空规格选择对象
  selectedSpecArray.value = []    // 清空规格数组
  formData.产品规格 = null        // 重置表单中的产品规格字段
}

// 格式化粉丝数
const formatFollowerCount = (count) => {
  if (!count) return '-'
  if (typeof count === 'string') return count
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + '万'
  }
  return count.toString()
}

// 获取联系人姓名
const getContactName = () => {
  // 由于现在使用指定联系人ID查询，完整联系人列表中应该只有一个联系人
  if (完整联系人列表.value.length > 0) {
    const 联系人 = 完整联系人列表.value[0]
    if (联系人 && 联系人.姓名) {
      return 联系人.姓名
    }
  }

  // 兜底逻辑：从达人信息中获取联系人姓名
  return props.talentInfo?.联系人姓名 || '未知联系人'
}

// 提交申请
const handleSubmit = async () => {
  try {
    // 验证表单
    await formRef.value.validate()
    
    // 验证产品选择
    if (!formData.产品id) {
      message.error('请选择要寄送的产品')
      return
    }
    
    loading.value = true
    
    /**
     * 准备规格数据
     * 优先从用户当前选择中获取，确保格式正确
     */
    let specsData = null

    // 优先从用户当前选择中获取规格数据，确保格式正确
    if (hasProductSpecs.value) {
      if (Array.isArray(selectedProduct.value.产品规格) ||
          (selectedProduct.value.产品规格?.规格列表 && Array.isArray(selectedProduct.value.产品规格.规格列表))) {
        // 新的数组格式：使用选中的规格数组
        if (selectedSpecArray.value.length > 0) {
          specsData = selectedSpecArray.value.join(', ')
          // 同步到formData以备下次使用
          formData.产品规格 = specsData
        }
      } else {
        // 兼容旧的对象格式
        if (Object.keys(selectedSpecs.value).length > 0) {
          specsData = sampleService.格式化产品规格(selectedSpecs.value)
          // 同步到formData以备下次使用
          formData.产品规格 = specsData
        }
      }
    }

    // 如果没有从用户选择中获取到数据，才使用formData中的数据
    if (!specsData && formData.产品规格) {
      specsData = formData.产品规格
    }

    // 调试日志：记录规格数据处理情况
    console.log('🔧 规格数据处理调试信息:', {
      产品有预定义规格: hasProductSpecs.value,
      产品规格类型: selectedProduct.value?.产品规格 ? 
        (Array.isArray(selectedProduct.value.产品规格) ? '数组格式' : '对象格式') : '无规格',
      formData中的产品规格: formData.产品规格,
      用户选择的规格数组: selectedSpecArray.value,
      用户选择的规格对象: selectedSpecs.value,
      最终提交的规格数据: specsData,
      数据来源: formData.产品规格 ? 'formData同步数据' : 
               (specsData ? '临时获取数据' : '无规格数据')
    })

    // 构建提交数据
    const submitData = {
      ...formData,
      产品规格: specsData,  // 确保规格数据正确传递
      用户联系人表id: props.talentInfo.contactInfo?.用户联系人表id || null  // 直接使用传递过来的UUID
    }

    console.log('📤 提交寄样申请数据:', submitData)
    
    const response = await sampleService.创建寄样申请(submitData)
    
    if (response.status === 100) {
      // 根据后端返回的审核状态显示相应消息
      const successMessage = response.message || '寄样申请提交成功'
      message.success(successMessage)
      emit('success')
      handleCancel()
    } else {
      message.error(response.message || '提交申请失败')
    }
  } catch (error) {
    console.error('提交寄样申请失败:', error)
    if (error.errorFields) {
      // 表单验证错误
      return
    }
    message.error('提交申请失败')
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  visible.value = false
  resetForm()
}
</script>

<style scoped>
.sample-application-form {
  max-height: 600px;
  overflow-y: auto;
}

.sample-application-form h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: #333;
  font-weight: 600;
  font-size: 14px;
}

.talent-info-section,
.product-selection-section,
.spec-selection-section,
.quantity-selection-section,
.contact-info-section,
.recipient-info-section {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.contact-info-display {
  background: #fafafa;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.single-address-info {
  margin-top: 12px;
}

.recipient-info-section {
  border-bottom: none;
  margin-bottom: 0;
}

/* 产品选择样式 */
.product-option {
  padding: 4px 0;
}

.product-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.product-desc {
  font-size: 12px;
  color: #999;
  line-height: 1.2;
}

/* 数量选择样式 */
.quantity-selection-section .ant-input-number {
  margin-right: 12px;
}

.quantity-hint {
  color: #999;
  font-size: 12px;
}

/* 规格选择样式 */
.spec-options {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
}

.spec-array-selection {
  width: 100%;
}

.spec-description {
  margin-bottom: 12px;
}

.spec-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.spec-checkbox {
  margin: 0;
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  transition: all 0.2s;
}

.spec-checkbox:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.spec-checkbox.ant-checkbox-wrapper-checked {
  border-color: #1890ff;
  background: #e6f7ff;
}

.spec-object-selection {
  width: 100%;
}

.spec-category {
  margin-bottom: 16px;
}

.spec-category:last-child {
  margin-bottom: 0;
}

.spec-category-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  font-size: 13px;
}

.spec-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.spec-radio {
  margin-right: 0;
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  transition: all 0.3s ease;
}

.spec-radio:hover {
  border-color: #1890ff;
}

.spec-radio.ant-radio-wrapper-checked {
  border-color: #1890ff;
  background: #e6f7ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sample-application-form {
    max-height: 500px;
  }

  .spec-radio-group {
    flex-direction: column;
  }

  .spec-radio {
    margin-bottom: 8px;
  }
}
</style>
