import asyncio

from 工具.限速器 import IP限速器实例  # 从迁移后的位置导入
# 假设这些全局实例和日志函数可以在此模块中被导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 记录系统关闭, 实时日志管理器, 应用日志器, 错误日志器


async def 清理应用资源(app_name: str, run_time: float):
    """处理应用关闭时的核心资源清理逻辑"""
    应用日志器.info(f"🔧 正在清理应用资源... (运行时长: {run_time:.2f}秒)")
    
    try:
        # 1. 清理LangChain系统资源
        try:
            # 清理LangChain相关服务
            try:
                from 服务.LangChain_智能体服务 import LangChain智能体服务实例
                if hasattr(LangChain智能体服务实例, '清理'):
                    await LangChain智能体服务实例.清理()
            except Exception:
                pass
            应用日志器.info("✅ LangChain系统资源清理完成")
        except Exception as e:
            应用日志器.warning(f"⚠️ LangChain系统资源清理失败: {str(e)}")
        
        # 2. 清理其他资源
        await IP限速器实例.关闭()
        await 实时日志管理器.关闭()
        
        # 优雅关闭数据库连接池
        try:
            await asyncio.wait_for(异步连接池实例.关闭连接池(), timeout=8.0)
        except asyncio.TimeoutError:
            错误日志器.warning("⚠️  数据库连接池优雅关闭超时，执行强制关闭")
            try:
                await asyncio.wait_for(异步连接池实例.关闭连接池(), timeout=3.0)
            except Exception as e:
                错误日志器.error(f"❌ 强制关闭数据库连接池失败: {e}")
        except Exception as e:
            错误日志器.error(f"❌ 数据库连接池关闭错误: {e}")
            
        # 记录系统关闭信息 (正常关闭)
        记录系统关闭(app_name, 0, run_time)
        应用日志器.info("✅ 应用资源清理完成")
            
    except Exception as e:
        错误日志器.error(f"❌ 资源清理过程中发生错误: {e}", exc_info=True)
        # 即使清理出错，也尝试记录系统关闭 (异常关闭)
        记录系统关闭(app_name, 1, run_time) 