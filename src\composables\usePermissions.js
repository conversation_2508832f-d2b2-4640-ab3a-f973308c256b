import { ref, reactive } from 'vue'
import teamService from '../services/team'

/**
 * 权限管理组合函数
 * 提供统一的权限检查、角色管理和权限配置功能
 */
export function usePermissions() {
  // 权限缓存
  const permissionCache = reactive(new Map())
  const permissionCategories = ref([])
  const teamRoles = ref([])
  
  // 角色类型映射
  const roleTypeMap = {
    '创建者': 'founder',
    '创始人': 'founder',
    '负责人': 'leader',
    '管理员': 'admin',
    '成员': 'member'
  }
  
  // 简化的权限层级定义（基于三个固定角色）
  const permissionHierarchy = {
    // 基础权限
    'team_view': ['创建者', '负责人', '成员'],
    'team_manage': ['创建者', '负责人'],
    
    // 成员权限
    'member_invite': ['创建者', '负责人'],
    'member_manage': ['创建者', '负责人'],
    'member_remove': ['创建者', '负责人'],
    
    // 管理权限
    'permission_manage': ['创建者', '负责人'],
    'role_manage': ['创建者', '负责人'],
    
    // 高级权限（只有创建者）
    'team_transfer': ['创建者'],
    'team_dissolve': ['创建者'],
    
    // 兼容旧的权限代码
    'VIEW_MEMBERS': ['创建者', '负责人', '成员'],
    'INVITE_MEMBERS': ['创建者', '负责人'],
    'REMOVE_MEMBERS': ['创建者', '负责人'],
    'MANAGE_PERMISSIONS': ['创建者', '负责人'],
    'SET_ROLES': ['创建者', '负责人'],
    'TRANSFER_OWNERSHIP': ['创建者'],
    'DISSOLVE_TEAM': ['创建者'],
    'EDIT_TEAM': ['创建者', '负责人'],
    'MANAGE_FINANCE': ['创建者', '负责人'],
    'VIEW_DATA': ['创建者', '负责人', '成员'],
    'EXPORT_DATA': ['创建者', '负责人'],
    'SYSTEM_SETTINGS': ['创建者', '负责人']
  }
  
  /**
   * 检查用户是否拥有指定权限
   * @param {Object} team - 团队对象
   * @param {string} permissionCode - 权限代码
   * @param {Object} options - 额外选项
   * @returns {boolean} 是否拥有权限
   */
  const hasPermission = (team, permissionCode, options = {}) => {
    if (!team || !permissionCode) {
      console.warn('权限检查参数缺失:', { team, permissionCode })
      return false
    }
    
    console.log('🔍 权限检查开始:', {
      permissionCode,
      team团队id: team.团队id,
      team我的角色: team.我的角色,
      team权限状态: team.权限状态 ? '存在' : '不存在'
    })
    
    // 优先检查：创建者拥有所有权限
    const 是否创建者 = (
      team.我的角色 === '创始人' || 
      team.我的角色 === '创建者' || 
      team.我的角色类型 === 'founder' || 
      team.创建人id === team.当前用户id ||
      team.用户角色?.我的角色 === '创始人' ||
      team.用户角色?.我的角色 === '创建者' ||
      team.权限状态?.是否团队创建者 === true ||
      team.权限状态?.用户角色 === '创建者'
    )
    
    console.log('👑 创建者检查:', {
      是否创建者,
      team我的角色: team.我的角色,
      team我的角色类型: team.我的角色类型,
      权限状态是否团队创建者: team.权限状态?.是否团队创建者,
      权限状态用户角色: team.权限状态?.用户角色
    })
    
    if (是否创建者) {
      console.log('✅ 创建者拥有所有权限:', permissionCode)
      return true
    }
    
    // 优化：使用权限状态数据（来自专门的权限状态接口）
    if (team.权限状态) {
      const 权限状态 = team.权限状态
      
      // 检查是否为团队成员
      if (权限状态.是否团队成员 === false) {
        console.log('❌ 不是团队成员')
        return false
      }
      
      // 优化：统一使用中文权限代码和字段名，前后端保持一致
      const 快捷权限映射 = {
        // 中文权限代码（与后端保持一致）
        '邀请成员': 权限状态.能否邀请成员,
        '移除成员': 权限状态.能否移除成员,
        '查看成员列表': 权限状态.能否查看成员,
        '管理成员角色': 权限状态.能否管理角色,
        '权限管理': 权限状态.能否管理权限,
        '编辑团队信息': 权限状态.能否编辑团队,
        '删除团队': 权限状态.能否删除团队,
        '查看团队统计': 权限状态.能否查看统计,
        '邀请管理': 权限状态.能否管理邀请,
        '查看团队信息': 权限状态.能否查看团队,
      }
      
      // 如果有快捷权限映射，直接返回结果
      if (快捷权限映射[permissionCode] !== undefined) {
        const hasQuickPermission = 快捷权限映射[permissionCode]
        console.log(`权限检查 ${permissionCode}:`, hasQuickPermission, '(快捷权限)')
        return hasQuickPermission
      }
      
      // 检查权限列表（使用中文权限名称）
      if (权限状态.权限列表 && Array.isArray(权限状态.权限列表)) {
        // 直接检查中文权限名称
        if (权限状态.权限列表.includes(permissionCode)) {
          console.log(`权限检查 ${permissionCode}: true (权限列表匹配)`)
          return true
        }
      }
    }
    
    // 降级到传统权限检查逻辑（兼容性保留）
    // 基础角色权限检查
    const roleType = team.我的角色类型 || team.用户角色?.我的角色类型 || roleTypeMap[team.我的角色] || 'member'
    const allowedRoles = permissionHierarchy[permissionCode]
    
    if (allowedRoles && allowedRoles.includes(roleType)) {
      console.log(`权限检查 ${permissionCode}: true (角色权限匹配)`)
      return true
    }
    
    console.log(`权限检查 ${permissionCode}: false (无匹配权限)`)
    return false
  }
  
  /**
   * 批量检查权限
   * @param {Object} team - 团队对象
   * @param {Array} permissionCodes - 权限代码数组
   * @returns {Object} 权限检查结果对象
   */
  const batchCheckPermissions = (team, permissionCodes) => {
    const result = {}
    permissionCodes.forEach(code => {
      result[code] = hasPermission(team, code)
    })
    return result
  }
  
  /**
   * 检查是否可以执行某个操作（使用中文操作名称）
   * @param {Object} team - 团队对象
   * @param {string} action - 操作类型（中文）
   * @returns {boolean} 是否可以执行
   */
  const canPerformAction = (team, action) => {
    const actionPermissionMap = {
      '查看成员': '查看成员列表',
      '邀请成员': '邀请成员',
      '移除成员': '移除成员',
      '管理权限': '管理成员角色',
      '设置角色': '管理成员角色',
      '转移所有权': '删除团队',
      '解散团队': '删除团队',
      '编辑团队': '编辑团队信息',
      '查看数据': '查看团队统计',
      '导出数据': '查看团队统计'
    }
    
    const permissionCode = actionPermissionMap[action]
    return permissionCode ? hasPermission(team, permissionCode) : false
  }
  
  /**
   * 获取角色显示信息
   * @param {string} roleType - 角色类型
   * @param {string} customRoleName - 自定义角色名称
   * @returns {Object} 角色显示信息
   */
  const getRoleDisplayInfo = (roleType, customRoleName = '') => {
    const roleInfo = {
      founder: { name: '创始人', color: 'gold', icon: '👑' },
      leader: { name: '负责人', color: 'orange', icon: '🎯' },
      member: { name: '成员', color: 'default', icon: '👤' },
      '创建者': { name: '创建者', color: 'gold', icon: '👑' },
      '负责人': { name: '负责人', color: 'orange', icon: '🎯' },
      '成员': { name: '成员', color: 'default', icon: '👤' }
    }
    
    return roleInfo[roleType] || roleInfo.member
  }
  
  /**
   * 加载权限配置
   * @returns {Promise<Array>} 权限分类列表
   */
  const loadPermissionCategories = async () => {
    try {
      const response = await teamService.getPermissionCategories()
      if (response.status === 100) {
        permissionCategories.value = response.message || []
        return permissionCategories.value
      }
    } catch (error) {
      console.error('加载权限配置失败:', error)
    }
    return []
  }
  
  /**
   * 获取固定角色列表
   * @returns {Array} 固定角色列表
   */
  const getFixedRoles = () => {
    return [
      { type: 'leader', name: '负责人', icon: '🎯', color: 'orange' },
      { type: 'member', name: '成员', icon: '👤', color: 'default' }
    ]
  }
  
  /**
   * 检查用户权限（异步，从后端获取最新权限）
   * @param {number} teamId - 团队id
   * @param {string} permissionCode - 权限代码
   * @param {number} userId - 用户id（可选）
   * @returns {Promise<boolean>} 是否拥有权限
   */
  const checkPermissionAsync = async (teamId, permissionCode, userId = null) => {
    const cacheKey = `${teamId}-${userId || 'current'}-${permissionCode}`
    
    // 检查缓存
    if (permissionCache.has(cacheKey)) {
      return permissionCache.get(cacheKey)
    }
    
    try {
      const response = await teamService.hasPermission({
        团队id: teamId,
        权限代码: permissionCode,
        用户id: userId
      })
      
      const hasAuth = response.status === 100 && response.message === true
      
      // 缓存结果（5分钟）
      permissionCache.set(cacheKey, hasAuth)
      setTimeout(() => {
        permissionCache.delete(cacheKey)
      }, 5 * 60 * 1000)
      
      return hasAuth
    } catch (error) {
      console.error('检查权限失败:', error)
      return false
    }
  }
  
  /**
   * 批量检查权限（异步）
   * @param {number} teamId - 团队id
   * @param {Array} permissionCodes - 权限代码数组
   * @param {number} userId - 用户id（可选）
   * @returns {Promise<Object>} 权限检查结果
   */
  const batchCheckPermissionsAsync = async (teamId, permissionCodes, userId = null) => {
    try {
      const response = await teamService.batchCheckPermissions({
        团队id: teamId,
        权限代码列表: permissionCodes,
        用户id: userId
      })
      
      if (response.status === 100) {
        return response.message || {}
      }
    } catch (error) {
      console.error('批量检查权限失败:', error)
    }
    
    // 降级到单个检查
    const result = {}
    for (const code of permissionCodes) {
      result[code] = await checkPermissionAsync(teamId, code, userId)
    }
    return result
  }

  /**
   * 获取用户团队权限状态（新方法 - 推荐使用）
   * 专为前端权限控制设计，返回完整的权限状态映射和角色信息
   * @param {number} teamId - 团队id
   * @param {number} userId - 用户id（可选）
   * @returns {Promise<Object>} 权限状态对象，包含权限映射、角色信息、特殊权限等
   */
  const getUserTeamPermissionStatus = async (teamId, userId = null) => {
    try {
      console.log('🔍 开始获取用户团队权限状态:', { teamId, userId })
      
      // 检查缓存
      const cacheKey = `${teamId}_${userId || 'current'}`
      if (permissionCache.has(cacheKey)) {
        console.log('📋 使用缓存的权限状态')
        return permissionCache.get(cacheKey)
      }
      
      const response = await teamService.getUserTeamPermissionStatus({
        团队id: teamId,
        用户id: userId
      })
      
      console.log('🔒 权限状态API响应:', {
        status: response.status,
        hasData: !!response.data,
        dataKeys: response.data ? Object.keys(response.data) : [],
        responseData: response.data
      })
      
      if ([100, 0, 1].includes(response.status)) {
        const permissionStatus = response.data || response.message
        
        // 详细分析权限状态数据
        console.log('✅ 权限状态详细信息:', {
          用户角色: permissionStatus.用户角色,
          是否团队创建者: permissionStatus.是否团队创建者,
          是否团队负责人: permissionStatus.是否团队负责人,
          是否团队成员: permissionStatus.是否团队成员,
          权限列表长度: permissionStatus.权限列表?.length,
          权限列表: permissionStatus.权限列表,
          快捷权限检查: {
            canInviteMembers: permissionStatus.canInviteMembers,
            canRemoveMembers: permissionStatus.canRemoveMembers,
            canManageRoles: permissionStatus.canManageRoles,
            canEditTeam: permissionStatus.canEditTeam,
            canDeleteTeam: permissionStatus.canDeleteTeam,
            canDissolve: permissionStatus.canDissolve
          }
        })
        
        if (permissionStatus) {
          // 存储到缓存
          permissionCache.set(cacheKey, permissionStatus)
          console.log('💾 权限状态已缓存')
          return permissionStatus
        } else {
          console.warn('⚠️ 权限状态数据为空')
          return null
        }
      } else {
        console.error('❌ 权限状态接口调用失败:', response.message)
        return null
      }
    } catch (error) {
      console.error('❌ 获取用户团队权限状态异常:', error)
      return null
    }
  }
  
  /**
   * 清除权限缓存
   * @param {number} teamId - 团队id（可选，不传则清除所有）
   */
  const clearPermissionCache = (teamId = null) => {
    if (teamId) {
      // 清除特定团队的缓存
      for (const [key] of permissionCache) {
        if (key.startsWith(`${teamId}-`) || key.includes(`_${teamId}_`)) {
          permissionCache.delete(key)
        }
      }
    } else {
      // 清除所有缓存
      permissionCache.clear()
    }
  }
  
  /**
   * 刷新权限缓存
   * @param {number} teamId - 团队id
   * @param {number} userId - 用户id（可选）
   */
  const refreshPermissionCache = async (teamId, userId = null) => {
    // 清除相关缓存
    clearPermissionCache(teamId)
    
    // 重新获取权限状态
    if (teamId) {
      try {
        return await getUserTeamPermissionStatus(teamId, userId)
      } catch (error) {
        console.error('刷新权限缓存失败:', error)
        return null
      }
    }
  }
  
  /**
   * 格式化权限错误信息
   * @param {string} action - 操作名称
   * @returns {string} 错误信息
   */
  const getPermissionErrorMessage = (action) => {
    const messages = {
      'VIEW_MEMBERS': '您没有查看成员的权限',
      'INVITE_MEMBERS': '您没有邀请成员的权限',
      'REMOVE_MEMBERS': '您没有移除成员的权限',
      'MANAGE_PERMISSIONS': '您没有管理权限的权限',
      'SET_ROLES': '您没有设置角色的权限',
      'TRANSFER_OWNERSHIP': '只有创始人可以转移所有权',
      'DISSOLVE_TEAM': '只有创始人可以解散团队',
      'EDIT_TEAM': '您没有编辑团队的权限',
      'MANAGE_FINANCE': '您没有管理财务的权限'
    }
    
    return messages[action] || '您没有执行此操作的权限'
  }
  
  return {
    // 数据
    permissionCategories,
    teamRoles,
    
    // 同步权限检查
    hasPermission,
    batchCheckPermissions,
    canPerformAction,
    
    // 异步权限检查
    checkPermissionAsync,
    batchCheckPermissionsAsync,
    getUserTeamPermissionStatus,
    
    // 数据加载
    loadPermissionCategories,
    getFixedRoles,
    
    // 工具函数
    getRoleDisplayInfo,
    clearPermissionCache,
    refreshPermissionCache,
    getPermissionErrorMessage,
    
    // 常量
    roleTypeMap,
    permissionHierarchy
  }
}

// 导出权限代码常量
export const PERMISSIONS = {
  VIEW_MEMBERS: 'VIEW_MEMBERS',
  INVITE_MEMBERS: 'INVITE_MEMBERS',
  REMOVE_MEMBERS: 'REMOVE_MEMBERS',
  MANAGE_PERMISSIONS: 'MANAGE_PERMISSIONS',
  SET_ROLES: 'SET_ROLES',
  TRANSFER_OWNERSHIP: 'TRANSFER_OWNERSHIP',
  DISSOLVE_TEAM: 'DISSOLVE_TEAM',
  EDIT_TEAM: 'EDIT_TEAM',
  MANAGE_FINANCE: 'MANAGE_FINANCE',
  VIEW_DATA: 'VIEW_DATA',
  EXPORT_DATA: 'EXPORT_DATA',
  SYSTEM_SETTINGS: 'SYSTEM_SETTINGS'
}

// 导出角色类型常量
// 预设职位类型定义（基于数据库实际存储的职位字段）
export const POSITION_TYPES = {
  FOUNDER: '创始人',        // 创始人（团队创建者）
  LEADER: '负责人',         // 负责人（团队负责人）
  ADMIN: '管理员',          // 管理员
  MEMBER: '普通成员',        // 普通成员
  CUSTOM: '自定义职位'       // 自定义职位
} 