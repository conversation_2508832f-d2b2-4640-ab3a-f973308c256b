<template>
  <div class="team-performance">
    <!-- 时间筛选和操作栏 -->
    <div class="performance-toolbar">
      <div class="toolbar-left">
        <a-select
          v-model:value="selectedTeam"
          placeholder="选择团队"
          style="width: 200px"
          size="large"
          @change="handleTeamChange"
        >
          <a-select-option 
            v-for="team in teamOptions" 
            :key="team.value" 
            :value="team.value"
          >
            {{ team.label }}
          </a-select-option>
        </a-select>
        
        <a-radio-group 
          v-model:value="selectedTimeRange" 
          @change="handleTimeRangeChange"
          button-style="solid"
          size="large"
        >
          <a-radio-button 
            v-for="option in timeRangeOptions" 
            :key="option.value" 
            :value="option.value"
          >
            {{ option.label }}
          </a-radio-button>
        </a-radio-group>
      </div>
      
      <div class="toolbar-right">
        <a-button 
          type="primary" 
          :icon="h(ShareAltOutlined)"
          @click="handleShare"
          size="large"
        >
          分享报告
        </a-button>
        
        <a-button 
          :icon="h(DownloadOutlined)"
          @click="handleExport"
          :loading="exportLoading"
          size="large"
        >
          导出数据
        </a-button>
      </div>
    </div>

    <!-- 团队概览卡片区域 -->
    <div class="team-metrics-cards">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :lg="8" :xl="5">
          <MetricCard
            title="团队总销售额"
            :value="teamData.总销售额"
            :growth="teamSalesGrowth"
            icon="DollarOutlined"
            color="#1890ff"
            :loading="loading"
          />
        </a-col>
        
        <a-col :xs="24" :sm="12" :lg="8" :xl="5">
          <MetricCard
            title="参与成员数"
            :value="teamData.参与成员数"
            icon="TeamOutlined"
            color="#52c41a"
            :loading="loading"
            suffix="人"
            format-type="number"
          />
        </a-col>
        
        <a-col :xs="24" :sm="12" :lg="8" :xl="5">
          <MetricCard
            title="人均销售额"
            :value="avgSalesPerMember"
            :growth="avgSalesGrowth"
            icon="UserOutlined"
            color="#faad14"
            :loading="loading"
          />
        </a-col>
        
        <a-col :xs="24" :sm="12" :lg="8" :xl="4">
          <MetricCard
            title="团队效率"
            :value="teamData.团队效率 || 0"
            icon="ThunderboltOutlined"
            color="#722ed1"
            :loading="loading"
            suffix="%"
            format-type="percentage"
          />
        </a-col>
        
        <a-col :xs="24" :sm="12" :lg="8" :xl="5">
          <MetricCard
            title="合作店铺数"
            :value="teamData.合作店铺数"
            icon="ShopOutlined"
            color="#13c2c2"
            :loading="loading"
            suffix="家"
            format-type="number"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 图表分析区域 -->
    <div class="team-charts-section">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :lg="12">
          <a-card title="团队销售趋势" class="chart-card">
            <SalesTrendChart
              :data="teamTrendData"
              :loading="loading"
              height="300px"
            />
          </a-card>
        </a-col>
        
        <a-col :xs="24" :lg="12">
          <a-card title="成员贡献排行" class="chart-card">
            <MemberContributionChart
              :data="memberContributionData"
              :loading="loading"
              height="300px"
            />
          </a-card>
        </a-col>
        
        <a-col :xs="24" :lg="12">
          <a-card title="店铺合作分析" class="chart-card">
            <StoreCollaborationChart
              :data="storeCollaborationData"
              :loading="loading"
              height="300px"
            />
          </a-card>
        </a-col>
        
        <a-col :xs="24" :lg="12">
          <a-card title="达人资源分布" class="chart-card">
            <TalentDistributionChart
              :data="talentDistributionData"
              :loading="loading"
              height="300px"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 团队成员业绩详情表格 -->
    <div class="team-members-section">
      <a-card title="团队成员业绩详情" class="table-card">
        <template #extra>
          <a-space>
            <a-input-search
              v-model:value="searchKeyword"
              placeholder="搜索成员..."
              @search="handleSearch"
              style="width: 200px"
            />
            <a-button 
              :icon="h(ReloadOutlined)"
              @click="refreshData"
              :loading="loading"
            >
              刷新
            </a-button>
          </a-space>
        </template>
        
        <TeamMembersTable
          :data="membersTableData"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          @member-click="handleMemberClick"
        />
      </a-card>
    </div>
  </div>
</template>

<script setup>
import {
    DownloadOutlined,
    ReloadOutlined,
    ShareAltOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, h, onMounted, reactive, ref } from 'vue'
import MemberContributionChart from '../../components/gvm/MemberContributionChart.vue'
import MetricCard from '../../components/gvm/MetricCard.vue'
import SalesTrendChart from '../../components/gvm/SalesTrendChart.vue'
import StoreCollaborationChart from '../../components/gvm/StoreCollaborationChart.vue'
import TalentDistributionChart from '../../components/gvm/TalentDistributionChart.vue'
import TeamMembersTable from '../../components/gvm/TeamMembersTable.vue'
import gvmService from '../../services/gvmService'

defineOptions({
  name: 'TeamPerformance'
})

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const selectedTeam = ref(null)
const selectedTimeRange = ref('30d')
const searchKeyword = ref('')

// 缓存优化
const teamDataCache = new Map()
let loadTeamDataTimer = null

// 团队数据
const teamData = reactive({
  总销售额: 0,
  参与成员数: 0,
  团队效率: 0,
  合作店铺数: 0,
  关联达人数: 0
})

// 图表数据
const teamTrendData = ref([])
const memberContributionData = ref([])
const storeCollaborationData = ref([])
const talentDistributionData = ref([])
const membersTableData = ref([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`
})

// 团队选项
const teamOptions = ref([])

// 时间范围选项
const timeRangeOptions = computed(() => gvmService.getTimeRangeOptions())

// 计算人均销售额
const avgSalesPerMember = computed(() => {
  if (teamData.参与成员数 === 0) return 0
  return teamData.总销售额 / teamData.参与成员数
})

// 增长率数据
const teamSalesGrowth = ref({ rate: 0, trend: 'stable', display: '0%' })
const avgSalesGrowth = ref({ rate: 0, trend: 'stable', display: '0%' })

// 防抖加载团队数据
const loadTeamDataDebounced = () => {
  if (loadTeamDataTimer) {
    clearTimeout(loadTeamDataTimer)
  }

  loadTeamDataTimer = setTimeout(() => {
    loadTeamData()
  }, 300)
}

// 加载团队数据
const loadTeamData = async () => {
  if (!selectedTeam.value) return

  const cacheKey = `${selectedTeam.value}-${selectedTimeRange.value}`

  // 检查缓存
  if (teamDataCache.has(cacheKey)) {
    const cachedData = teamDataCache.get(cacheKey)
    Object.assign(teamData, cachedData.统计数据)
    generateChartData()
    return
  }

  loading.value = true
  try {
    const params = { 时间范围: selectedTimeRange.value }
    const response = await gvmService.getTeamStats(selectedTeam.value, params)

    if (response.status === 'success') {
      const data = response.data.统计数据
      Object.assign(teamData, data)

      // 缓存数据
      teamDataCache.set(cacheKey, { 统计数据: data, timestamp: Date.now() })

      generateChartData()
    } else {
      message.error(response.message || '获取团队数据失败')
    }
  } catch (error) {
    message.error('加载团队数据失败，请重试')
    console.error('加载团队数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 生成图表和成员数据
const generateChartData = () => {
  // 模拟团队趋势数据
  const days = 30
  teamTrendData.value = Array.from({ length: days }, (_, i) => ({
    date: new Date(Date.now() - (days - i - 1) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    sales: Math.floor(Math.random() * 50000) + 30000,
    orders: Math.floor(Math.random() * 200) + 100
  }))

  // 模拟成员贡献数据
  memberContributionData.value = Array.from({ length: teamData.参与成员数 || 8 }, (_, i) => ({
    id: i + 1,
    name: `成员${i + 1}`,
    sales: Math.floor(Math.random() * 100000) + 20000,
    orders: Math.floor(Math.random() * 100) + 20,
    commission: Math.floor(Math.random() * 10000) + 2000
  }))

  // 模拟成员表格数据
  membersTableData.value = memberContributionData.value.map((member, index) => ({
    ...member,
    avatar: null,
    role: index === 0 ? '团队负责人' : '团队成员',
    contribution: Math.floor(Math.random() * 30) + 5,
    status: Math.random() > 0.2 ? 'active' : 'inactive',
    salesGrowth: {
      rate: (Math.random() - 0.5) * 50,
      trend: Math.random() > 0.3 ? 'up' : 'down',
      display: `${Math.random() > 0.5 ? '+' : ''}${((Math.random() - 0.5) * 50).toFixed(1)}%`
    }
  }))

  pagination.total = membersTableData.value.length

  // 模拟店铺合作数据
  storeCollaborationData.value = Array.from({ length: 8 }, (_, i) => ({
    id: i + 1,
    name: `合作店铺${i + 1}`,
    orders: Math.floor(Math.random() * 200) + 50,
    sales: Math.floor(Math.random() * 200000) + 50000,
    collaborationDays: Math.floor(Math.random() * 365) + 30,
    commissionRate: Math.floor(Math.random() * 15) + 5,
    status: Math.random() > 0.2 ? 'active' : 'inactive'
  }))

  // 模拟达人分布数据
  talentDistributionData.value = [
    { category: '美妆达人', count: Math.floor(Math.random() * 20) + 10 },
    { category: '服装达人', count: Math.floor(Math.random() * 15) + 8 },
    { category: '数码达人', count: Math.floor(Math.random() * 12) + 5 },
    { category: '生活达人', count: Math.floor(Math.random() * 18) + 7 },
    { category: '其他', count: Math.floor(Math.random() * 10) + 3 }
  ]
}

// 事件处理
const handleTeamChange = () => {
  loadTeamDataDebounced()
}

const handleTimeRangeChange = () => {
  loadTeamDataDebounced()
}

const handleShare = () => {
  message.success('分享链接已复制到剪贴板')
}

const handleExport = async () => {
  exportLoading.value = true
  try {
    message.success('团队数据导出成功')
  } catch (error) {
    message.error('数据导出失败')
  } finally {
    exportLoading.value = false
  }
}

const handleSearch = () => {
  loadTeamData()
}

const refreshData = () => {
  loadTeamData()
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadTeamData()
}

const handleMemberClick = (member) => {
  console.log('点击成员:', member)
  // 可以跳转到成员详情页面
}

// 加载团队列表
const loadTeamOptions = async () => {
  try {
    const response = await gvmService.getUserTeams()

    if (response.status === 'success' && response.data) {
      teamOptions.value = response.data.map(team => ({
        label: team.团队名称 || `团队${team.团队id}`,
        value: team.团队id
      }))

      // 设置默认选中第一个团队
      if (teamOptions.value.length > 0) {
        selectedTeam.value = teamOptions.value[0].value
        loadTeamData()
      }
    }
  } catch (error) {
    console.error('加载团队列表失败:', error)
    message.error('加载团队列表失败')
  }
}

// 生命周期
onMounted(() => {
  loadTeamOptions()
})
</script>

<style scoped>
.team-performance {
  padding: 0;
}

.performance-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.team-metrics-cards {
  margin-bottom: 24px;
}

.team-charts-section {
  margin-bottom: 24px;
}

.chart-card,
.table-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
}

.team-members-section {
  margin-bottom: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .performance-toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
}
</style>
