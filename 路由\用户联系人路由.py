"""
用户联系人管理路由
"""

from fastapi import APIRouter, Depends

from 依赖项.认证 import 获取当前用户
from 数据模型.响应模型 import 统一响应模型
from 数据模型.用户联系人模型 import (
    查询用户联系人请求模型,
)
from 日志 import 错误日志器
from 服务.用户联系人服务 import 用户联系人服务实例
from 状态 import 通用 as 状态

# 创建路由器
用户联系人路由 = APIRouter(prefix="/user-contact", tags=["用户联系人管理"])


@用户联系人路由.post("/detail", summary="获取单个联系人详情")
async def 获取联系人详情接口(请求数据: dict, 当前用户: dict = Depends(获取当前用户)):
    """
    获取单个联系人详情

    功能说明：
    - 根据联系人ID查询联系人详情
    - 验证联系人是否属于当前用户
    - 返回联系人的完整信息包括寄样信息
    """
    try:
        from uuid import UUID

        # 从请求数据中获取联系人ID
        联系人id_str = 请求数据.get("用户联系人id")
        if not 联系人id_str:
            return 统一响应模型.失败(400, "缺少联系人ID")

        # 验证UUID格式
        try:
            联系人uuid = UUID(联系人id_str)
        except ValueError:
            return 统一响应模型.失败(400, "联系人ID格式无效")

        用户id = 当前用户["id"]

        # 调用服务层查询联系人详情
        结果 = await 用户联系人服务实例.查询单个联系人(联系人uuid, 用户id)

        if 结果["status"] == "success":
            return 统一响应模型.成功(结果["data"], "获取联系人详情成功")
        else:
            return 统一响应模型.失败(400, 结果["message"])

    except Exception as e:
        错误日志器.error(f"获取联系人详情接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"获取联系人详情失败: {str(e)}")


@用户联系人路由.post("/associate", summary="关联联系人到达人补充信息")
async def 关联联系人接口(请求数据: dict, 当前用户: dict = Depends(获取当前用户)):
    """
    将用户联系人关联到达人补充信息

    功能说明：
    - 更新用户达人补充信息表的用户联系人表id字段
    - 建立联系人与达人补充信息的关联关系
    """
    try:
        补充信息id = 请求数据.get("补充信息id")
        用户联系人id = 请求数据.get("用户联系人id")

        if not 补充信息id or not 用户联系人id:
            return 统一响应模型.失败(400, "补充信息id和用户联系人id不能为空")

        # 调用数据访问层关联联系人
        from 数据.达人补充信息数据访问层 import 达人补充信息数据访问实例

        关联成功 = await 达人补充信息数据访问实例.关联用户联系人(
            补充信息id, 用户联系人id
        )

        if 关联成功:
            return 统一响应模型.成功(None, "关联联系人成功")
        else:
            return 统一响应模型.失败(400, "未找到对应的补充信息记录")

    except Exception as e:
        错误日志器.error(f"关联联系人接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"关联联系人失败: {str(e)}")


@用户联系人路由.post("/create-and-associate", summary="创建联系人并关联到达人补充信息")
async def 创建联系人并关联接口(请求数据: dict, 当前用户: dict = Depends(获取当前用户)):
    """
    创建用户联系人并关联到达人补充信息（事务操作）

    功能说明：
    - 在事务中创建用户联系人并关联到达人补充信息
    - 确保事务一致性：如果关联失败，则不创建联系人
    - 避免创建孤立的联系人记录
    """
    try:
        用户id = 当前用户["id"]
        姓名 = 请求数据.get("姓名")
        补充信息id = 请求数据.get("补充信息id")

        if not 姓名 or not 姓名.strip():
            return 统一响应模型.失败(400, "联系人姓名不能为空")
        if not 补充信息id:
            return 统一响应模型.失败(400, "补充信息id不能为空")

        # 调用数据访问层创建联系人并关联
        from 数据.达人补充信息数据访问层 import 达人补充信息数据访问实例

        联系人数据 = await 达人补充信息数据访问实例.创建联系人并关联到补充信息(
            用户id, 姓名.strip(), 补充信息id
        )

        if 联系人数据:
            return 统一响应模型.成功(联系人数据, "创建联系人并关联成功")
        else:
            return 统一响应模型.失败(400, "创建联系人并关联失败")

    except Exception as e:
        错误日志器.error(f"创建联系人并关联接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"创建联系人并关联失败: {str(e)}")


@用户联系人路由.post("/list", summary="获取用户联系人列表")
async def 获取用户联系人列表接口(
    请求数据: 查询用户联系人请求模型, 当前用户: dict = Depends(获取当前用户)
):
    """
    获取当前用户的联系人列表

    功能说明：
    - 查询当前用户的所有联系人及关联的联系方式
    - 支持按姓名和联系方式搜索
    """
    try:
        用户id = 当前用户["id"]

        # 调用服务层查询联系人列表
        结果 = await 用户联系人服务实例.查询用户联系人列表(用户id, 请求数据.关键词)

        if 结果["status"] == "success":
            return 统一响应模型.成功(结果["data"], "获取联系人列表成功")
        else:
            return 统一响应模型.失败(400, 结果["message"])

    except Exception as e:
        错误日志器.error(f"获取用户联系人列表接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"获取联系人列表失败: {str(e)}")


@用户联系人路由.put("/update", summary="更新联系人信息")
async def 更新联系人接口(
    请求数据: dict,  # 暂时使用dict，因为更新用户联系人请求模型导入有问题
    当前用户: dict = Depends(获取当前用户),
):
    """
    更新联系人信息

    功能说明：
    - 根据请求中的联系人ID更新联系人信息
    - 支持更新姓名和寄样地址
    - 验证联系人是否属于当前用户
    """
    try:
        from uuid import UUID

        # 从请求数据中获取联系人ID
        联系人id_str = 请求数据.get("用户联系人id")
        if not 联系人id_str:
            return 统一响应模型.失败(400, "缺少联系人ID")

        # 验证UUID格式
        try:
            联系人uuid = UUID(联系人id_str)
        except ValueError:
            return 统一响应模型.失败(400, "联系人ID格式无效")

        用户id = 当前用户["id"]

        # 从请求数据中提取字段
        姓名 = 请求数据.get("姓名")
        寄样信息 = 请求数据.get("寄样信息")

        # 调用服务层更新联系人
        结果 = await 用户联系人服务实例.更新联系人(联系人uuid, 用户id, 姓名, 寄样信息)

        if 结果["status"] == "success":
            return 统一响应模型.成功(None, 结果["message"])
        else:
            return 统一响应模型.失败(400, 结果["message"])

    except Exception as e:
        错误日志器.error(f"更新联系人接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"更新联系人失败: {str(e)}")


@用户联系人路由.put("/update-with-contact-info", summary="更新联系人和联系方式信息")
async def 更新联系人和联系方式接口(
    请求数据: dict,
    当前用户: dict = Depends(获取当前用户),
):
    """
    更新联系人信息和关联的联系方式信息

    功能说明：
    - 根据请求中的联系人ID更新联系人信息
    - 支持更新姓名、寄样地址和联系方式信息
    - 验证联系人是否属于当前用户
    """
    try:
        from uuid import UUID

        # 从请求数据中获取联系人ID
        联系人id_str = 请求数据.get("用户联系人id")
        if not 联系人id_str:
            return 统一响应模型.失败(400, "缺少联系人ID")

        # 验证UUID格式
        try:
            联系人uuid = UUID(联系人id_str)
        except ValueError:
            return 统一响应模型.失败(400, "联系人ID格式无效")

        用户id = 当前用户["id"]

        # 从请求数据中提取字段
        姓名 = 请求数据.get("姓名")
        寄样信息 = 请求数据.get("寄样信息")
        联系方式信息 = 请求数据.get("联系方式信息")

        # 调用服务层更新联系人和联系方式
        结果 = await 用户联系人服务实例.更新联系人和联系方式(
            联系人uuid, 用户id, 姓名, 寄样信息, 联系方式信息
        )

        if 结果["status"] == "success":
            return 统一响应模型.成功(None, 结果["message"])
        else:
            return 统一响应模型.失败(400, 结果["message"])

    except Exception as e:
        错误日志器.error(f"更新联系人和联系方式接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"更新联系人和联系方式失败: {str(e)}")


@用户联系人路由.delete("/delete", summary="删除联系人")
async def 删除联系人接口(
    请求数据: dict,  # 包含用户联系人id的请求数据
    当前用户: dict = Depends(获取当前用户),
):
    """
    删除联系人

    功能说明：
    - 根据用户联系人id删除联系人记录
    - 验证联系人是否属于当前用户
    - 数据库外键约束会自动处理关联关系
    """
    try:
        from uuid import UUID

        # 从请求数据中获取联系人ID
        联系人id_str = 请求数据.get("用户联系人id")
        if not 联系人id_str:
            return 统一响应模型.失败(400, "缺少联系人ID")

        # 验证UUID格式
        try:
            联系人uuid = UUID(联系人id_str)
        except ValueError:
            return 统一响应模型.失败(400, "联系人ID格式无效")

        用户id = 当前用户["id"]

        # 调用服务层删除联系人
        结果 = await 用户联系人服务实例.删除联系人(联系人uuid, 用户id)

        if 结果["status"] == "success":
            return 统一响应模型.成功(None, 结果["message"])
        else:
            return 统一响应模型.失败(400, 结果["message"])

    except Exception as e:
        错误日志器.error(f"删除联系人接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"删除联系人失败: {str(e)}")


@用户联系人路由.post(
    "/create-with-talent-association", summary="创建联系人并关联达人补充信息"
)
async def 创建联系人并关联达人补充信息接口(
    请求数据: dict,  # 使用dict以兼容前端现有数据结构
    当前用户: dict = Depends(获取当前用户),
):
    """
    创建联系人并关联达人补充信息（完整业务流程）

    核心业务逻辑：
    1. 创建联系人数据到联系人表
    2. 检查联系方式表中是否已存在相同的联系方式+类型组合
    3. 如果存在：获取该记录的ID，保存到用户达人补充信息表的联系方式表ID字段
    4. 如果不存在：创建新的联系方式记录，然后保存其ID到用户达人补充信息表
    5. 创建用户达人补充信息表数据
    6. 通过用户联系人表ID将上述两个表关联起来

    请求参数：
    - 姓名: 联系人姓名
    - 寄样信息: 寄样信息列表（可选）
    - 联系方式: 联系方式内容
    - 联系方式类型: 联系方式类型
    - 个人备注: 个人备注（可选）
    - 个人标签: 个人标签列表（可选）
    - 补充信息: 补充信息（可选）
    """
    try:
        用户id = 当前用户["id"]

        # 调用服务层创建联系人并关联达人补充信息
        结果 = await 用户联系人服务实例.创建联系人并关联达人补充信息(用户id, 请求数据)

        if 结果["status"] == "success":
            return 统一响应模型.成功(结果["data"], 结果["message"], 状态.成功)
        else:
            return 统一响应模型.失败(状态.参数错误, 结果["message"])

    except Exception as e:
        错误日志器.error(f"创建联系人并关联达人补充信息接口异常: {str(e)}")
        return 统一响应模型.失败(
            状态.服务器错误, f"创建联系人并关联达人补充信息失败: {str(e)}"
        )


@用户联系人路由.post("/search-talent-info", summary="搜索达人补充信息用于关联")
async def 搜索达人补充信息接口(请求数据: dict, 当前用户: dict = Depends(获取当前用户)):
    """
    搜索达人补充信息用于联系人关联

    功能说明：
    - 搜索当前用户的达人补充信息
    - 支持按联系方式、类型、备注等关键词搜索
    - 支持分页查询
    """
    try:
        用户id = 当前用户["id"]
        关键词 = 请求数据.get("关键词")
        页码 = 请求数据.get("页码", 1)
        每页数量 = 请求数据.get("每页数量", 20)

        # 调用服务层搜索达人补充信息
        结果 = await 用户联系人服务实例.搜索达人补充信息_用于关联(
            用户id, 关键词, 页码, 每页数量
        )

        if 结果["status"] == "success":
            return 统一响应模型.成功(结果["data"], 结果["message"])
        else:
            return 统一响应模型.失败(400, 结果["message"])

    except Exception as e:
        错误日志器.error(f"搜索达人补充信息接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"搜索达人补充信息失败: {str(e)}")
