"""
团队权限管理路由
负责处理团队权限相关接口（基于直接权限分配，无角色系统）
"""

from fastapi import APIRouter, Body, Depends

from 依赖项.认证 import 获取当前用户
from 工具.团队工具 import 标准化API响应
from 数据.团队权限数据 import 批量设置用户权限, 获取所有权限, 获取用户团队权限状态

# 权限相关模型暂时不使用

团队权限管理路由 = APIRouter(
    tags=["团队权限管理"],
)


@团队权限管理路由.post(
    "/check-user-team-permissions", summary="检查用户在指定团队的权限状态"
)
async def 用户团队权限状态(
    请求数据: dict = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取指定用户在特定团队中的详细权限状态，专为前端权限检查优化。
    """
    try:
        团队id = 请求数据.get("团队id")
        用户id = 请求数据.get("用户id") or 当前用户["id"]

        if not 团队id:
            return 标准化API响应(400, None, "缺少团队id参数")

        权限状态 = await 获取用户团队权限状态(团队id, 用户id)

        if not 权限状态:
            return 标准化API响应(40001, None, "无法获取权限状态，可能团队或用户不存在")

        return 标准化API响应(100, 权限状态)

    except Exception as e:
        return 标准化API响应(500, None, f"权限状态检查失败: {str(e)}")


@团队权限管理路由.post("/update-member-permissions", summary="更新单个成员权限")
async def 更新成员权限(
    请求数据: dict = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    更新单个团队成员的权限设置。
    """
    try:
        团队id = 请求数据.get("团队id")
        用户id = 请求数据.get("用户id")
        权限列表 = 请求数据.get("权限列表", [])

        if not all([团队id, 用户id]):
            return 标准化API响应(400, None, "缺少必需参数")

        # 使用批量设置权限函数
        结果 = await 批量设置用户权限(
            团队id=团队id,
            用户id=用户id,
            权限名称列表=权限列表,  # 将前端发来的"权限列表"作为"权限名称列表"参数传递
            授权人ID=当前用户["id"],
        )

        if 结果:
            return 标准化API响应(
                100, {"用户id": 用户id, "权限数量": len(权限列表)}, "权限更新成功"
            )
        else:
            return 标准化API响应(400, None, "权限更新失败")

    except Exception as e:
        return 标准化API响应(500, None, f"权限更新失败: {str(e)}")


@团队权限管理路由.post("/list", summary="获取所有可用权限列表")
async def 获取权限列表(当前用户: dict = Depends(获取当前用户)):
    """
    获取系统中所有可用的权限列表，并按分类进行组织，用于前端动态生成权限配置界面。
    """
    try:
        权限列表 = await 获取所有权限()

        if not 权限列表:
            return 标准化API响应(100, {}, "系统中暂无可用权限")

        # 按权限分类进行分组
        权限分组 = {}
        for 权限 in 权限列表:
            分类 = 权限.get("权限分类", "其他")
            if 分类 not in 权限分组:
                权限分组[分类] = []

            权限分组[分类].append(
                {
                    "权限名称": 权限["权限名称"],
                    "权限描述": 权限["权限描述"],
                }
            )

        return 标准化API响应(100, 权限分组, "获取权限列表成功")

    except Exception as e:
        return 标准化API响应(500, None, f"获取权限列表失败: {str(e)}")


# 其他权限相关接口功能暂时移除，仅保留基本的权限检查功能
