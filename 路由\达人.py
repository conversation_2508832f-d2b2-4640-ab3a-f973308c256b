import json
from typing import Any, Dict, List, Optional

import httpx
from fastapi import APIRouter, Body, Depends
from pydantic import BaseModel, Field

# 导入认证依赖
from 依赖项.认证 import 获取当前用户
from 数据.寄样申请数据访问层 import 寄样申请数据访问实例

# 导入数据访问层
from 数据.异步用户产品数据 import 异步用户产品数据

# 导入数据模型和响应模型
from 数据模型.响应模型 import 统一响应模型

# 导入统一日志系统
from 日志 import 接口日志器, 错误日志器

# 导入业务服务层 - 三层分离架构
from 服务.达人业务服务 import (
    达人业务处理服务实例,
    达人信息服务实例,
    达人补充信息服务实例,
)
from 服务.达人微信绑定服务 import 达人微信绑定服务实例
from 服务.达人服务 import (
    取消认领达人,
    搜索用户认领达人列表,
    更新达人信息,
    查询或更新达人,
    检查并插入达人到数据库,
    获取达人公海列表,
    获取达人详情,
    认领达人,
)
from 状态 import 状态


# 获取达人信息的辅助函数
async def 获取达人信息(
    达人id: Optional[int], 平台: str, 平台账号: Optional[str] = None
) -> dict:
    """
    根据达人id和平台获取达人信息

    参数:
        达人id: 达人的ID（可能为None）
        平台: 平台类型（抖音、微信）
        平台账号: 平台账号（当达人id为None时使用）

    返回:
        包含达人信息的字典
    """
    try:
        # 使用业务服务层获取达人信息
        结果 = await 达人信息服务实例.获取达人信息(达人id, 平台, 平台账号)

        if 结果["status"] == "success":
            return 结果["data"]
        else:
            错误日志器.error(f"获取达人信息失败: {结果.get('message', '未知错误')}")
            return {"昵称": "获取失败", "账号": "", "头像": "", "粉丝数": 0}

    except Exception as e:
        错误日志器.error(
            f"获取达人信息异常: 达人id={达人id}, 平台={平台}, 错误={str(e)}"
        )
        return {"昵称": "获取失败", "账号": "", "头像": "", "粉丝数": 0}


# 创建达人路由器
达人路由 = APIRouter(
    tags=["达人公海"],
    responses={404: {"description": "接口未找到"}},
)


# Pydantic模型


class 获取达人公海请求(BaseModel):
    页码: int = 1
    每页数量: int = 20  # 优化为每次加载20个达人
    最后id: Optional[int] = 0
    筛选条件: Optional[Dict[str, Any]] = None
    有联系方式: Optional[bool] = None
    关键词: Optional[str] = None
    uid: Optional[str] = None  # 新增：支持通过单个uid进行精准查询


class 获取达人详情请求(BaseModel):
    达人id: int


class 认领达人请求(BaseModel):
    达人id: int


class 取消认领达人请求(BaseModel):
    达人id: int


class 查询或更新达人请求(BaseModel):
    uid_number: str


class 更新达人信息请求(BaseModel):
    达人id: int
    sec_uid: Optional[str] = None
    uid_short: Optional[str] = None
    nickname: Optional[str] = None
    account_douyin: Optional[str] = None
    avatar: Optional[str] = None
    uid_number: Optional[str] = None
    introduction: Optional[str] = None
    粉丝数: Optional[int] = None
    关注数: Optional[int] = None
    账号状态: Optional[int] = None


class 获取我的认领达人请求(BaseModel):
    页码: int = 1
    每页数量: int = 20
    筛选条件: Optional[Dict[str, Any]] = None  # 可选的筛选条件
    关键词: Optional[str] = None  # 可选的搜索关键词
    补充信息筛选: Optional[str] = None  # 新增：补充信息筛选（"有补充信息"、"无补充信息"、""或None表示全部）


class 绑定达人微信号请求(BaseModel):
    """
    为已认领的达人绑定微信号
    """

    达人id: int
    微信号: str
    平台: Optional[str] = "微信"  # 平台类型，默认为微信


class 获取联系方式列表请求(BaseModel):
    """
    获取用户认领达人的联系方式列表
    """

    页码: int = 1
    每页数量: int = 20
    关键词: Optional[str] = None  # 搜索关键词（达人昵称、联系方式）
    平台: Optional[str] = None  # 平台筛选（抖音、微信）


class 添加达人请求(BaseModel):
    """
    添加达人请求模型 - 简化版本，只需要UID
    """

    UID: str = Field(..., description="达人uid，用于查询或创建达人记录")


class 搜索达人请求(BaseModel):
    """
    搜索达人请求模型 - 第一步：搜索第三方达人
    """

    抖音号: str = Field(..., description="达人抖音号，用于第三方搜索")
    选择的达人索引: Optional[int] = Field(None, description="选择的达人索引")
    第三方搜索结果: Optional[List[Dict[str, Any]]] = Field(
        None, description="第三方搜索结果"
    )


class 导入联系方式请求(BaseModel):
    """
    导入联系方式数据
    """

    平台类型: str  # '抖音' 或 '微信'
    导入数据: List[Dict[str, Any]]  # 导入的数据列表，包含补充信息JSON


class 添加补充联系方式请求(BaseModel):
    """
    添加补充联系方式
    """

    达人id: int
    联系方式: str
    联系方式类型: str = Field(
        ...,
        pattern="^(微信|手机|邮箱)$",
        description="联系方式类型，只支持：微信、手机、邮箱",
    )
    个人备注: Optional[str] = None
    个人标签: Optional[List[str]] = None
    补充信息: Optional[Dict[str, Any]] = None


class 更新补充联系方式请求(BaseModel):
    """
    更新补充联系方式
    注意：联系方式和联系方式类型一旦创建就不能修改，只能删除后重新添加
    """

    补充信息id: int
    个人备注: Optional[str] = None
    个人标签: Optional[List[str]] = None
    补充信息: Optional[Dict[str, Any]] = None
    微信信息表id: Optional[int] = Field(
        None, description="微信信息表id，用于关联微信信息"
    )


class 删除补充联系方式请求(BaseModel):
    """
    删除补充联系方式
    """

    补充信息id: int


class 获取用户产品列表请求(BaseModel):
    """
    获取用户产品列表请求（用于寄样申请）
    """

    页码: int = 1
    每页数量: int = 20
    产品名称: Optional[str] = None


class 创建寄样申请请求(BaseModel):
    """
    创建寄样申请请求
    """

    达人id: int
    产品id: int
    产品规格: Optional[str] = None  # JSON字符串，存储选中的规格信息
    数量: int = 1  # 申请数量，默认为1
    收件人: str
    收件地址: str
    收件电话: str
    申请备注: Optional[str] = None
    用户联系人表id: Optional[str] = Field(None, description="用户联系人表id（可选）")


@达人路由.post("/list")
async def 达人公海列表(
    请求: 获取达人公海请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取达人公海列表

    参数:
        页码: 当前页码，默认为1
        每页数量: 每页显示数量，默认为20
        最后id: 上一页最后的达人id，默认为0
        筛选条件: 可选的筛选条件，如粉丝数范围、直播销售额等
        有联系方式: 可选，筛选是否有联系方式的达人
        关键词: 可选，通过抖音号搜索达人

    返回:
        包含分页信息和达人列表的统一响应 (不含总数)
        认领状态基于团队维度判断，只显示当前团队内的认领状态
    """
    try:
        # 从认证中获取用户id
        用户id = 当前用户["id"]

        # 获取用户当前活跃的团队id - 使用业务服务层
        当前团队id = None
        try:
            # 使用业务服务层获取用户团队信息
            团队结果 = await 达人信息服务实例.获取用户团队信息(用户id)

            if 团队结果["status"] == "success" and 团队结果["data"]["团队id"]:
                当前团队id = 团队结果["data"]["团队id"]
                接口日志器.debug(
                    f"用户 {用户id} 使用团队模式查询达人公海，团队id: {当前团队id}"
                )
            else:
                接口日志器.warning(
                    f"用户 {用户id} 未加入任何团队，使用全局模式查询达人公海"
                )

        except Exception as team_error:
            # 团队查询失败，记录错误但不影响主功能
            错误日志器.warning(
                f"获取用户 {用户id} 团队信息失败，将使用兼容模式: {str(team_error)}"
            )

        # 获取达人列表 - 传递团队id以实现基于团队维度的认领状态判断
        结果 = await 获取达人公海列表(
            请求.页码,
            请求.每页数量,
            请求.最后id or 0,
            请求.筛选条件,
            请求.有联系方式,
            请求.关键词,
            用户id,
            当前团队id,
            请求.uid,  # 新增：传递uid参数进行精准查询
        )

        # 为响应添加团队信息（用于前端调试和用户体验优化）
        结果["团队模式"] = 当前团队id is not None
        结果["当前团队id"] = 当前团队id

        # 返回成功响应
        return 统一响应模型.成功(结果, "获取达人公海列表成功")
    except Exception as e:
        错误日志器.error(f"达人公海列表接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"获取达人公海列表失败: {str(e)}")


@达人路由.post("/detail")
async def 达人详情(
    请求: 获取达人详情请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取达人详情，包含联系方式信息和当前用户认领状态

    参数:
        达人id: 达人的唯一标识ID

    返回:
        包含达人详情、联系方式和认领状态的统一响应
    """
    try:
        # 从认证中获取用户id
        用户id = 当前用户["id"]

        # 获取达人详情（包含所有关联的联系方式）
        达人信息 = await 获取达人详情(请求.达人id, 用户id)

        # 返回成功响应
        return 统一响应模型.成功(达人信息, "获取达人详情成功")
    except ValueError as e:
        # 业务逻辑错误
        return 统一响应模型.失败(400, str(e))
    except Exception as e:
        错误日志器.error(f"达人详情接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"获取达人详情失败: {str(e)}")


@达人路由.post("/add-contact")
async def 添加补充联系方式(
    请求: 添加补充联系方式请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    添加补充联系方式 - 使用业务服务层
    """
    try:
        用户id = 当前用户["id"]

        # 验证联系方式类型
        允许的类型 = ["微信", "手机", "邮箱"]
        if 请求.联系方式类型 not in 允许的类型:
            return 统一响应模型.失败(
                400, f"不支持的联系方式类型，只支持: {', '.join(允许的类型)}"
            )

        # 使用业务服务层处理添加补充联系方式
        结果 = await 达人业务处理服务实例.添加补充联系方式_业务处理(
            用户id=用户id,
            达人id=请求.达人id,
            联系方式=请求.联系方式,
            联系方式类型=请求.联系方式类型,
            个人备注=请求.个人备注,
            个人标签=请求.个人标签,
            补充信息=请求.补充信息,
        )

        if 结果["status"] == "success":
            return 统一响应模型.成功(结果.get("data"), 结果["message"])
        else:
            return 统一响应模型.失败(400, 结果["message"])

    except Exception as e:
        错误日志器.error(f"添加补充联系方式异常: {str(e)}")
        return 统一响应模型.失败(500, f"添加补充联系方式失败: {str(e)}")


@达人路由.post("/update-contact")
async def 更新补充联系方式(
    请求: 更新补充联系方式请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    更新补充联系方式 - 使用业务服务层
    """
    try:
        用户id = 当前用户["id"]

        # 使用业务服务层检查权限
        权限结果 = await 达人补充信息服务实例.检查补充信息权限(请求.补充信息id, 用户id)

        if 权限结果["status"] != "success":
            return 统一响应模型.失败(403, "无权限修改该联系方式")

        # 使用数据访问层更新补充联系方式
        from 数据.达人补充信息数据访问层 import 达人补充信息数据访问

        更新结果 = await 达人补充信息数据访问.更新补充联系方式(
            请求.补充信息id,
            个人备注=请求.个人备注,
            个人标签=请求.个人标签,
            补充信息=请求.补充信息,
            微信信息表id=请求.微信信息表id,
        )

        if 更新结果:
            return 统一响应模型.成功(None, "更新补充联系方式成功")
        else:
            return 统一响应模型.失败(500, "更新补充联系方式失败")

    except Exception as e:
        错误日志器.error(f"更新补充联系方式异常: {str(e)}")
        return 统一响应模型.失败(500, f"更新补充联系方式失败: {str(e)}")


@达人路由.post("/delete-contact")
async def 删除补充联系方式(
    请求: 删除补充联系方式请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    删除补充联系方式 - 使用业务服务层
    """
    try:
        用户id = 当前用户["id"]

        # 使用业务服务层检查权限
        权限结果 = await 达人补充信息服务实例.检查补充信息权限(请求.补充信息id, 用户id)

        if 权限结果["status"] != "success":
            return 统一响应模型.失败(403, "无权限删除该联系方式")

        # 使用数据访问层删除补充联系方式
        from 数据.达人补充信息数据访问层 import 达人补充信息数据访问

        删除结果 = await 达人补充信息数据访问.删除补充联系方式(请求.补充信息id)

        if 删除结果:
            return 统一响应模型.成功(None, "删除补充联系方式成功")
        else:
            return 统一响应模型.失败(500, "删除补充联系方式失败")

    except Exception as e:
        错误日志器.error(f"删除补充联系方式异常: {str(e)}")
        return 统一响应模型.失败(500, f"删除补充联系方式失败: {str(e)}")


@达人路由.post("/claim")
async def 认领达人接口(
    请求: 认领达人请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    认领达人接口

    参数:
        达人id: 被认领达人的ID

    返回:
        操作结果的统一响应
    """
    try:
        # 从认证中获取用户id
        用户id = 当前用户["id"]

        # 执行认领操作
        await 认领达人(用户id, 请求.达人id)

        # 返回成功响应
        return 统一响应模型.成功(None, "认领达人成功")
    except ValueError as e:
        # 业务逻辑错误
        return 统一响应模型.失败(400, str(e))
    except Exception as e:
        错误日志器.error(f"认领达人接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"认领达人失败: {str(e)}")


@达人路由.post("/unclaim")
async def 取消认领达人接口(
    请求: 取消认领达人请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    取消认领达人接口

    参数:
        达人id: 被取消认领的达人id

    返回:
        操作结果的统一响应
    """
    try:
        # 从认证中获取用户id
        用户id = 当前用户["id"]

        # 执行取消认领操作
        await 取消认领达人(用户id, 请求.达人id)

        # 返回成功响应
        return 统一响应模型.成功(None, "取消认领达人成功")
    except ValueError as e:
        # 业务逻辑错误
        return 统一响应模型.失败(400, str(e))
    except Exception as e:
        错误日志器.error(f"取消认领达人接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"取消认领达人失败: {str(e)}")


@达人路由.post("/get_id")
async def 查询或更新达人接口(
    请求: 查询或更新达人请求 = Body(...), _当前用户: dict = Depends(获取当前用户)
):
    """
    通过uid_number查询或创建达人，并返回其系统内ID。
    不再接收或处理 account_douyin 参数。

    参数:
        uid_number: 达人唯一标识符 (通过外部平台获取的UserId)

    返回:
        包含达人id的统一响应
    """
    try:
        # 执行查询或更新操作，只传递 uid_number
        结果 = await 查询或更新达人(
            请求.uid_number
        )  # 调用服务层，不再传递 account_douyin

        # 根据结果状态返回响应
        if 结果["状态"] == "成功":
            # 只返回达人id，而不是整个结果对象
            return 统一响应模型.成功({"达人id": 结果["达人id"]}, 结果["消息"])
        else:
            # 即便操作失败（例如，数据库异常），也可能包含有用的消息
            return 统一响应模型.失败(
                400, 结果.get("消息", "查询或创建达人id时发生未知错误")
            )
    except Exception as e:
        错误日志器.error(f"查询或更新达人接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"查询或更新达人接口失败: {str(e)}")


@达人路由.post("/update")
async def 更新达人信息接口(
    请求: 更新达人信息请求 = Body(...), _当前用户: dict = Depends(获取当前用户)
):
    """
    更新达人信息

    参数:
        达人id: 达人的唯一标识ID(必填)
        sec_uid: 达人的sec_uid(可选)
        uid_short: 达人的短ID(可选)
        nickname: 达人昵称(可选)
        account_douyin: 达人抖音账号(可选)
        avatar: 达人头像URL(可选)
        uid_number: 达人uid(可选)
        introduction: 达人简介(可选)
        粉丝数: 粉丝数量(可选)
        关注数: 关注数量(可选)
        账号状态: 账号状态(可选)，1-已注销

    返回:
        包含操作结果的统一响应
    """
    try:
        # 构建更新数据字典
        更新数据 = {}
        for 字段 in [
            "sec_uid",
            "uid_short",
            "nickname",
            "account_douyin",
            "avatar",
            "uid_number",
            "introduction",
            "粉丝数",
            "关注数",
            "账号状态",
        ]:
            值 = getattr(请求, 字段, None)
            if 值 is not None:
                更新数据[字段] = 值

        # 执行更新操作
        结果 = await 更新达人信息(请求.达人id, 更新数据)

        # 根据结果状态返回响应
        if 结果["状态"] == "成功":
            return 统一响应模型.成功({"达人id": 结果["达人id"]}, 结果["消息"])
        else:
            return 统一响应模型.失败(400, 结果["消息"])
    except Exception as e:
        错误日志器.error(f"更新达人信息接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"更新达人信息失败: {str(e)}")


@达人路由.post("/myclaims")
async def 搜索我的认领达人(
    请求: 获取我的认领达人请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    搜索当前用户认领的达人列表，支持分页和筛选

    参数:
        页码: 当前页码，默认为1
        每页数量: 每页显示数量，默认为20
        筛选条件: 可选的筛选条件，如粉丝数范围、类别等
        关键词: 可选的搜索关键词，搜索达人昵称和抖音号

    返回:
        包含达人列表和分页信息的统一响应
    """
    try:
        # 从认证中获取用户id
        用户id = 当前用户["id"]

        # 获取用户认领的达人列表 - 移除排序参数，添加补充信息筛选
        结果 = await 搜索用户认领达人列表(
            用户id,
            请求.页码,
            请求.每页数量,
            "认领时间",  # 固定使用认领时间排序
            "desc",     # 固定使用降序
            请求.筛选条件,
            请求.关键词,
            请求.补充信息筛选,  # 新增：补充信息筛选参数
        )

        # 返回成功响应
        return 统一响应模型.成功(结果, "搜索认领达人列表成功")
    except ValueError as e:
        # 业务逻辑错误
        return 统一响应模型.失败(400, str(e))
    except Exception as e:
        错误日志器.error(f"搜索认领达人列表接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"搜索认领达人列表失败: {str(e)}")


@达人路由.post("/bind-wechat")
async def 绑定达人微信号(
    请求: 绑定达人微信号请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    为已认领的达人绑定微信号

    功能说明:
    1. 验证用户是否已认领该达人
    2. 在微信信息表中查询或创建微信号记录
    3. 创建达人商务微信记录
    4. 建立用户与达人商务微信的关联
    5. 更新达人补充信息中的微信号

    参数:
        达人id: 要绑定微信号的达人id
        微信号: 要绑定的微信号
        平台: 平台类型，默认为"微信"

    返回:
        绑定结果的统一响应
    """
    try:
        # 从认证中获取用户id
        用户id = 当前用户["id"]

        # 参数验证
        if not 请求.微信号 or not 请求.微信号.strip():
            return 统一响应模型.失败(400, "微信号不能为空")

        if not 请求.达人id or 请求.达人id <= 0:
            return 统一响应模型.失败(400, "达人id无效")

        # 调用微信绑定服务
        结果 = await 达人微信绑定服务实例.绑定达人微信号(
            用户id=用户id,
            达人id=请求.达人id,
            微信号=请求.微信号.strip(),
            平台=请求.平台 or "微信",
        )

        # 根据结果状态返回响应
        if 结果["状态"] == "成功":
            return 统一响应模型.成功(结果.get("数据", {}), 结果["消息"])
        else:
            return 统一响应模型.失败(400, 结果["消息"])

    except Exception as e:
        错误日志器.error(
            f"绑定达人微信号失败: 用户id={当前用户.get('id')}, 达人id={请求.达人id}, 微信号={请求.微信号}, 错误={str(e)}"
        )
        return 统一响应模型.失败(500, f"绑定微信号失败: {str(e)}")


@达人路由.post("/contact-list")
async def 获取联系方式列表接口(
    请求: 获取联系方式列表请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取用户认领达人的联系方式列表

    注意：此接口只返回有联系方式的达人记录

    参数:
        页码: 当前页码，默认为1
        每页数量: 每页显示数量，默认为20
        关键词: 搜索关键词（达人昵称、联系方式、个人备注）
        平台: 平台筛选（抖音、微信）

    返回:
        包含联系方式列表的统一响应
        抖音达人会额外返回uid_number字段

    性能优化建议：
        建议添加以下数据库索引以提高查询性能：
        1. CREATE INDEX idx_user_talent_userid_status ON 用户达人关联表(用户id, 状态);
        2. CREATE INDEX idx_user_talent_talentid_platform ON 用户达人关联表(达人id, 平台);
        3. CREATE INDEX idx_supplement_contact ON 用户达人补充信息表(联系方式);
        4. CREATE INDEX idx_supplement_userid ON 用户达人补充信息表(用户达人关联表id);
        5. CREATE INDEX idx_talent_nickname ON 达人表(nickname);
        6. CREATE INDEX idx_wechat_talent_nickname ON 微信达人表(昵称);
    """
    try:
        用户id = 当前用户["id"]

        # 使用数据访问层查询补充信息列表
        from 数据.达人补充信息数据访问层 import 达人补充信息数据访问

        查询结果 = await 达人补充信息数据访问.查询补充信息列表(
            用户id=用户id,
            页码=请求.页码,
            每页数量=请求.每页数量,
            关键词=请求.关键词,
            平台=请求.平台,
            联系方式类型=getattr(请求, "联系方式类型", None),
        )

        # 处理查询结果，获取达人信息
        data_result = 查询结果["列表"]
        for item in data_result:
            # 处理个人标签
            if item.get("个人标签"):
                try:
                    item["个人标签"] = (
                        json.loads(item["个人标签"])
                        if isinstance(item["个人标签"], str)
                        else item["个人标签"]
                    )
                except (json.JSONDecodeError, TypeError, ValueError):
                    item["个人标签"] = []
            else:
                item["个人标签"] = []

            # 处理补充信息
            if item.get("补充信息"):
                try:
                    item["补充信息"] = (
                        json.loads(item["补充信息"])
                        if isinstance(item["补充信息"], str)
                        else item["补充信息"]
                    )
                except (json.JSONDecodeError, TypeError, ValueError):
                    item["补充信息"] = {}
            else:
                item["补充信息"] = {}

            # 根据平台获取达人信息
            达人信息 = await 获取达人信息(
                item["达人id"], item["平台"], item.get("平台账号")
            )
            item.update(
                {
                    "达人昵称": 达人信息.get("昵称", ""),
                    "平台账号": 达人信息.get("账号", ""),
                    "头像": 达人信息.get("头像", ""),
                    "粉丝数": 达人信息.get("粉丝数", 0),
                }
            )

            # 如果是抖音达人，添加uid_number字段
            if item["平台"] == "抖音" and 达人信息.get("uid_number"):
                item["uid_number"] = 达人信息.get("uid_number", "")

        总页数 = (查询结果["总数"] + 请求.每页数量 - 1) // 请求.每页数量
        response_data = {
            "列表": data_result,
            "总数": 查询结果["总数"],
            "页码": 请求.页码,
            "每页数量": 请求.每页数量,
            "总页数": 总页数,
            "有下一页": 请求.页码 < 总页数,
        }

        return 统一响应模型.成功(response_data, "获取联系方式列表成功")

    except Exception as e:
        错误日志器.error(f"获取联系方式列表接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"获取联系方式列表失败: {str(e)}")


@达人路由.post("/sample/user-products")
async def 获取用户产品列表接口(
    请求: 获取用户产品列表请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取用户产品列表（用于寄样申请）

    参数:
        页码: 当前页码，默认为1
        每页数量: 每页显示数量，默认为20
        产品名称: 产品名称筛选（可选）

    返回:
        用户的产品列表
    """
    try:
        用户id = 当前用户["id"]

        # 调用产品数据层获取用户产品列表
        异步用户产品数据实例 = 异步用户产品数据()

        结果 = await 异步用户产品数据实例.获取用户产品列表(
            用户id=用户id,
            页码=请求.页码,
            每页数量=请求.每页数量,
            产品名称_筛选=请求.产品名称,
        )

        # 检查结果格式
        if isinstance(结果, tuple) and len(结果) >= 3:
            # 旧版返回格式 (成功标志, 数据, 消息)
            成功, 数据, 消息 = 结果
            if 成功:
                return 统一响应模型.成功(数据, "获取用户产品列表成功")
            else:
                return 统一响应模型.失败(500, 消息)
        elif isinstance(结果, dict):
            # 新版返回格式
            if 结果.get("status") == 100:
                return 统一响应模型.成功(结果.get("data", {}), "获取用户产品列表成功")
            else:
                return 统一响应模型.失败(
                    结果.get("status", 500), 结果.get("message", "获取用户产品列表失败")
                )
        else:
            return 统一响应模型.失败(500, "获取用户产品列表失败：返回格式异常")

    except Exception as e:
        错误日志器.error(f"获取用户产品列表接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"获取用户产品列表失败: {str(e)}")


@达人路由.post("/sample/create")
async def 创建寄样申请接口(
    请求: 创建寄样申请请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    创建寄样申请

    功能:
        - 验证达人认领关系
        - 验证产品所有权
        - 自动关联用户达人补充信息（如不存在则创建）
        - 创建寄样申请记录

    参数:
        达人id: 达人id
        产品id: 产品id
        产品规格: 产品规格信息（JSON字符串）
        数量: 申请数量
        收件人: 收件人姓名
        收件地址: 收件地址
        收件电话: 收件电话
        申请备注: 申请备注（可选）

    返回:
        创建结果，包含寄样申请ID
    """
    try:
        用户id = 当前用户["id"]

        # 使用业务服务层验证达人所有权
        验证结果 = await 达人信息服务实例.验证达人所有权(用户id, 请求.达人id)

        if 验证结果["status"] != "success":
            return 统一响应模型.失败(403, "您没有认领该达人，无法申请寄样")

        关联表id = 验证结果["data"]["关联表id"]

        # 使用数据访问层查询或创建补充信息记录
        from 数据.达人补充信息数据访问层 import 达人补充信息数据访问

        用户达人补充信息表id = await 达人补充信息数据访问.查询或创建补充信息记录(
            关联表id
        )

        if not 用户达人补充信息表id:
            return 统一响应模型.失败(500, "创建补充信息记录失败")

        # 使用数据访问层验证产品所有权
        产品验证通过 = await 寄样申请数据访问实例.验证产品所有权(请求.产品id, 用户id)

        if not 产品验证通过:
            return 统一响应模型.失败(404, "产品不存在或无权限操作")

        # 创建寄样申请记录

        申请结果 = await 寄样申请数据访问实例.创建统一寄样申请(
            收件人=请求.收件人,
            收件地址=请求.收件地址,
            收件电话=请求.收件电话,
            用户产品表id=请求.产品id,
            数量=请求.数量,
            产品规格=请求.产品规格 or "",
            申请备注=请求.申请备注,
            用户id=用户id,
            用户联系人表id=请求.用户联系人表id,
            团队审核状态=0,
            启用自动审批=True,
        )

        if 申请结果:
            寄样申请ID = 申请结果["申请ID"]
            是否自动审核 = 申请结果["是否自动审核"]
            审核状态文本 = 申请结果["审核状态文本"]

            成功消息 = f"寄样申请创建成功，{审核状态文本}"

            return 统一响应模型.成功(
                {
                    "寄样申请ID": 寄样申请ID,
                    "是否自动审核": 是否自动审核,
                    "审核状态": 审核状态文本,
                },
                成功消息,
            )
        else:
            return 统一响应模型.失败(500, "创建寄样申请失败")

    except Exception as e:
        错误日志器.error(f"创建寄样申请接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"创建寄样申请失败: {str(e)}")


class 获取用户寄样记录请求(BaseModel):
    """
    获取用户寄样记录请求
    """

    页码: int = 1
    每页数量: int = 20
    审核状态: Optional[int] = None  # 筛选审核状态


@达人路由.post("/sample/list")
async def 获取用户寄样记录接口(
    请求: 获取用户寄样记录请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取用户寄样记录列表

    功能:
        - 查询当前用户的所有寄样申请记录
        - 关联显示达人信息和产品信息
        - 支持按审核状态筛选
        - 支持分页查询

    参数:
        页码: 页码，默认为1
        每页数量: 每页记录数，默认为20
        审核状态: 审核状态筛选（可选）

    返回:
        寄样记录列表，包含达人信息和产品信息
    """
    try:
        用户id = 当前用户["id"]

        # 使用数据访问层查询寄样申请列表
        状态筛选 = None
        if 请求.审核状态 == 0:
            状态筛选 = "待审批"
        elif 请求.审核状态 == 1:
            状态筛选 = "已通过"
        elif 请求.审核状态 == 2:
            状态筛选 = "已拒绝"

        查询结果 = await 寄样申请数据访问实例.查询寄样申请列表(
            用户id=用户id, 页码=请求.页码, 每页数量=请求.每页数量, 状态筛选=状态筛选
        )

        # 处理查询结果，直接使用数据访问层返回的数据
        寄样记录 = 查询结果["列表"]

        # 构建响应数据
        总页数 = (查询结果["总数"] + 请求.每页数量 - 1) // 请求.每页数量
        response_data = {
            "列表": 寄样记录,
            "总数": 查询结果["总数"],
            "页码": 请求.页码,
            "每页数量": 请求.每页数量,
            "总页数": 总页数,
            "有下一页": 请求.页码 < 总页数,
        }

        return 统一响应模型.成功(response_data, "获取寄样记录列表成功")

    except Exception as e:
        错误日志器.error(f"获取用户寄样记录接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"获取寄样记录失败: {str(e)}")


@达人路由.post("/search-talent-pool")
async def 达人公海搜索接口(
    请求: 搜索达人请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    达人公海搜索接口 - 专门用于达人公海的关键词搜索

    功能特点：
    1. 使用关键词搜索第三方API获取达人列表
    2. 自动检查并插入新达人到数据库
    3. 返回中文键名格式的搜索结果
    4. 提供数据库操作统计信息

    参数:
        抖音号: 搜索关键词（可以是达人昵称、抖音号等）

    返回:
        包含搜索结果和数据库操作统计的统一响应
    """
    try:
        用户id = 当前用户["id"]
        搜索关键词 = 请求.抖音号.strip()  # 实际上是搜索关键词，不只是抖音号

        if not 搜索关键词:
            return 统一响应模型.失败(400, "搜索关键词不能为空")

        错误日志器.info(f"用户 {用户id} 开始达人公海关键词搜索: {搜索关键词}")

        # 步骤1：调用第三方API搜索
        外部达人搜索API地址 = "https://daduoduo.com/ajax/dyLiveDataAjax.ashx"
        params = {
            "action": "GetSearchTipForPeople",
            "keyword": 搜索关键词,
            "sortType": "1",
        }

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        async with httpx.AsyncClient(timeout=15.0) as client:
            try:
                response = await client.get(
                    外部达人搜索API地址, params=params, headers=headers
                )
                response.raise_for_status()
                搜索结果 = response.json()

                # 解析搜索结果
                is_success = (
                    搜索结果.get("code") == 0
                    or 搜索结果.get("msg") == "success"
                    or 搜索结果.get("status") == "success"
                    or "data" in 搜索结果
                )

                if not is_success:
                    return 统一响应模型.失败(404, "第三方搜索未找到匹配的达人")

                # 获取达人列表
                达人列表 = []
                if 搜索结果.get("data", {}).get("data"):
                    达人列表 = 搜索结果["data"]["data"]
                elif 搜索结果.get("data") and isinstance(搜索结果["data"], list):
                    达人列表 = 搜索结果["data"]
                elif 搜索结果.get("list"):
                    达人列表 = 搜索结果["list"]

                if not 达人列表:
                    return 统一响应模型.失败(404, f"未找到与 '{搜索关键词}' 相关的达人")

                # 步骤2：检查并插入达人到数据库，然后返回中文键名数据
                中文键名达人列表 = []
                插入结果统计 = {"新增": 0, "已存在": 0, "失败": 0}

                for 达人 in 达人列表:
                    uid_number = 达人.get("UserId", "")
                    nickname = 达人.get("Name", "")
                    avatar = 达人.get("HeaderImg", "")
                    follower_count = 达人.get("FansCnt", 0)

                    # 跳过无效的达人数据
                    if not uid_number or not nickname:
                        continue

                    # 检查并插入达人到数据库
                    数据库操作结果 = await 检查并插入达人到数据库(
                        uid_number=uid_number,
                        昵称=nickname,
                        avatar=avatar,
                        follower_count=follower_count,
                    )

                    # 统计插入结果
                    if 数据库操作结果["action"] == "inserted":
                        插入结果统计["新增"] += 1
                    elif 数据库操作结果["action"] == "found_existing":
                        插入结果统计["已存在"] += 1
                    else:
                        插入结果统计["失败"] += 1
                        错误日志器.warning(
                            f"达人数据库操作失败: {数据库操作结果['message']}"
                        )

                    # 转换键名为中文，保持原始数据结构
                    达人数据 = {
                        "uid_number": 达人.get("UserId", ""),
                        "nickname": 达人.get("Name", ""),
                        "avatar": 达人.get("HeaderImg", ""),
                        "follower_count": 达人.get("FansCnt", 0),
                    }
                    中文键名达人列表.append(达人数据)

                return 统一响应模型.成功(
                    {
                        "达人列表": 中文键名达人列表,
                        "搜索关键词": 搜索关键词,
                        "返回数量": len(中文键名达人列表),
                        "数据来源": "第三方API",
                    }
                )

            except httpx.TimeoutException:
                错误日志器.error(f"达人公海搜索超时: {搜索关键词}")
                return 统一响应模型.失败(504, "第三方搜索服务超时")
            except httpx.HTTPStatusError as e:
                错误日志器.error(
                    f"达人公海搜索HTTP错误: {e.response.status_code}, 搜索关键词: {搜索关键词}"
                )
                return 统一响应模型.失败(
                    e.response.status_code,
                    f"第三方搜索服务错误: {e.response.status_code}",
                )
            except Exception as e:
                错误日志器.error(
                    f"达人公海搜索异常: {str(e)}, 搜索关键词: {搜索关键词}"
                )
                return 统一响应模型.失败(503, f"第三方搜索服务异常: {str(e)}")

    except Exception as e:
        错误日志器.error(f"达人公海搜索接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"达人公海搜索失败: {str(e)}")


@达人路由.post("/save-talent-to-pool")
async def 保存达人到公海接口(
    请求: 添加达人请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    保存达人到公海接口 - 通过UID保存达人到数据库

    参数:
        UID: 达人uid，用于查询或创建达人记录

    返回:
        包含操作结果的统一响应
    """
    try:
        用户id = 当前用户["id"]
        UID = 请求.UID.strip()

        if not UID:
            return 统一响应模型.失败(400, "达人uid不能为空")

        错误日志器.info(f"用户 {用户id} 保存达人到公海: UID={UID}")

        # 调用现有的查询或更新达人函数来保存新达人
        from 服务.达人服务 import 查询或更新达人

        结果 = await 查询或更新达人(UID)

        if 结果["状态"] == "成功":
            return 统一响应模型.成功(
                {"达人id": 结果["达人id"], "操作": "保存成功", "消息": 结果["消息"]},
                "达人已成功保存到公海",
            )
        else:
            return 统一响应模型.失败(400, 结果.get("消息", "保存达人失败"))

    except Exception as e:
        错误日志器.error(f"保存达人到公海接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"保存达人到公海失败: {str(e)}")


@达人路由.post("/search-talent")  # 我的达人页面使用
async def 搜索达人接口(
    请求: 搜索达人请求 = Body(...), _当前用户: dict = Depends(获取当前用户)
):
    """
    搜索达人接口 - 直接调用第三方API

    参数:
        抖音号: 达人抖音号，用于第三方搜索

    返回:
        第三方搜索结果
    """
    try:
        抖音号 = 请求.抖音号.strip()

        if not 抖音号:
            return 统一响应模型.失败(状态.达人管理.抖音号不能为空, "抖音号不能为空")

        # 直接调用第三方搜索API
        外部达人搜索API地址 = "https://daduoduo.com/ajax/dyLiveDataAjax.ashx"
        params = {"action": "GetSearchTipForPeople", "keyword": 抖音号, "sortType": "1"}

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        async with httpx.AsyncClient(timeout=15.0) as client:
            try:
                response = await client.get(
                    外部达人搜索API地址, params=params, headers=headers
                )
                response.raise_for_status()
                搜索结果 = response.json()

                if 搜索结果.get("code") == 0 and 搜索结果.get("data", {}).get("data"):
                    达人列表 = 搜索结果["data"]["data"]

                    # 只保留中文字段，统一UID字段名
                    简化达人列表 = []
                    for 达人 in 达人列表:
                        简化达人列表.append(
                            {
                                "UID": 达人.get("UserId", ""),
                                "昵称": 达人.get("Name", ""),
                                "抖音号": 抖音号,
                                "头像": 达人.get("HeaderImg", ""),
                                "粉丝数": 达人.get("FansCnt", 0),
                            }
                        )

                    return 统一响应模型.成功(
                        {"达人列表": 简化达人列表, "需要确认": len(简化达人列表) > 1},
                        "搜索成功",
                        状态.达人管理.搜索成功,
                    )
                else:
                    return 统一响应模型.成功(
                        {"达人列表": [], "需要确认": False},
                        "未找到相关达人",
                        状态.达人管理.未找到相关达人,
                    )

            except httpx.TimeoutException:
                return 统一响应模型.失败(
                    状态.达人管理.第三方搜索超时, "第三方搜索服务超时"
                )
            except httpx.HTTPStatusError as e:
                return 统一响应模型.失败(
                    状态.达人管理.第三方搜索服务错误, f"第三方搜索服务错误{e}"
                )
            except Exception as e:
                错误日志器.error(f"第三方搜索异常: {str(e)}")
                return 统一响应模型.失败(
                    状态.达人管理.第三方搜索服务异常, f"第三方搜索服务异常: {str(e)}"
                )

    except Exception as e:
        错误日志器.error(f"搜索达人接口异常: {str(e)}")
        return 统一响应模型.失败(状态.达人管理.搜索失败, f"搜索达人失败: {str(e)}")


@达人路由.post("/add-talent")
async def 添加达人接口(
    请求: 添加达人请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    添加达人接口 - 简化版本

    功能说明：
    - 通过UID调用达人服务.查询或更新达人
    - 先更新达人数据保存到数据库，返回达人id
    - 直接和用户关联

    参数:
        uid_number: 达人uid

    返回:
        包含操作结果的统一响应
    """
    try:
        用户id = 当前用户["id"]
        UID = 请求.UID.strip()

        if not UID:
            return 统一响应模型.失败(状态.达人管理.UID不能为空, "达人uid不能为空")

        接口日志器.info(f"用户 {用户id} 开始添加达人: UID={UID}")

        # 使用业务服务层处理添加达人逻辑
        结果 = await 达人业务处理服务实例.添加达人_业务处理(用户id, UID)

        if 结果["status"] == "success":
            达人信息 = 结果["data"]
            达人id = 达人信息.get("达人id", "未知")
            接口日志器.info(
                f"添加达人完成：用户id={用户id}, 达人id={达人id}, UID={UID}"
            )
            return 统一响应模型.成功(达人信息, "添加达人成功", 状态.达人管理.添加成功)
        else:
            return 统一响应模型.失败(400, 结果["message"])

    except Exception as e:
        错误日志器.error(f"添加达人接口异常: {str(e)}")
        return 统一响应模型.失败(状态.达人管理.添加失败, f"添加达人失败: {str(e)}")


@达人路由.post("/import-contacts")
async def 导入联系方式接口(
    请求: 导入联系方式请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    导入联系方式数据

    功能说明：
    - 支持导入抖音和微信平台的联系方式数据
    - 自动识别手机号和微信号
    - 智能匹配达人并创建关联关系
    - 支持批量导入处理

    参数:
        平台类型: '抖音' 或 '微信'
        导入数据: 包含联系方式信息的数据列表

    返回:
        导入结果统计信息
    """
    try:
        用户id = 当前用户["id"]
        平台类型 = 请求.平台类型
        导入数据 = 请求.导入数据

        # 使用业务服务层处理导入逻辑
        from 服务.联系方式处理服务 import 联系方式导入服务

        导入服务 = 联系方式导入服务()
        处理结果 = await 导入服务.导入联系方式数据(用户id, 平台类型, 导入数据)

        if 处理结果["状态"] == "成功":
            return 统一响应模型.成功(处理结果["数据"], 处理结果["消息"])
        else:
            return 统一响应模型.失败(400, 处理结果["消息"])

    except Exception as e:
        错误日志器.error(f"导入联系方式接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"导入联系方式失败: {str(e)}")


# 关联达人请求模型
class 关联达人请求(BaseModel):
    补充信息id: int = Field(..., description="用户达人补充信息表id")
    平台账号: str = Field(..., description="平台账号")
    达人uid: str = Field(..., description="达人uid，写入达人表的uid_number字段")


class 更新达人数据请求(BaseModel):
    """
    更新达人数据请求模型 - 通过UID从第三方API更新达人最新信息
    """

    达人id: int = Field(..., description="达人id")
    UID: Optional[str] = Field(
        None, description="达人uid，可选。如果不传则从数据库中获取uid_number字段"
    )


@达人路由.post(
    "/bind-talent",
    summary="关联达人",
    description="为用户达人关联表补充达人id，将达人信息写入达人表",
)
async def 关联达人接口(
    请求: 关联达人请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    关联达人接口 - 补充用户达人关联表的达人id

    用途：用户达人关联表中达人id为null时，通过达人uid创建达人记录并关联

    流程：
    1. 通过补充信息id找到对应的用户达人关联表记录
    2. 验证该记录的达人id为null
    3. 调用达人服务.查询或更新达人函数（自动处理查询、创建、更新逻辑）
    4. 更新用户达人关联表的达人id字段

    参数:
        补充信息id: 用户达人补充信息表id
        平台账号: 平台账号（抖音号等）
        达人uid: 达人uid，写入达人表的uid_number字段

    返回:
        关联操作结果
    """
    try:
        用户id = 当前用户["id"]

        # 验证达人uid参数
        if not 请求.达人uid or not 请求.达人uid.strip():
            return 统一响应模型.失败(400, "达人uid不能为空")

        达人uid = 请求.达人uid.strip()

        # 使用业务服务层处理关联达人逻辑
        结果 = await 达人业务处理服务实例.关联达人_业务处理(
            用户id=用户id,
            补充信息id=请求.补充信息id,
            平台账号=请求.平台账号,
            达人uid=达人uid,
        )

        if 结果["status"] == "success":
            return 统一响应模型.成功(结果["data"], 结果["message"])
        else:
            return 统一响应模型.失败(400, 结果["message"])

    except Exception as e:
        错误日志器.error(f"关联达人接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"关联达人失败: {str(e)}")


@达人路由.post(
    "/update-talent-data",
    summary="更新达人数据",
    description="通过UID从第三方API更新达人最新信息",
)
async def 更新达人数据接口(
    请求: 更新达人数据请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    更新达人数据接口 - 从第三方API获取最新达人信息并更新数据库

    功能说明：
    1. 验证达人id的有效性
    2. 通过UID调用达人服务.查询或更新达人函数获取最新数据
    3. 返回更新结果

    参数:
        达人id: 要更新的达人id
        UID: 达人uid，用于从第三方API获取最新数据

    返回:
        更新操作结果
    """
    try:
        用户id = 当前用户["id"]

        # 参数验证
        if not 请求.达人id or 请求.达人id <= 0:
            return 统一响应模型.失败(状态.达人管理.参数错误, "达人id无效")

        # 使用业务服务层处理更新达人数据逻辑
        结果 = await 达人业务处理服务实例.更新达人数据_业务处理(
            用户id=用户id, 达人id=请求.达人id, UID=请求.UID
        )

        if 结果["status"] == "success":
            return 统一响应模型.成功(
                结果["data"], 结果["message"], 状态.达人管理.添加成功
            )
        else:
            return 统一响应模型.失败(状态.达人管理.查询或更新达人失败, 结果["message"])

    except Exception as e:
        错误日志器.error(
            f"更新达人数据接口异常: 用户id={当前用户.get('id')}, 达人id={请求.达人id}, 请求UID={请求.UID}, 错误={str(e)}"
        )
        return 统一响应模型.失败(500, f"更新达人数据失败: {str(e)}")
