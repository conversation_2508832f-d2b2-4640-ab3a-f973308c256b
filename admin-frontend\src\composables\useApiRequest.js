/**
 * 统一的API请求处理Hook
 * 合并useApi.js和useApiResponse.js的功能，消除重复代码
 * 
 * 功能特性：
 * 1. 统一的API调用和响应处理
 * 2. 自动处理后端标准化响应格式 {status: 100, message: '', data: {}}
 * 3. 完整的加载状态和错误处理
 * 4. 用户认证状态管理
 * 5. 灵活的消息提示配置
 * 6. 请求统计和性能监控
 */

import { ref, computed, reactive, nextTick } from 'vue';
import { message, notification } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '../store';
import apiClient from '../services/apiClient';

/**
 * 创建统一的API请求Hook
 * @param {Object} options - 配置选项
 * @returns {Object} API请求相关的响应式数据和方法
 */
export function useApiRequest(options = {}) {
  const {
    showErrorMessage = true,        // 是否显示错误消息
    showSuccessMessage = false,     // 是否显示成功消息
    autoHandleLogin = true,         // 是否自动处理登录跳转
    requiresAuth = true,           // 是否需要认证
    successMessageType = 'message', // 成功消息类型：message | notification
    errorMessageType = 'message',   // 错误消息类型：message | notification
    defaultErrorMessage = '请求失败，请稍后重试'
  } = options;

  const router = useRouter();
  const userStore = useUserStore();

  // 响应式状态
  const loading = ref(false);
  const data = ref(null);
  const error = ref(null);
  const lastResponse = ref(null);

  // 请求统计
  const stats = reactive({
    totalRequests: 0,
    successCount: 0,
    errorCount: 0,
    lastRequestTime: null,
    averageResponseTime: 0
  });

  // 计算属性
  const isSuccess = computed(() => {
    return lastResponse.value?.status === 100;
  });

  const hasError = computed(() => {
    return !isSuccess.value && lastResponse.value !== null;
  });

  const isEmpty = computed(() => {
    return isSuccess.value && (!data.value || 
      (Array.isArray(data.value) && data.value.length === 0) ||
      (typeof data.value === 'object' && Object.keys(data.value).length === 0));
  });

  /**
   * 显示消息提示 - 优化后的统一消息处理
   * @param {string} 消息内容 - 要显示的消息内容
   * @param {string} 消息类型 - success | error | warning | info
   * @param {string} 显示方式 - message | notification
   */
  const 显示消息 = (消息内容, 消息类型, 显示方式) => {
    if (!消息内容) return;

    const 消息配置 = {
      success: { title: '操作成功', duration: 3 },
      error: { title: '操作失败', duration: 5 },
      warning: { title: '操作警告', duration: 4 },
      info: { title: '提示信息', duration: 3 }
    };

    if (显示方式 === 'notification') {
      notification[消息类型]({
        message: 消息配置[消息类型]?.title || '系统提示',
        description: 消息内容,
        duration: 消息配置[消息类型]?.duration || 3
      });
    } else {
      message[消息类型](消息内容);
    }
  };

  /**
   * 处理登录状态 - 优化后的登录状态检查
   * @param {Object} 响应数据 - API响应数据
   */
  const 处理登录状态 = (响应数据) => {
    const 需要重新登录 = autoHandleLogin && (
      响应数据?.needLogin ||
      响应数据?.shouldRedirectToLogin ||
      响应数据?.status === 401
    );

    if (需要重新登录) {
      console.log('🔒 检测到需要重新登录，即将跳转到登录页');
      userStore.logout();
      nextTick(() => router.push('/login'));
    }
  };

  /**
   * 验证用户认证状态 - 独立的认证验证函数
   * @returns {Promise<boolean>} 认证是否有效
   */
  const 验证用户认证 = async () => {
    try {
      if (!userStore.isAuthenticated) {
        const 错误消息 = '用户未登录，请先登录';
        显示消息(错误消息, 'error', errorMessageType);
        return false;
      }
      return true;
    } catch (存储错误) {
      console.error('检查用户认证状态失败:', 存储错误);
      const 错误消息 = '认证状态异常，请重新登录';
      显示消息(错误消息, 'error', errorMessageType);

      try {
        userStore.logout();
      } catch (登出错误) {
        console.error('登出失败:', 登出错误);
      }
      return false;
    }
  };

  /**
   * 处理API响应 - 统一的响应处理逻辑
   * @param {Object} 原始响应 - API原始响应数据
   * @param {Object} 消息选项 - 消息显示配置
   * @param {Function} 错误回调 - 错误处理回调
   * @returns {Promise<any>} 处理后的数据
   */
  const 处理API响应 = async (原始响应, 消息选项, 错误回调) => {
    // 检查是否为标准化响应格式
    const 是标准响应 = 原始响应&& typeof 原始响应 === 'object' &&
                    'status' in 原始响应 && 'data' in 原始响应;

    if (!是标准响应) {
      return 原始响应; // 直接返回非标准响应
    }

    // 处理成功响应
    if (原始响应.status === 100) {
      if (消息选项.showSuccessMessage && 原始响应.message) {
        显示消息(原始响应.message, 'success', 消息选项.successMessageType);
      }
      stats.successCount++;
      return 原始响应; // 返回完整响应对象，而不是只返回data
    }

    // 处理业务错误
    const 错误消息 = 原始响应.message || defaultErrorMessage;
    const 业务错误 = Object.assign(new Error(错误消息), {
      businessError: true,
      statusCode: 原始响应.status,
      userFriendlyMessage: 错误消息
    });

    if (消息选项.showErrorMessage) {
      显示消息(错误消息, 'error', 消息选项.errorMessageType);
    }

    stats.errorCount++;
    error.value = 业务错误;
    处理登录状态(原始响应);

    // 标记这是业务错误，已经处理过回调
    业务错误.callbackHandled = true;

    if (错误回调) {
      await 错误回调(业务错误, 原始响应);
    }

    throw 业务错误;
  };

  /**
   * 处理请求错误 - 统一的错误处理逻辑
   * @param {Error} 网络错误 - 捕获的错误对象
   * @param {number} 开始时间 - 请求开始时间
   * @param {Object} 消息选项 - 消息显示配置
   * @param {Function} 错误回调 - 错误处理回调
   */
  const 处理请求错误 = async (网络错误, 开始时间, 消息选项, 错误回调) => {
    // 更新统计信息
    const 响应时间 = Date.now() - 开始时间;
    stats.errorCount++;
    stats.averageResponseTime = Math.round(
      (stats.averageResponseTime * (stats.totalRequests - 1) + 响应时间) / stats.totalRequests
    );

    // 处理错误消息和状态
    const 错误消息 = 处理错误信息(网络错误);
    error.value = 网络错误;

    // 显示错误消息
    if (消息选项.showErrorMessage) {
      显示消息(错误消息, 'error', 消息选项.errorMessageType);
    }

    // 处理登录状态和执行回调
    处理登录状态(网络错误);
    错误回调 && await 错误回调(网络错误);
  };

  /**
   * 处理错误信息 - 优化后的统一错误处理
   * @param {Error} 错误对象 - 捕获的错误对象
   * @returns {string} 用户友好的错误消息
   */
  const 处理错误信息 = (错误对象) => {
    if (!错误对象) return defaultErrorMessage;

    // 优先使用预设的用户友好消息
    if (错误对象.userFriendlyMessage) return 错误对象.userFriendlyMessage;

    // 错误类型映射表 - 提高代码可维护性
    const 错误类型映射 = {
      // 网络相关错误
      NETWORK_ERROR: '网络连接失败，请检查网络设置',
      ECONNABORTED: '请求超时，请稍后重试',
      // HTTP状态码错误
      401: '用户认证失败，请重新登录',
      403: '您没有权限执行此操作',
      404: '请求的资源未找到',
      422: '提交的数据格式不正确',
      429: '请求过于频繁，请稍后再试',
      500: '服务器内部错误，请稍后重试',
      502: '服务暂时不可用，请稍后重试',
      503: '服务暂时不可用，请稍后重试',
      504: '服务暂时不可用，请稍后重试'
    };

    // 检查错误代码
    if (错误对象.code && 错误类型映射[错误对象.code]) {
      return 错误类型映射[错误对象.code];
    }

    // 检查网络错误关键词
    if (错误对象.message?.includes('Network Error') || 错误对象.message?.includes('timeout')) {
      return 错误对象.message.includes('timeout') ? 错误类型映射.ECONNABORTED : 错误类型映射.NETWORK_ERROR;
    }

    // 检查HTTP状态码
    const 状态码 = 错误对象.response?.status;
    if (状态码 && 错误类型映射[状态码]) {
      return 错误类型映射[状态码];
    }

    // 返回后端提供的错误消息或默认消息
    return 错误对象.response?.data?.message || 错误对象.message || defaultErrorMessage;
  };

  /**
   * 执行API请求的核心方法
   * @param {Function|Promise} apiCall - API调用函数或Promise
   * @param {Object} executeOptions - 执行选项
   * @returns {Promise} 处理后的数据
   */
  const 执行API请求 = async (apiCall, executeOptions = {}) => {
    const {
      onSuccess,                    // 成功回调
      onError,                      // 错误回调
      onFinally,                    // 完成回调
      transform,                    // 数据转换函数
      showLoading = true,           // 是否显示加载状态
      resetDataBefore = true,       // 执行前是否重置数据
      requiresAuth: executeRequiresAuth = requiresAuth,  // 允许单次请求覆盖认证要求
      ...messageOptions             // 消息相关选项覆盖
    } = executeOptions;

    // 合并消息选项
    const finalMessageOptions = {
      showSuccessMessage,
      showErrorMessage,
      successMessageType,
      errorMessageType,
      ...messageOptions
    };

    // 验证用户认证状态 - 优化后的认证检查
    if (executeRequiresAuth && !await 验证用户认证()) {
      return Promise.reject(new Error('用户认证失败'));
    }

    // 重置状态
    if (showLoading) loading.value = true;
    if (resetDataBefore) {
      data.value = null;
      error.value = null;
    }

    // 更新统计
    stats.totalRequests++;
    stats.lastRequestTime = new Date();
    const 开始时间 = Date.now();

    try {
      // 执行API调用
      const 原始响应 = typeof apiCall === 'function' ? await apiCall() : await apiCall;
      const 结束时间 = Date.now();
      const 响应时间 = 结束时间 - 开始时间;

      // 更新平均响应时间
      stats.averageResponseTime = Math.round(
        (stats.averageResponseTime * (stats.totalRequests - 1) + 响应时间) / stats.totalRequests
      );

      // 保存原始响应
      lastResponse.value = 原始响应;

      // 处理标准化响应格式 - 优化后的响应处理
      const 最终数据 = await 处理API响应(原始响应, finalMessageOptions, onError);

      // 应用数据转换并保存
      const 转换后数据 = transform && typeof transform === 'function' ? transform(最终数据) : 最终数据;
      data.value = 转换后数据;

      // 执行成功回调
      onSuccess && await onSuccess(转换后数据, 原始响应);

      return 转换后数据;

    } catch (网络错误) {
      // 只有非业务错误或未处理过回调的错误才需要处理
      if (!网络错误.callbackHandled) {
        await 处理请求错误(网络错误, 开始时间, finalMessageOptions, onError);
      }
      throw 网络错误;
    } finally {
      // 清理和回调处理
      showLoading && (loading.value = false);
      onFinally && await onFinally();
    }
  };

  /**
   * 重置状态
   */
  const 重置状态 = () => {
    loading.value = false;
    data.value = null;
    error.value = null;
    lastResponse.value = null;
  };

  /**
   * 重试请求
   */
  const 重试请求 = async (apiCall, executeOptions = {}) => {
    return 执行API请求(apiCall, executeOptions);
  };

  return {
    // 响应式状态
    loading: computed(() => loading.value),
    data: computed(() => data.value),
    error: computed(() => error.value),
    lastResponse: computed(() => lastResponse.value),
    stats: computed(() => ({ ...stats })),

    // 计算属性
    isSuccess,
    hasError,
    isEmpty,

    // 核心方法（中文命名）
    执行API请求,
    重置状态,
    重试请求,
    显示消息: (消息内容, 消息类型 = 'info') => 显示消息(消息内容, 消息类型, 'message'),

    // 兼容性方法（向后兼容）
    execute: 执行API请求,
    reset: 重置状态,
    retry: 重试请求,
    showMessage: (content, type = 'info') => 显示消息(content, type, 'message')
  };
}

// 预设配置的Hook变体
export function useSuperAdminRequest(options = {}) {
  return useApiRequest({
    showSuccessMessage: true,
    showErrorMessage: true,
    autoHandleLogin: true,
    successMessageType: 'message',
    errorMessageType: 'message',
    ...options
  });
}

export function useSilentRequest(options = {}) {
  return useApiRequest({
    showSuccessMessage: false,
    showErrorMessage: false,
    autoHandleLogin: true,
    ...options
  });
}

export function useNotificationRequest(options = {}) {
  return useApiRequest({
    showSuccessMessage: true,
    showErrorMessage: true,
    successMessageType: 'notification',
    errorMessageType: 'notification',
    ...options
  });
}

/**
 * 创建特定API端点的请求Hook
 * @param {String} API端点 - API接口端点路径
 * @param {Object} 配置选项 - Hook配置选项
 * @returns {Object} 特定API端点的请求Hook对象
 */
export function useApiEndpoint(API端点, 配置选项 = {}) {
  const API实例 = useApiRequest(配置选项);

  const 发送GET请求 = (查询参数 = {}) => {
    return API实例.执行API请求(() => apiClient.get(API端点, { params: 查询参数 }), 查询参数);
  };

  const 发送POST请求 = (请求数据 = {}) => {
    return API实例.执行API请求(() => apiClient.post(API端点, 请求数据), 请求数据);
  };

  const 发送PUT请求 = (请求数据 = {}) => {
    return API实例.执行API请求(() => apiClient.put(API端点, 请求数据), 请求数据);
  };

  const 发送DELETE请求 = (查询参数 = {}) => {
    return API实例.执行API请求(() => apiClient.delete(API端点, { params: 查询参数 }), 查询参数);
  };

  return {
    ...API实例,
    发送GET请求,
    发送POST请求,
    发送PUT请求,
    发送DELETE请求,
    // 兼容性方法
    get: 发送GET请求,
    post: 发送POST请求,
    put: 发送PUT请求,
    delete: 发送DELETE请求
  };
}

/**
 * 批量API请求Hook
 * @param {Object} 配置选项 - Hook配置选项
 * @returns {Object} 批量请求相关的响应式数据和方法
 */
export function useBatchApiRequest(配置选项 = {}) {
  const 执行结果 = ref([]);
  const 错误列表 = ref([]);
  const 执行进度 = ref({ completed: 0, total: 0, percentage: 0 });
  const 批量加载中 = ref(false);

  /**
   * 执行批量请求
   * @param {Array} 请求列表 - API请求函数数组
   * @param {Object} 批量选项 - 批量执行选项
   * @returns {Promise} 批量执行结果
   */
  const 执行批量请求 = async (请求列表, 批量选项 = {}) => {
    const {
      并发数量 = 3,
      失败时继续 = true,
      显示进度 = true
    } = 批量选项;

    批量加载中.value = true;
    执行结果.value = [];
    错误列表.value = [];
    执行进度.value = { completed: 0, total: 请求列表.length, percentage: 0 };

    try {
      const 结果数组 = [];
      const 错误数组 = [];

      // 分批执行请求
      for (let i = 0; i < 请求列表.length; i += 并发数量) {
        const 当前批次 = 请求列表.slice(i, i + 并发数量);

        const 批次结果 = await Promise.allSettled(
          当前批次.map(async (请求函数, 索引) => {
            try {
              const API实例 = useApiRequest(配置选项);
              const 结果 = await API实例.执行API请求(请求函数);
              return { success: true, data: 结果, index: i + 索引 };
            } catch (错误) {
              return { success: false, error: 错误, index: i + 索引 };
            }
          })
        );

        // 处理批次结果
        批次结果.forEach((结果, 批次索引) => {
          const 全局索引 = i + 批次索引;

          if (结果.status === 'fulfilled') {
            if (结果.value.success) {
              结果数组[全局索引] = 结果.value.data;
            } else {
              错误数组[全局索引] = 结果.value.error;
              if (!失败时继续) {
                throw 结果.value.error;
              }
            }
          } else {
            错误数组[全局索引] = 结果.reason;
            if (!失败时继续) {
              throw 结果.reason;
            }
          }

          // 更新进度
          执行进度.value.completed++;
          执行进度.value.percentage = Math.round(
            (执行进度.value.completed / 执行进度.value.total) * 100
          );
        });

        // 更新响应式数据
        执行结果.value = [...结果数组];
        错误列表.value = [...错误数组];
      }

      return {
        results: 执行结果.value,
        errors: 错误列表.value,
        successCount: 执行结果.value.filter(r => r !== undefined).length,
        errorCount: 错误列表.value.filter(e => e !== undefined).length
      };

    } finally {
      批量加载中.value = false;
    }
  };

  return {
    // 状态
    执行结果: computed(() => 执行结果.value),
    错误列表: computed(() => 错误列表.value),
    执行进度: computed(() => 执行进度.value),
    批量加载中: computed(() => 批量加载中.value),

    // 方法
    执行批量请求,

    // 兼容性方法
    results: computed(() => 执行结果.value),
    errors: computed(() => 错误列表.value),
    progress: computed(() => 执行进度.value),
    loading: computed(() => 批量加载中.value),
    executeBatch: 执行批量请求
  };
}
