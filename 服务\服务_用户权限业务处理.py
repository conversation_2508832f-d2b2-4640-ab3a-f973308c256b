"""
用户权限业务处理服务层
负责用户权限相关的业务逻辑处理
"""

from 数据.数据访问_用户会员权限 import (
    异步查询_用户会员关联信息_通过用户id_返回详情列表,
    异步查询_用户关联店铺列表_通过用户id_支持分页,
    异步查询_用户登录历史记录_通过用户id_支持分页,
    异步查询_用户安全审计数据_通过用户id_生成报告
)
from 日志 import 应用日志器 as 服务日志器, 错误日志器


async def 异步处理_用户权限详情查询_包含会员和权限信息(用户id: int):
    """
    处理用户权限详情查询业务逻辑
    整合用户会员信息、权限列表和AI智能体信息
    
    Args:
        用户id (int): 用户的唯一标识
        
    Returns:
        dict: 包含权限列表、套餐信息、AI信息的完整权限详情
    """
    try:
        服务日志器.info(f"开始处理用户权限详情查询: 用户id={用户id}")
        
        # 查询用户会员关联信息
        会员权限信息 = await 异步查询_用户会员关联信息_通过用户id_返回详情列表(用户id)
        
        # 格式化会员权限信息 - 消除冗余，统一数据结构
        格式化会员权限信息 = []
        for 权限记录 in 会员权限信息:
            # 解析权限列表为数组
            权限列表 = 权限记录.get('权限列表', '').split(', ') if 权限记录.get('权限列表') else []
            权限描述列表 = 权限记录.get('权限描述列表', '').split('; ') if 权限记录.get('权限描述列表') else []

            # 构建权限详情数组
            权限详情列表 = []
            for i, 权限名称 in enumerate(权限列表):
                if 权限名称.strip():  # 过滤空权限名称
                    权限详情列表.append({
                        "权限名称": 权限名称.strip(),
                        "权限描述": 权限描述列表[i].strip() if i < len(权限描述列表) else '',
                        "权限状态": 权限记录.get('会员状态', '')
                    })

            格式化会员权限信息.append({
                "会员id": 权限记录.get('会员id'),
                "会员名称": 权限记录.get('会员名称', ''),
                "会员状态": 权限记录.get('会员状态', ''),
                "开通时间": 权限记录.get('开通时间', ''),
                "到期时间": 权限记录.get('到期时间', ''),
                "剩余天数": 权限记录.get('剩余天数', 0),
                "会员费用": {
                    "每月费用": 权限记录.get('会员每月费用', 0),
                    "每年费用": 权限记录.get('会员每年费用', 0)
                },
                "会员权益": {
                    "每月算力点": 权限记录.get('会员每月算力点', 0),
                    "可创建团队数": 权限记录.get('可创建团队数', 0),
                    "创建团队默认人数上限": 权限记录.get('创建团队默认人数上限', 0),
                    "可加入团队数": 权限记录.get('可加入团队数', 0)
                },
                "权限详情": 权限详情列表
            })

        处理结果 = {
            "会员权限信息": 格式化会员权限信息
        }
        
        服务日志器.info(f"用户权限详情查询处理完成: 用户id={用户id}")
        return 处理结果
        
    except Exception as e:
        错误日志器.error(f"处理用户权限详情查询失败: 用户id={用户id}, 错误={e}", exc_info=True)
        raise


async def 异步处理_用户关联店铺查询_支持分页(用户id: int, 页码: int = 1, 每页数量: int = 10):
    """
    处理用户关联店铺查询业务逻辑
    格式化店铺数据，添加业务相关字段
    
    Args:
        用户id (int): 用户的唯一标识
        页码 (int): 页码，从1开始
        每页数量 (int): 每页显示的记录数
        
    Returns:
        dict: 格式化后的店铺分页数据
    """
    try:
        服务日志器.info(f"开始处理用户关联店铺查询: 用户id={用户id}, 页码={页码}")
        
        # 查询店铺数据
        店铺分页数据 = await 异步查询_用户关联店铺列表_通过用户id_支持分页(用户id, 页码, 每页数量)
        
        # 格式化店铺数据，使用中文字段名
        格式化店铺列表 = []
        for 店铺 in 店铺分页数据['列表']:
            格式化店铺列表.append({
                "店铺id": 店铺.get('店铺id'),
                "店铺标识": 店铺.get('店铺标识') or '',
                "店铺名称": 店铺.get('店铺名称') or '',
                "店铺头像": 店铺.get('店铺头像') or '',
                "创建时间": 店铺.get('创建时间').strftime("%Y-%m-%d %H:%M:%S") if 店铺.get('创建时间') else '',
                "关联时间": 店铺.get('关联时间').strftime("%Y-%m-%d %H:%M:%S") if 店铺.get('关联时间') else '',
                "关联状态": "已关联"
            })
        
        处理结果 = {
            "总数": 店铺分页数据['总数'],
            "列表": 格式化店铺列表,
            "页码": 店铺分页数据['页码'],
            "每页数量": 店铺分页数据['每页数量']
        }
        
        服务日志器.info(f"用户关联店铺查询处理完成: 用户id={用户id}, 总数={处理结果['总数']}")
        return 处理结果
        
    except Exception as e:
        错误日志器.error(f"处理用户关联店铺查询失败: 用户id={用户id}, 错误={e}", exc_info=True)
        raise


async def 异步处理_用户登录历史查询_支持分页(用户id: int, 页码: int = 1, 每页数量: int = 20):
    """
    处理用户登录历史查询业务逻辑
    格式化登录记录数据，添加业务相关字段
    
    Args:
        用户id (int): 用户的唯一标识
        页码 (int): 页码，从1开始
        每页数量 (int): 每页显示的记录数
        
    Returns:
        dict: 格式化后的登录历史分页数据
    """
    try:
        服务日志器.info(f"开始处理用户登录历史查询: 用户id={用户id}, 页码={页码}")
        
        # 查询登录历史数据
        登录历史分页数据 = await 异步查询_用户登录历史记录_通过用户id_支持分页(用户id, 页码, 每页数量)
        
        # 格式化登录历史数据
        格式化登录历史列表 = []
        for 登录记录 in 登录历史分页数据['列表']:
            格式化登录历史列表.append({
                "id": 登录记录.get('id'),
                "登陆时间": 登录记录.get('登陆时间'),
                "ip地址": 登录记录.get('ip地址') or '',
                "ip归属地": 登录记录.get('ip归属地') or '未知',
                "格式化时间": 登录记录.get('格式化时间') or '',
                "登录状态": "成功"  # 业务逻辑：记录在表中的都是成功登录
            })
        
        处理结果 = {
            "总数": 登录历史分页数据['总数'],
            "列表": 格式化登录历史列表,
            "页码": 登录历史分页数据['页码'],
            "每页数量": 登录历史分页数据['每页数量']
        }
        
        服务日志器.info(f"用户登录历史查询处理完成: 用户id={用户id}, 总数={处理结果['总数']}")
        return 处理结果
        
    except Exception as e:
        错误日志器.error(f"处理用户登录历史查询失败: 用户id={用户id}, 错误={e}", exc_info=True)
        raise


async def 异步处理_用户安全审计查询_生成审计报告(用户id: int):
    """
    处理用户安全审计查询业务逻辑
    生成完整的用户安全审计报告，匹配前端显示需求

    Args:
        用户id (int): 用户的唯一标识

    Returns:
        dict: 用户安全审计报告，包含登录安全、接口安全、常用IP、近期登录等信息
    """
    try:
        服务日志器.info(f"开始处理用户安全审计查询: 用户id={用户id}")

        # 查询安全审计数据
        安全审计数据 = await 异步查询_用户安全审计数据_通过用户id_生成报告(用户id)

        # 查询会员权限信息用于风险评估
        会员权限信息 = await 异步查询_用户会员关联信息_通过用户id_返回详情列表(用户id)

        # 格式化常用IP数据
        格式化常用IP = []
        for ip记录 in 安全审计数据.get('常用IP', []):
            格式化常用IP.append({
                "IP地址": ip记录.get('IP地址', ''),
                "IP归属地": ip记录.get('IP归属地', '未知'),
                "登录次数": ip记录.get('登录次数', 0),
                "最后使用时间": ip记录.get('最后使用时间').strftime("%Y-%m-%d %H:%M:%S") if ip记录.get('最后使用时间') else '',
                "首次使用时间": ip记录.get('首次使用时间').strftime("%Y-%m-%d %H:%M:%S") if ip记录.get('首次使用时间') else ''
            })

        # 格式化近期登录数据
        格式化近期登录 = []
        for 登录记录 in 安全审计数据.get('近期登录', []):
            格式化近期登录.append({
                "登陆时间": 登录记录.get('登陆时间'),
                "IP地址": 登录记录.get('IP地址', ''),
                "IP归属地": 登录记录.get('IP归属地', '未知'),
                "格式化时间": 登录记录.get('格式化时间', ''),
                "登录状态": "成功"  # 记录在表中的都是成功登录
            })

        # 生成风险提示
        风险提示 = []
        登录安全 = 安全审计数据.get('登录安全', {})

        if 登录安全.get('总登录次数', 0) == 0:
            风险提示.append("用户从未登录")
        elif 登录安全.get('不同IP数量', 0) > 10:
            风险提示.append("使用IP地址过多，存在安全风险")
        elif 登录安全.get('今日登录', 0) > 50:
            风险提示.append("今日登录频率异常")

        if not 会员权限信息:
            风险提示.append("用户无有效权限")
        elif 会员权限信息 and 会员权限信息[0].get('会员状态') == '已过期':
            风险提示.append("用户会员已过期")

        # 计算安全等级
        安全等级 = "高"
        if len(风险提示) > 0:
            安全等级 = "中" if len(风险提示) <= 2 else "低"

        # 构建最终审计报告
        审计报告 = {
            "登录安全": 安全审计数据.get('登录安全', {}),
            "接口安全": 安全审计数据.get('接口安全', {}),
            "常用IP": 格式化常用IP,
            "近期登录": 格式化近期登录,
            "安全等级": 安全等级,
            "风险提示": 风险提示,
            "审计时间": "刚刚"
        }

        服务日志器.info(f"用户安全审计查询处理完成: 用户id={用户id}, 安全等级={安全等级}")
        return 审计报告

    except Exception as e:
        错误日志器.error(f"处理用户安全审计查询失败: 用户id={用户id}, 错误={e}", exc_info=True)
        raise
