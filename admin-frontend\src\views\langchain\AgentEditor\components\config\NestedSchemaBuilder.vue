<template>
  <div :class="$style.schemaBuilder">
    <!-- 构建器头部 -->
    <div :class="$style.header">
      <div :class="$style.title">
        <span>🏗️ JSON Schema 可视化构建器</span>
        <a-badge :count="totalFields" :offset="[10, 0]" />
      </div>
      <div :class="$style.actions">
        <a-button size="small" @click="addRootField">
          <PlusOutlined /> 添加字段
        </a-button>
        <a-button size="small" @click="clearAll" danger>
          <ClearOutlined /> 清空
        </a-button>
      </div>
    </div>

    <!-- 构建器主体 -->
    <div :class="$style.content">
      <!-- 字段树形结构 -->
      <div :class="$style.fieldsTree">
        <div v-if="schema.properties && Object.keys(schema.properties).length === 0" :class="$style.emptyState">
          <div :class="$style.emptyIcon">📝</div>
          <div :class="$style.emptyText">点击"添加字段"开始构建Schema</div>
        </div>
        
        <FieldNode
          v-for="(field, key) in schema.properties"
          :key="key"
          :field-key="key"
          :field-data="{ ...field, required: schema.required.includes(key) }"
          :path="[key]"
          :level="0"
          @update="handleFieldUpdate"
          @delete="handleFieldDelete"
          @add-child="handleAddChild"
          @required-change="handleRequiredChange"
        />
      </div>

      <!-- 实时预览 -->
      <div :class="$style.preview">
        <div :class="$style.previewHeader">
          <span>📋 Schema 预览</span>
          <a-button size="small" type="text" @click="copySchema">
            <CopyOutlined /> 复制
          </a-button>
        </div>
        <div :class="$style.previewContent">
          <pre :class="$style.previewCode">{{ formattedSchema }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, ClearOutlined, CopyOutlined } from '@ant-design/icons-vue'
import FieldNode from './FieldNode.vue'

// Props
const props = defineProps({
  modelValue: {
    type: [String, Object],
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

// 基础Schema结构
const schema = ref({
  type: 'object',
  properties: {},
  required: []
})

// 计算属性
const totalFields = computed(() => {
  const countFields = (obj) => {
    let count = 0
    if (obj && typeof obj === 'object') {
      for (const key in obj) {
        if (key === 'properties' && obj[key]) {
          count += Object.keys(obj[key]).length
          for (const prop in obj[key]) {
            count += countFields(obj[key][prop])
          }
        } else if (key === 'items' && obj[key]) {
          count += countFields(obj[key])
        }
      }
    }
    return count
  }
  return countFields(schema.value)
})

const formattedSchema = computed(() => {
  return JSON.stringify(schema.value, null, 2)
})

// 初始化
const initializeSchema = () => {
  if (props.modelValue) {
    try {
      let parsed
      if (typeof props.modelValue === 'string') {
        parsed = JSON.parse(props.modelValue)
      } else if (typeof props.modelValue === 'object') {
        parsed = props.modelValue
      }

      if (parsed && typeof parsed === 'object') {
        schema.value = {
          type: 'object',
          properties: {},
          required: [],
          ...parsed
        }
      }
    } catch (error) {
      console.warn('Invalid JSON schema:', error)
    }
  }
}

// 添加根字段
const addRootField = () => {
  const fieldName = `字段_${Date.now()}`
  schema.value.properties[fieldName] = {
    type: 'string',
    description: ''
  }
  updateOutput()
}

// 清空所有字段
const clearAll = () => {
  schema.value.properties = {}
  schema.value.required = []
  updateOutput()
}

// 处理字段更新
const handleFieldUpdate = (path, updates, newKey) => {
  if (newKey && newKey !== path[path.length - 1]) {
    // 字段名变更
    handleFieldRename(path, newKey, updates)
  } else {
    // 普通更新
    const target = getNestedValue(schema.value, path)
    if (target) {
      Object.assign(target, updates)
      updateOutput()
    }
  }
}

// 处理字段重命名
const handleFieldRename = (path, newKey, updates) => {
  if (!newKey || !path || path.length === 0) return

  const oldKey = path[path.length - 1]
  if (oldKey === newKey) return // 没有变化

  const parentPath = path.slice(0, -1)

  try {
    if (parentPath.length === 0) {
      // 重命名根字段
      if (!schema.value.properties || !schema.value.properties[oldKey]) return

      const oldData = schema.value.properties[oldKey]
      delete schema.value.properties[oldKey]
      schema.value.properties[newKey] = { ...oldData, ...updates }

      // 更新required数组
      if (schema.value.required) {
        const requiredIndex = schema.value.required.indexOf(oldKey)
        if (requiredIndex > -1) {
          schema.value.required[requiredIndex] = newKey
        }
      }
    } else {
      // 重命名嵌套字段
      const parent = getNestedValue(schema.value, parentPath)
      if (parent && parent.properties && parent.properties[oldKey]) {
        const oldData = parent.properties[oldKey]
        delete parent.properties[oldKey]
        parent.properties[newKey] = { ...oldData, ...updates }

        // 更新required数组
        if (parent.required) {
          const requiredIndex = parent.required.indexOf(oldKey)
          if (requiredIndex > -1) {
            parent.required[requiredIndex] = newKey
          }
        }
      }
    }
    updateOutput()
  } catch (error) {
    console.warn('字段重命名失败:', error)
  }
}

// 处理字段删除
const handleFieldDelete = (path) => {
  if (path.length === 1) {
    // 删除根字段
    delete schema.value.properties[path[0]]
    // 从required中移除
    const requiredIndex = schema.value.required.indexOf(path[0])
    if (requiredIndex > -1) {
      schema.value.required.splice(requiredIndex, 1)
    }
  } else {
    // 删除嵌套字段
    const parentPath = path.slice(0, -1)
    const fieldKey = path[path.length - 1]
    const parent = getNestedValue(schema.value, parentPath)

    if (parent && parent.properties) {
      delete parent.properties[fieldKey]
      if (parent.required) {
        const requiredIndex = parent.required.indexOf(fieldKey)
        if (requiredIndex > -1) {
          parent.required.splice(requiredIndex, 1)
        }
      }
    }
  }
  updateOutput()
}

// 处理必需字段变更
const handleRequiredChange = (path, fieldKey, required) => {
  if (path.length === 1) {
    // 根字段的required变更
    if (required) {
      if (!schema.value.required.includes(fieldKey)) {
        schema.value.required.push(fieldKey)
      }
    } else {
      const requiredIndex = schema.value.required.indexOf(fieldKey)
      if (requiredIndex > -1) {
        schema.value.required.splice(requiredIndex, 1)
      }
    }
  } else {
    // 嵌套字段的required变更
    const parentPath = path.slice(0, -1)
    const parent = getNestedValue(schema.value, parentPath)

    if (parent) {
      if (!parent.required) {
        parent.required = []
      }

      if (required) {
        if (!parent.required.includes(fieldKey)) {
          parent.required.push(fieldKey)
        }
      } else {
        const requiredIndex = parent.required.indexOf(fieldKey)
        if (requiredIndex > -1) {
          parent.required.splice(requiredIndex, 1)
        }
      }
    }
  }
  updateOutput()
}

// 处理添加子字段
const handleAddChild = (path) => {
  const target = getNestedValue(schema.value, path)
  if (target) {
    if (target.type === 'object') {
      if (!target.properties) {
        target.properties = {}
      }
      const childName = `子字段_${Date.now()}`
      target.properties[childName] = {
        type: 'string',
        description: ''
      }
    } else if (target.type === 'array') {
      if (!target.items) {
        target.items = {
          type: 'object',
          properties: {},
          required: []
        }
      }
      if (target.items.type === 'object') {
        if (!target.items.properties) {
          target.items.properties = {}
        }
        const childName = `数组项_${Date.now()}`
        target.items.properties[childName] = {
          type: 'string',
          description: ''
        }
      }
    }
    updateOutput()
  }
}

// 获取嵌套值
const getNestedValue = (obj, path) => {
  let current = obj
  for (let i = 0; i < path.length; i++) {
    const key = path[i]

    if (key === 'items') {
      // 处理数组items
      if (current.items) {
        current = current.items
      } else {
        return null
      }
    } else if (current.properties && current.properties[key]) {
      // 处理对象属性
      current = current.properties[key]
    } else {
      return null
    }
  }
  return current
}

// 更新输出
const updateOutput = () => {
  emit('update:modelValue', schema.value)
}

// 复制Schema
const copySchema = async () => {
  try {
    await navigator.clipboard.writeText(formattedSchema.value)
    message.success('Schema已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

// 监听外部变化
watch(() => props.modelValue, initializeSchema, { immediate: true })
</script>

<style module>
.schemaBuilder {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #262626;
}

.actions {
  display: flex;
  gap: 8px;
}

.content {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr minmax(350px, 500px);
  gap: 16px;
  padding: 16px;
  min-height: 0;
}

.fieldsTree {
  overflow-y: auto;
  padding-right: 8px;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #8c8c8c;
  text-align: center;
}

.emptyIcon {
  font-size: 48px;
  margin-bottom: 16px;
}

.emptyText {
  font-size: 14px;
}

.preview {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.previewHeader {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  background: #fafafa;
  flex-shrink: 0;
}

.previewContent {
  flex: 1;
  overflow: auto;
  min-height: 0;
}

.previewCode {
  margin: 0;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #262626;
  background: transparent;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 响应式设计 */
/* 超宽屏优化 (2560px+) */
@media (min-width: 2560px) {
  .content {
    grid-template-columns: 1fr minmax(500px, 700px);
    gap: 24px;
  }
}

/* 大屏优化 (1920px+) */
@media (min-width: 1920px) {
  .content {
    grid-template-columns: 1fr minmax(450px, 600px);
    gap: 20px;
  }
}

/* 中等屏幕 */
@media (max-width: 1400px) {
  .content {
    grid-template-columns: 1fr minmax(300px, 400px);
  }
}

@media (max-width: 1200px) {
  .content {
    grid-template-columns: 1fr minmax(280px, 350px);
  }
}

@media (max-width: 768px) {
  .content {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
  }

  .preview {
    max-height: 300px;
  }
}
</style>
