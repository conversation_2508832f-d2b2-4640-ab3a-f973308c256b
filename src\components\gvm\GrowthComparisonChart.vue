<template>
  <div class="growth-comparison-chart">
    <div 
      ref="chartContainer" 
      :style="{ height: height }"
      class="chart-container"
    ></div>
    
    <div v-if="loading" class="chart-loading">
      <a-spin size="large" />
    </div>
    
    <div v-if="!loading && (!data || data.length === 0)" class="chart-empty">
      <a-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import gvmService from '../../services/gvmService'

defineOptions({
  name: 'GrowthComparisonChart'
})

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    default: 'individual', // 'individual' | 'team'
    validator: (value) => ['individual', 'team'].includes(value)
  },
  height: {
    type: String,
    default: '300px'
  },
  loading: {
    type: Boolean,
    default: false
  },
  topCount: {
    type: Number,
    default: 10
  }
})

const chartContainer = ref(null)
let chartInstance = null

const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || props.loading) return
  
  const colors = gvmService.getChartColors()
  const option = getChartOption(colors)
  
  chartInstance.setOption(option, true)
}

const getChartOption = (colors) => {
  if (!props.data || props.data.length === 0) {
    return {}
  }
  
  // 取前N名数据并按增长率排序
  const topData = props.data
    .filter(item => item.growth && typeof item.growth.rate === 'number')
    .sort((a, b) => b.growth.rate - a.growth.rate)
    .slice(0, props.topCount)
  
  const names = topData.map(item => item.name || `${props.type === 'individual' ? '用户' : '团队'}${item.id}`)
  const growthRates = topData.map(item => item.growth.rate)
  const sales = topData.map(item => item.sales || 0)
  
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: (params) => {
        let html = `<div style="margin-bottom: 4px; font-weight: 600;">${params[0].axisValue}</div>`
        params.forEach(param => {
          if (param.seriesName === '增长率') {
            html += `
              <div style="display: flex; align-items: center; margin-bottom: 2px;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
                <span style="margin-right: 8px;">增长率:</span>
                <span style="font-weight: 600; color: ${param.value >= 0 ? '#52c41a' : '#f5222d'};">${param.value.toFixed(1)}%</span>
              </div>
            `
          } else {
            html += `
              <div style="display: flex; align-items: center; margin-bottom: 2px;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
                <span style="margin-right: 8px;">销售额:</span>
                <span style="font-weight: 600;">${gvmService.formatAmount(param.value)}</span>
              </div>
            `
          }
        })
        return html
      }
    },
    legend: {
      data: ['销售额', '增长率'],
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: names,
        axisPointer: {
          type: 'shadow'
        },
        axisLabel: {
          interval: 0,
          rotate: 45,
          formatter: (value) => {
            return value.length > 6 ? value.substring(0, 6) + '...' : value
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '销售额',
        position: 'left',
        axisLabel: {
          formatter: (value) => gvmService.formatAmount(value)
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: colors.primary
          }
        }
      },
      {
        type: 'value',
        name: '增长率',
        position: 'right',
        axisLabel: {
          formatter: '{value}%'
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: colors.success
          }
        }
      }
    ],
    series: [
      {
        name: '销售额',
        type: 'bar',
        yAxisIndex: 0,
        data: sales,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: colors.primary + '80' },
            { offset: 1, color: colors.primary }
          ])
        },
        barWidth: '40%'
      },
      {
        name: '增长率',
        type: 'line',
        yAxisIndex: 1,
        data: growthRates,
        itemStyle: {
          color: colors.success
        },
        lineStyle: {
          width: 3,
          color: colors.success
        },
        symbol: 'circle',
        symbolSize: 8,
        markLine: {
          data: [
            {
              type: 'average',
              name: '平均值',
              lineStyle: {
                color: colors.warning,
                type: 'dashed'
              },
              label: {
                formatter: '平均: {c}%'
              }
            }
          ]
        }
      }
    ],
    animation: true,
    animationDuration: 1000
  }
}

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

watch(() => props.data, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

watch(() => props.type, () => {
  nextTick(() => {
    updateChart()
  })
})

watch(() => props.loading, (newLoading) => {
  if (!newLoading) {
    nextTick(() => {
      updateChart()
    })
  }
})

onMounted(() => {
  nextTick(() => {
    initChart()
  })
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})

defineExpose({
  refreshChart: updateChart,
  getChartInstance: () => chartInstance
})
</script>

<style scoped>
.growth-comparison-chart {
  position: relative;
  width: 100%;
}

.chart-container {
  width: 100%;
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.chart-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
</style>
