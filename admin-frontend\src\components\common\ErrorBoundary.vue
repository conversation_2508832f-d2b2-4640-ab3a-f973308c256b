<template>
  <div class="error-boundary">
    <slot v-if="!hasError" />
    <div v-else class="error-display">
      <a-result
        status="error"
        :title="errorTitle"
        :sub-title="errorMessage"
      >
        <template #extra>
          <a-button type="primary" @click="重试">
            重试
          </a-button>
          <a-button @click="返回首页">
            返回首页
          </a-button>
        </template>
      </a-result>
    </div>
  </div>
</template>

<script setup>
import {onErrorCaptured, ref} from 'vue';
import {useRouter} from 'vue-router';

const router = useRouter();
const hasError = ref(false);
const errorTitle = ref('页面出现错误');
const errorMessage = ref('抱歉，页面遇到了一些问题');

const emit = defineEmits(['error']);

// 捕获子组件错误
onErrorCaptured((error, instance, info) => {
  // 根据错误类型决定日志级别
  const isUserInputError = error.message && (
    error.message.includes('用户名或密码错误') ||
    error.message.includes('密码错误') ||
    error.message.includes('登录失败，请检查用户名和密码')
  );

  if (isUserInputError) {
    console.log('🔐 用户输入验证失败:', error.message);
    // 用户输入错误不需要显示错误边界，让组件自己处理
    return false; // 阻止错误继续向上传播，但不显示错误边界
  } else {
    console.error('ErrorBoundary 捕获到错误:', error);
    console.error('错误信息:', info);

    hasError.value = true;
    errorTitle.value = '组件渲染错误';
    errorMessage.value = error.message || '组件在渲染过程中出现了错误';

    // 发送错误事件给父组件
    emit('error', { error, instance, info });

    // 阻止错误继续向上传播
    return false;
  }
});

// 重试功能
const 重试 = () => {
  hasError.value = false;
  errorTitle.value = '页面出现错误';
  errorMessage.value = '抱歉，页面遇到了一些问题';
};

// 返回首页
const 返回首页 = () => {
  router.push('/');
};

// 手动设置错误状态（供外部调用）
const 设置错误 = (title, msg) => {
  hasError.value = true;
  errorTitle.value = title;
  errorMessage.value = msg;
};

// 清除错误状态
const 清除错误 = () => {
  hasError.value = false;
};

// 暴露方法给父组件
defineExpose({
  设置错误,
  清除错误,
  hasError
});
</script>

<style scoped>
.error-boundary {
  width: 100%;
  height: 100%;
}

.error-display {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 20px;
}
</style>