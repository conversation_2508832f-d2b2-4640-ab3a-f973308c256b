"""
店铺订单数据层
基于PostgreSQL实现的店铺订单相关数据库操作

特性：
1. 使用PostgreSQL原生语法和特性
2. 支持高效的订单查询和管理
3. 使用$1, $2参数占位符，防止SQL注入
4. 优化的批量操作和事务处理
5. 完整的错误处理和日志记录
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器


class 店铺订单数据层:
    """店铺订单数据访问层"""

    def __init__(self):
        self.数据库连接池 = 异步连接池实例

        # 安全字段白名单 - 防止SQL注入（只包含实际存在的字段）
        self.允许的字段名 = {
            # 实际存在的订单字段
            "订单id",
            "商品id",
            "商品名称",
            "商品数量",
            "订单状态",
            "店铺id",
            "店铺名称",
            "创建时间",
            "更新时间",
            "收货时间",
            "付款时间",
            "订单结算时间",
            "订单账号映射表id",
            "作者账号",
            "抖音火山号",
            "支付金额",
            "佣金率",
            "预估佣金支出",
            "结算金额",
            "实际佣金支出",
            "超时未结算原因",
            "商品来源",
            "尾款支付时间",
            "定金金额",
            "佣金发票",
            "冻结比例",
            "是否阶梯佣金",
            "门槛销量",
            "基础佣金率",
            "升佣佣金率",
            "预估奖励佣金支出",
            "结算奖励佣金支出",
            "阶梯计划ID",
            "支付补贴",
            "平台补贴",
            "达人补贴",
            "运费",
            "税费",
            "运费补贴",
            "分销来源",
            "营销活动id",
            "推广费率",
            "推广技术服务费",
            "预估推广费支出",
            "结算推广费支出",
            "计划类型",
            "订单来源",
            "流量细分来源",
            "流量来源",
            "订单类型",
        }

        # 允许的排序字段（只包含实际存在的字段）
        self.允许的排序字段 = {
            "订单id",
            "创建时间",
            "更新时间",
            "付款时间",
            "收货时间",
            "订单结算时间",
            "支付金额",
            "预估佣金支出",
            "实际佣金支出",
            "结算金额",
            "商品数量",
            "佣金率",
            "定金金额",
            "尾款支付时间",
        }

        # 允许的订单状态
        self.允许的订单状态 = {
            "待付款",
            "待发货",
            "待收货",
            "已完成",
            "已取消",
            "退款中",
            "已退款",
            "售后中",
            "已付款",
            "已结算",
            "已确认收货",
            "超时未结算",
        }

    def _验证字段名安全性(self, 字段名: str) -> bool:
        """验证字段名是否在安全白名单中"""
        return 字段名 in self.允许的字段名

    def _验证排序字段安全性(self, 字段名: str) -> bool:
        """验证排序字段是否在安全白名单中"""
        return 字段名 in self.允许的排序字段

    async def 检查订单是否存在(self, 订单id: str) -> bool:
        """
        检查指定订单ID是否存在

        参数:
            订单id: 订单ID

        返回:
            bool: 订单是否存在
        """
        try:
            检查查询 = "SELECT COUNT(*) as count FROM 店铺订单表 WHERE 订单id = $1"
            检查结果 = await self.数据库连接池.执行查询(检查查询, (订单id,))

            return 检查结果 and 检查结果[0]["count"] > 0

        except Exception as e:
            错误日志器.error(f"检查订单是否存在失败: {e}", exc_info=True)
            return False

    async def 插入订单数据(self, 订单数据: Dict[str, Any]) -> bool:
        """
        插入单条订单数据

        参数:
            订单数据: 包含订单信息的字典

        返回:
            bool: 插入是否成功
        """
        try:
            # 验证字段安全性
            字段列表 = list(订单数据.keys())
            for 字段名 in 字段列表:
                if not self._验证字段名安全性(字段名):
                    数据库日志器.error(f"插入订单数据失败: 不安全的字段名 {字段名}")
                    return False

            # 构建安全的插入SQL
            字段名称 = ", ".join([f'"{字段}"' for 字段 in 字段列表])
            占位符 = ", ".join([f"${i + 1}" for i in range(len(字段列表))])

            插入查询 = f"""
            INSERT INTO 店铺订单表 ({字段名称})
            VALUES ({占位符})
            """

            数值列表 = [订单数据[字段] for 字段 in 字段列表]
            await self.数据库连接池.执行更新(插入查询, 数值列表)

            数据库日志器.info(
                f"成功插入订单数据: 订单ID={订单数据.get('订单id', 'Unknown')}"
            )
            return True

        except Exception as e:
            错误日志器.error(f"插入订单数据失败: {e}", exc_info=True)
            return False

    async def 获取用户店铺权限(self, 用户id: int) -> List[int]:
        """
        获取用户有权限访问的店铺id列表

        参数:
            用户id: 用户id

        返回:
            List[int]: 店铺id列表
        """
        try:
            权限查询 = """
            SELECT 店铺id FROM 用户_店铺 
            WHERE 用户id = $1
            """

            权限结果 = await self.数据库连接池.执行查询(权限查询, (用户id,))

            return [row["店铺id"] for row in 权限结果] if 权限结果 else []

        except Exception as e:
            错误日志器.error(f"获取用户店铺权限失败: {e}", exc_info=True)
            return []

    async def 查询订单列表(
        self,
        用户id: int,
        页码: int = 1,
        每页数量: int = 20,
        店铺id: Optional[str] = None,
        订单状态: Optional[str] = None,
        商品名称: Optional[str] = None,
        开始时间: Optional[str] = None,
        结束时间: Optional[str] = None,
        排序字段: str = "创建时间",
        排序方向: str = "DESC",
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        查询订单列表（支持分页和筛选）

        参数:
            用户id: 用户id
            页码: 页码（从1开始）
            每页数量: 每页显示的订单数量
            店铺id: 可选的店铺id筛选
            订单状态: 可选的订单状态筛选
            商品名称: 可选的商品名称模糊搜索
            开始时间: 可选的开始时间筛选
            结束时间: 可选的结束时间筛选
            排序字段: 排序字段
            排序方向: 排序方向（ASC/DESC）

        返回:
            Tuple[List[Dict], int]: (订单列表, 总记录数)
        """
        try:
            # 验证排序字段安全性
            if not self._验证排序字段安全性(排序字段):
                排序字段 = "创建时间"

            # 验证排序方向
            if 排序方向.upper() not in ["ASC", "DESC"]:
                排序方向 = "DESC"

            # 构建查询条件
            查询条件 = []
            查询参数 = []
            param_count = 0

            # 基础查询 - 优化版本：使用JOIN代替EXISTS子查询提升性能
            基础查询 = """
            SELECT DISTINCT
                o.订单id, o.商品名称, o.商品数量, o.商品id,
                o.订单状态, o.店铺id, o.店铺名称,
                o.创建时间, o.作者账号, o.支付金额, o.佣金率, o.预估佣金支出,
                o.实际佣金支出, o.付款时间, o.收货时间, o.结算金额,
                o.抖音火山号, o.商品来源, o.订单来源, o.订单类型
            FROM 店铺订单表 o
            INNER JOIN 店铺 s ON s.shop_id = o.店铺id
            INNER JOIN 用户_店铺 us ON us.店铺id = s.id
            WHERE us.用户id = $1
            """
            param_count += 1
            查询参数.append(用户id)

            # 添加筛选条件
            if 店铺id:
                param_count += 1
                查询条件.append(f"o.店铺id = ${param_count}")
                查询参数.append(str(店铺id))

            if 订单状态:
                param_count += 1
                查询条件.append(f"o.订单状态 = ${param_count}")
                查询参数.append(订单状态)

            if 商品名称:
                param_count += 1
                查询条件.append(f"o.商品名称 ILIKE ${param_count}")
                查询参数.append(f"%{商品名称}%")

            if 开始时间:
                param_count += 1
                查询条件.append(f"o.付款时间 >= ${param_count}")
                查询参数.append(开始时间)

            if 结束时间:
                param_count += 1
                查询条件.append(f"o.付款时间 <= ${param_count}")
                查询参数.append(结束时间)

            # 组装完整查询
            if 查询条件:
                完整查询 = 基础查询 + " AND " + " AND ".join(查询条件)
            else:
                完整查询 = 基础查询

            # 查询总数
            计数查询 = f"SELECT COUNT(*) as total FROM ({完整查询}) as count_query"
            计数结果 = await self.数据库连接池.执行查询(计数查询, 查询参数)
            总记录数 = 计数结果[0]["total"] if 计数结果 else 0

            # 添加排序和分页
            分页查询 = f"""
            {完整查询}
            ORDER BY o."{排序字段}" {排序方向}
            LIMIT ${len(查询参数) + 1} OFFSET ${len(查询参数) + 2}
            """

            偏移量 = (页码 - 1) * 每页数量
            查询参数.extend([每页数量, 偏移量])

            # 执行查询
            订单列表 = await self.数据库连接池.执行查询(分页查询, 查询参数)

            数据库日志器.info(f"查询订单列表成功: 用户id={用户id}, 总记录数={总记录数}")
            return 订单列表 or [], 总记录数

        except Exception as e:
            错误日志器.error(f"查询订单列表失败: {e}", exc_info=True)
            return [], 0

    async def 查询订单详情(self, 订单id: str, 用户id: int) -> Optional[Dict[str, Any]]:
        """
        查询订单详情

        参数:
            订单id: 订单ID
            用户id: 用户id

        返回:
            Optional[Dict]: 订单详情，如果不存在或无权限返回None
        """
        try:
            详情查询 = """
            SELECT o.*
            FROM 店铺订单表 o
            WHERE o.订单id = $1
            AND EXISTS (
                SELECT 1 FROM 用户_店铺 us
                WHERE us.用户id = $2
                AND (us.店铺id = CAST(o.店铺id AS INTEGER) OR o.店铺id IS NULL)
            )
            """

            详情结果 = await self.数据库连接池.执行查询(详情查询, (订单id, 用户id))

            if 详情结果:
                return 详情结果[0]
            else:
                数据库日志器.warning(
                    f"订单不存在或无权限: 订单ID={订单id}, 用户id={用户id}"
                )
                return None

        except Exception as e:
            错误日志器.error(f"查询订单详情失败: {e}", exc_info=True)
            return None

    async def 获取订单状态选项(self, 用户id: int) -> List[str]:
        """
        获取用户有权限访问的订单状态选项

        参数:
            用户id: 用户id

        返回:
            List[str]: 订单状态列表
        """
        try:
            状态查询 = """
            SELECT DISTINCT o.订单状态
            FROM 店铺订单表 o
            WHERE EXISTS (
                SELECT 1 FROM 用户_店铺 us
                INNER JOIN 店铺 s ON us.店铺id = s.id
                WHERE us.用户id = $1
                AND s.shop_id = o.店铺id
            )
            AND o.订单状态 IS NOT NULL
            AND o.订单状态 != ''
            ORDER BY o.订单状态
            """

            状态结果 = await self.数据库连接池.执行查询(状态查询, (用户id,))

            状态列表 = [row["订单状态"] for row in 状态结果] if 状态结果 else []
            数据库日志器.info(
                f"获取订单状态选项成功: 用户id={用户id}, 状态数量={len(状态列表)}"
            )
            return 状态列表

        except Exception as e:
            错误日志器.error(f"获取订单状态选项失败: {e}", exc_info=True)
            return []

    async def 查找达人信息(self, 抖音火山号: str) -> Optional[int]:
        """
        根据抖音火山号查找达人表id

        参数:
            抖音火山号: 抖音火山号

        返回:
            Optional[int]: 达人表id，如果找不到返回None
        """
        try:
            查找查询 = """
            SELECT id FROM 达人表
            WHERE account_douyin = $1
            LIMIT 1
            """

            查找结果 = await self.数据库连接池.执行查询(查找查询, (抖音火山号,))

            if 查找结果:
                达人id = 查找结果[0]["id"]
                数据库日志器.info(f"找到达人信息: 抖音号={抖音火山号}, 达人id={达人id}")
                return 达人id
            else:
                数据库日志器.debug(f"未找到达人信息: 抖音号={抖音火山号}")
                return None

        except Exception as e:
            错误日志器.error(f"查找达人信息失败: {e}", exc_info=True)
            return None

    async def 检查抖音号认领记录存在(self, 抖音号: str) -> Optional[int]:
        """
        检查抖音号的映射记录是否存在（通过订单账号映射表）

        参数:
            抖音号: 抖音号

        返回:
            Optional[int]: 如果存在返回订单账号映射表id，不存在返回None
        """
        try:
            检查查询 = """
            SELECT id FROM 订单账号映射表
            WHERE 抖音号 = $1
            LIMIT 1
            """

            检查结果 = await self.数据库连接池.执行查询(检查查询, (抖音号,))

            if 检查结果:
                映射表id = 检查结果[0]["id"]
                数据库日志器.debug(
                    f"找到抖音号映射记录: 抖音号={抖音号}, 映射表id={映射表id}"
                )
                return 映射表id
            else:
                数据库日志器.debug(f"未找到抖音号映射记录: 抖音号={抖音号}")
                return None

        except Exception as e:
            错误日志器.error(f"检查抖音号映射记录失败: {e}", exc_info=True)
            return None

    async def 创建用户订单认领记录_仅抖音号(self, 抖音号: str) -> Optional[int]:
        """
        创建订单账号映射记录（仅抖音号）- 已移除用户订单认领表相关操作

        参数:
            抖音号: 抖音号

        返回:
            Optional[int]: 成功返回订单账号映射表id，失败返回None

        注意: 此方法已移除用户订单认领表相关操作，仅保留订单账号映射表操作
        """
        try:
            # 先检查是否已存在
            现有映射表id = await self.检查抖音号认领记录存在(抖音号)
            if 现有映射表id:
                数据库日志器.info(f"订单账号映射记录已存在，返回现有ID: {现有映射表id}")
                return 现有映射表id

            # 插入新的订单账号映射记录
            插入查询 = """
            INSERT INTO 订单账号映射表 (抖音号, 平台)
            VALUES ($1, '抖音')
            RETURNING id
            """

            插入结果 = await self.数据库连接池.执行查询(插入查询, (抖音号,))

            if 插入结果:
                映射表id = 插入结果[0]["id"]
                数据库日志器.info(
                    f"创建订单账号映射记录成功: 抖音号={抖音号}, 映射表id={映射表id}"
                )
                return 映射表id
            else:
                错误日志器.error(f"创建订单账号映射记录失败: 抖音号={抖音号}")
                return None

        except Exception as e:
            错误日志器.error(f"创建用户订单认领记录_仅抖音号失败: {e}", exc_info=True)
            return None

    async def 更新用户订单认领记录_达人表id(self, 映射表id: int, 达人表id: int) -> bool:
        """
        更新订单账号映射表的达人表id字段

        参数:
            映射表id: 订单账号映射表id
            达人表id: 达人表id

        返回:
            bool: 更新是否成功
        """
        try:
            更新查询 = """
            UPDATE 订单账号映射表
            SET 达人表id = $1
            WHERE id = $2
            """

            更新结果 = await self.数据库连接池.执行更新(更新查询, (达人表id, 映射表id))

            if 更新结果:
                # 移除冗余日志，避免大量重复输出
                return True
            else:
                错误日志器.error(f"更新订单账号映射表达人id失败: 映射表id={映射表id}")
                return False

        except Exception as e:
            错误日志器.error(f"更新用户订单认领记录_达人表id失败: {e}", exc_info=True)
            return False

    # {{ AURA-X: Delete - 移除用户订单认领表相关操作. Approval: 寸止(ID:20250107). }}
    # async def 创建用户订单认领记录 - 已移除，不再使用用户订单认领表

    async def 获取用户主要团队id(self, 用户id: int) -> Optional[int]:
        """
        获取用户的主要团队id（最近加入且状态正常的团队）

        参数:
            用户id: 用户id

        返回:
            Optional[int]: 团队id，如果用户没有团队则返回None
        """
        try:
            查询SQL = """
            SELECT 团队id
            FROM 用户团队关联表
            WHERE 用户id = $1 AND 状态 = '正常'
            ORDER BY 加入时间 DESC
            LIMIT 1
            """

            查询结果 = await self.数据库连接池.执行查询(查询SQL, (用户id,))

            if 查询结果:
                团队id = 查询结果[0]["团队id"]
                数据库日志器.debug(
                    f"获取用户主要团队id成功: 用户id={用户id}, 团队id={团队id}"
                )
                return 团队id
            else:
                数据库日志器.debug(f"用户没有团队: 用户id={用户id}")
                return None

        except Exception as e:
            错误日志器.error(f"获取用户主要团队id失败: {e}", exc_info=True)
            return None

    async def 统计用户销售额(
        self,
        用户id: int,
        开始时间: Optional[datetime] = None,  # 参数保留但不使用
        结束时间: Optional[datetime] = None,  # 参数保留但不使用
    ) -> Dict[str, Any]:
        """
        统计用户销售额数据

        参数:
            用户id: 用户id
            开始时间: 开始时间（可选）
            结束时间: 结束时间（可选）

        返回:
            Dict: 包含销售额统计数据的字典

        订单状态说明:
            - 订单收货: 已完成的订单，用于业绩统计 (87.66%)
            - 订单退货退款: 退货退款订单，不计入业绩 (6.18%)
            - 订单付款: 仅付款未收货，不计入业绩 (6.17%)
        """
        try:
            # {{ AURA-X: Modify - 移除用户订单认领表依赖. Approval: 寸止(ID:20250107). }}
            # 用户销售额统计功能已禁用 - 用户订单认领表相关操作已移除
            数据库日志器.warning(f"用户销售额统计功能已禁用: 用户id={用户id}")
            return {
                "订单数量": 0,
                "总销售额": 0.0,
                "预估佣金": 0.0,
                "实际佣金": 0.0,
                "平均客单价": 0.0,
                "合作店铺数": 0,
                "关联达人数": 0,
            }

        except Exception as e:
            错误日志器.error(f"统计用户销售额失败: {e}", exc_info=True)
            return {
                "订单数量": 0,
                "总销售额": 0.0,
                "预估佣金": 0.0,
                "实际佣金": 0.0,
                "平均客单价": 0.0,
                "合作店铺数": 0,
                "关联达人数": 0,
            }

    async def 统计团队销售额(
        self,
        团队id: int,
        开始时间: Optional[datetime] = None,  # 参数保留但不使用
        结束时间: Optional[datetime] = None,  # 参数保留但不使用
    ) -> Dict[str, Any]:
        """
        统计团队销售额数据

        参数:
            团队id: 团队id
            开始时间: 开始时间（可选）
            结束时间: 结束时间（可选）

        返回:
            Dict: 包含团队销售额统计数据的字典
        """
        try:
            # {{ AURA-X: Modify - 移除用户订单认领表依赖. Approval: 寸止(ID:20250107). }}
            # 团队销售额统计功能已禁用 - 用户订单认领表相关操作已移除
            数据库日志器.warning(f"团队销售额统计功能已禁用: 团队id={团队id}")
            return {
                "订单数量": 0,
                "总销售额": 0.0,
                "预估佣金": 0.0,
                "实际佣金": 0.0,
                "平均客单价": 0.0,
                "合作店铺数": 0,
                "关联达人数": 0,
                "参与成员数": 0,
            }

        except Exception as e:
            错误日志器.error(f"统计团队销售额失败: {e}", exc_info=True)
            return {
                "订单数量": 0,
                "总销售额": 0.0,
                "预估佣金": 0.0,
                "实际佣金": 0.0,
                "平均客单价": 0.0,
                "合作店铺数": 0,
                "关联达人数": 0,
                "参与成员数": 0,
            }

    async def 更新订单认领关联(self, 订单id: str, 映射表id: int) -> bool:
        """
        更新订单的认领关联（使用新的字段名）

        参数:
            订单id: 订单ID
            映射表id: 订单账号映射表id

        返回:
            bool: 更新是否成功
        """
        try:
            更新查询 = """
            UPDATE 店铺订单表
            SET 订单账号映射表id = $1, 更新时间 = CURRENT_TIMESTAMP
            WHERE 订单id = $2
            """

            更新结果 = await self.数据库连接池.执行更新(更新查询, (映射表id, 订单id))

            if 更新结果:
                数据库日志器.info(
                    f"更新订单认领关联成功: 订单ID={订单id}, 映射表ID={映射表id}"
                )
                return True
            else:
                错误日志器.error(f"更新订单认领关联失败: 订单ID={订单id}")
                return False

        except Exception as e:
            错误日志器.error(f"更新订单认领关联失败: {e}", exc_info=True)
            return False

    async def 批量检查订单存在(self, 订单ids: List[str]) -> set:
        """
        批量检查订单是否存在

        参数:
            订单ids: 订单ID列表

        返回:
            set: 已存在的订单ID集合
        """
        try:
            if not 订单ids:
                return set()

            # 构建批量查询
            占位符 = ", ".join([f"${i + 1}" for i in range(len(订单ids))])
            批量查询 = f"""
            SELECT 订单id FROM 店铺订单表
            WHERE 订单id IN ({占位符})
            """

            查询结果 = await self.数据库连接池.执行查询(批量查询, 订单ids)

            已存在集合 = {row["订单id"] for row in 查询结果} if 查询结果 else set()
            return 已存在集合

        except Exception as e:
            错误日志器.error(f"批量检查订单存在失败: {e}", exc_info=True)
            return set()

    async def 批量插入订单数据(
        self, 订单数据列表: List[Dict[str, Any]], 任务ID: str, 批次号: int
    ) -> Tuple[int, int]:
        """
        批量插入订单数据 - 事务优化版本

        在单个事务中完成批量插入，减少数据库往返次数，提升性能

        参数:
            订单数据列表: 订单数据字典列表
            任务ID: 任务ID（用于日志）
            批次号: 批次号（用于日志）

        返回:
            Tuple[int, int]: (成功插入数量, 失败数量)
        """
        try:
            if not 订单数据列表:
                return 0, 0

            成功数量 = 0
            失败数量 = 0

            # 获取第一条记录的字段作为模板
            字段列表 = list(订单数据列表[0].keys())

            # 验证字段安全性
            for 字段名 in 字段列表:
                if not self._验证字段名安全性(字段名):
                    错误日志器.error(f"批量插入失败: 不安全的字段名 {字段名}")
                    return 0, len(订单数据列表)

            # 构建批量插入SQL
            字段名称 = ", ".join([f'"{字段}"' for 字段 in 字段列表])
            单行占位符 = ", ".join([f"${i + 1}" for i in range(len(字段列表))])

            # 事务优化：使用单个事务完成所有操作
            async with self.数据库连接池.获取连接() as 连接:
                async with 连接.transaction():
                    try:
                        # 高性能批量插入
                        插入查询 = f"""
                        INSERT INTO 店铺订单表 ({字段名称})
                        VALUES ({单行占位符})
                        """

                        # 准备所有数据
                        所有数值列表 = []
                        for 订单数据 in 订单数据列表:
                            数值列表 = [订单数据[字段] for 字段 in 字段列表]
                            所有数值列表.append(数值列表)

                        # 使用executemany进行批量插入
                        await 连接.executemany(插入查询, 所有数值列表)
                        成功数量 = len(订单数据列表)
                        失败数量 = 0

                    except Exception as e:
                        # 如果批量插入失败，在同一事务中回退到逐个插入
                        数据库日志器.warning(
                            f"[任务{任务ID}] 批次{批次号}批量插入失败，在事务中回退到逐个插入: {str(e)}"
                        )
                        # 输出详细的错误信息用于调试
                        错误日志器.error(
                            f"[任务{任务ID}] 批次{批次号}批量插入详细错误: "
                            f"数据量={len(订单数据列表)}, 字段数={len(字段列表)}, "
                            f"错误类型={type(e).__name__}, 错误信息={str(e)}"
                        )
                        成功数量 = 0
                        失败数量 = 0

                        for 订单数据 in 订单数据列表:
                            try:
                                单条插入查询 = f"""
                                INSERT INTO 店铺订单表 ({字段名称})
                                VALUES ({单行占位符})
                                """
                                数值列表 = [订单数据[字段] for 字段 in 字段列表]
                                await 连接.execute(单条插入查询, *数值列表)
                                成功数量 += 1
                            except Exception as single_error:
                                错误日志器.error(
                                    f"[任务{任务ID}] 批次{批次号}插入单条订单失败: "
                                    f"订单ID={订单数据.get('订单id', 'Unknown')}, 错误={str(single_error)}"
                                )
                                失败数量 += 1

            return 成功数量, 失败数量

        except Exception as e:
            错误日志器.error(
                f"[任务{任务ID}] 批次{批次号}事务优化批量插入失败: {e}", exc_info=True
            )
            return 0, len(订单数据列表) if 订单数据列表 else 0

    async def 批量更新订单数据(
        self, 订单数据列表: List[Dict[str, Any]], 任务ID: str, 批次号: int
    ) -> Tuple[int, int]:
        """
        批量更新订单数据 - 事务优化版本

        用于处理重复订单的更新操作，使用新导入的数据覆盖现有订单记录

        参数:
            订单数据列表: 订单数据字典列表
            任务ID: 任务ID（用于日志）
            批次号: 批次号（用于日志）

        返回:
            Tuple[int, int]: (成功更新数量, 失败数量)
        """
        if not 订单数据列表:
            return 0, 0

        成功数量 = 0
        失败数量 = 0

        try:
            # {{ AURA-X: Add - 批量更新重复订单数据. Approval: 寸止(ID:20250107). }}
            async with self.数据库连接池.获取连接() as 连接:
                # 构建批量更新SQL - 更新除订单id外的所有字段
                更新SQL = """
                    UPDATE 店铺订单表 SET
                        商品id = $2, 商品名称 = $3, 作者账号 = $4, 抖音火山号 = $5,
                        订单账号映射表id = $6, 支付金额 = $7, 佣金率 = $8, 预估佣金支出 = $9,
                        结算金额 = $10, 实际佣金支出 = $11, 订单状态 = $12, 超时未结算原因 = $13,
                        付款时间 = $14, 收货时间 = $15, 订单结算时间 = $16, 商品来源 = $17,
                        尾款支付时间 = $18, 定金金额 = $19, 店铺id = $20, 店铺名称 = $21,
                        商品数量 = $22, 佣金发票 = $23, 冻结比例 = $24, 是否阶梯佣金 = $25,
                        门槛销量 = $26, 基础佣金率 = $27, 升佣佣金率 = $28, 预估奖励佣金支出 = $29,
                        结算奖励佣金支出 = $30, "阶梯计划ID" = $31, 支付补贴 = $32, 平台补贴 = $33,
                        达人补贴 = $34, 运费 = $35, 税费 = $36, 运费补贴 = $37, 分销来源 = $38,
                        营销活动id = $39, 推广费率 = $40, 推广技术服务费 = $41, 预估推广费支出 = $42,
                        结算推广费支出 = $43, 计划类型 = $44, 订单来源 = $45, 流量细分来源 = $46,
                        流量来源 = $47, 订单类型 = $48, 更新时间 = NOW()
                    WHERE 订单id = $1
                    """

                # {{ AURA-X: Fix - 修复事务中断问题，使用独立事务处理每个更新. Approval: 寸止(ID:20250107). }}
                # 批量执行更新 - 每个更新使用独立事务避免事务中断
                for 订单数据 in 订单数据列表:
                    try:
                        # 使用独立事务处理每个更新
                        async with 连接.transaction():
                            # 准备更新参数（按SQL中的参数顺序）
                            更新参数 = (
                                订单数据["订单id"],  # $1
                                订单数据.get("商品id"),
                                订单数据.get("商品名称"),
                                订单数据.get("作者账号"),  # $2-$4
                                订单数据.get("抖音火山号"),
                                订单数据.get("订单账号映射表id"),
                                订单数据.get("支付金额"),  # $5-$7
                                订单数据.get("佣金率"),
                                订单数据.get("预估佣金支出"),
                                订单数据.get("结算金额"),  # $8-$10
                                订单数据.get("实际佣金支出"),
                                订单数据.get("订单状态"),
                                订单数据.get("超时未结算原因"),  # $11-$13
                                订单数据.get("付款时间"),
                                订单数据.get("收货时间"),
                                订单数据.get("订单结算时间"),  # $14-$16
                                订单数据.get("商品来源"),
                                订单数据.get("尾款支付时间"),
                                订单数据.get("定金金额"),  # $17-$19
                                订单数据.get("店铺id"),
                                订单数据.get("店铺名称"),
                                订单数据.get("商品数量"),  # $20-$22
                                订单数据.get("佣金发票"),
                                订单数据.get("冻结比例"),
                                订单数据.get("是否阶梯佣金"),  # $23-$25
                                订单数据.get("门槛销量"),
                                订单数据.get("基础佣金率"),
                                订单数据.get("升佣佣金率"),  # $26-$28
                                订单数据.get("预估奖励佣金支出"),
                                订单数据.get("结算奖励佣金支出"),
                                订单数据.get("阶梯计划ID"),  # $29-$31
                                订单数据.get("支付补贴"),
                                订单数据.get("平台补贴"),
                                订单数据.get("达人补贴"),  # $32-$34
                                订单数据.get("运费"),
                                订单数据.get("税费"),
                                订单数据.get("运费补贴"),  # $35-$37
                                订单数据.get("分销来源"),
                                订单数据.get("营销活动id"),
                                订单数据.get("推广费率"),  # $38-$40
                                订单数据.get("推广技术服务费"),
                                订单数据.get("预估推广费支出"),
                                订单数据.get("结算推广费支出"),  # $41-$43
                                订单数据.get("计划类型"),
                                订单数据.get("订单来源"),
                                订单数据.get("流量细分来源"),  # $44-$46
                                订单数据.get("流量来源"),
                                订单数据.get("订单类型"),  # $47-$48
                            )

                            await 连接.execute(更新SQL, *更新参数)
                            成功数量 += 1

                    except Exception as update_error:
                        失败数量 += 1
                        错误日志器.error(
                            f"[任务{任务ID}] 批次{批次号}更新订单失败: 订单id={订单数据.get('订单id')}, "
                            f"错误={str(update_error)}"
                        )

            数据库日志器.info(
                f"[任务{任务ID}] 批次{批次号}批量更新完成: 成功{成功数量}条, 失败{失败数量}条"
            )

            return 成功数量, 失败数量

        except Exception as e:
            错误日志器.error(f"批量更新订单数据失败: {e}", exc_info=True)
            return 0, len(订单数据列表)

    async def 创建导入记录(
        self,
        任务ID: str,
        用户id: int,
        文件名: str,
        文件大小: int,
        文件路径: str,
        文件hash: str,
    ) -> Optional[int]:
        """
        创建导入记录

        参数:
            任务ID: 任务ID
            用户id: 用户id
            文件名: 文件名
            文件大小: 文件大小（字节）
            文件路径: 文件路径
            文件hash: 文件哈希值

        返回:
            Optional[int]: 成功返回记录id，失败返回None
        """
        try:
            插入查询 = """
            INSERT INTO 店铺订单_导入记录表 (
                任务id, 用户id, 文件名, 文件大小, 文件路径, 文件hash,
                任务状态, 开始时间, 更新时间
            )
            VALUES ($1, $2, $3, $4, $5, $6, '进行中', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING id
            """

            插入结果 = await self.数据库连接池.执行查询(
                插入查询, (任务ID, 用户id, 文件名, 文件大小, 文件路径, 文件hash)
            )

            if 插入结果:
                记录id = 插入结果[0]["id"]
                数据库日志器.info(f"创建导入记录成功: 任务ID={任务ID}, 记录id={记录id}")
                return 记录id
            else:
                错误日志器.error(f"创建导入记录失败: 任务ID={任务ID}")
                return None

        except Exception as e:
            错误日志器.error(f"创建导入记录失败: {e}", exc_info=True)
            return None

    async def 更新导入记录状态(
        self,
        记录id: int,
        状态: str,
        进度百分比: float = 0.0,
        错误信息: Optional[str] = None,
    ) -> bool:
        """
        更新导入记录状态

        参数:
            记录id: 记录id
            状态: 新状态
            进度百分比: 进度百分比
            错误信息: 错误信息

        返回:
            bool: 更新是否成功
        """
        try:
            更新查询 = """
            UPDATE 店铺订单_导入记录表
            SET 任务状态 = $1, 进度百分比 = $2, 错误信息 = $3, 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $4
            """

            更新结果 = await self.数据库连接池.执行更新(
                更新查询, (状态, 进度百分比, 错误信息, 记录id)
            )

            if 更新结果:
                数据库日志器.info(f"更新导入记录状态成功: 记录id={记录id}, 状态={状态}")
                return True
            else:
                错误日志器.error(f"更新导入记录状态失败: 记录id={记录id}")
                return False

        except Exception as e:
            错误日志器.error(f"更新导入记录状态失败: {e}", exc_info=True)
            return False

    async def 检查用户文件重复导入(
        self, 用户id: int, 文件hash: str
    ) -> Optional[Dict[str, Any]]:
        """
        检查用户文件重复导入

        参数:
            用户id: 用户id
            文件hash: 文件MD5哈希值

        返回:
            Optional[Dict]: 如果存在重复文件，返回包含任务信息的字典；否则返回None
        """
        try:
            检查查询 = """
            SELECT
                id, 任务id as "任务ID", 任务状态, 进度百分比, 文件名, 文件大小,
                开始时间, 完成时间, 成功数量, 失败数量, 跳过数量
            FROM 店铺订单_导入记录表
            WHERE 用户id = $1 AND 文件hash = $2
            ORDER BY 开始时间 DESC
            LIMIT 1
            """

            检查结果 = await self.数据库连接池.执行查询(检查查询, (用户id, 文件hash))

            if 检查结果:
                重复记录 = 检查结果[0]
                数据库日志器.info(
                    f"发现重复文件导入: 用户id={用户id}, 文件hash={文件hash[:8]}, "
                    f"任务ID={重复记录['任务ID']}, 状态={重复记录['任务状态']}"
                )
                return 重复记录
            else:
                return None

        except Exception as e:
            错误日志器.error(f"检查用户文件重复导入失败: {e}", exc_info=True)
            return None

    async def 查询导入记录(self, 任务ID: str, 用户id: int) -> Optional[Dict[str, Any]]:
        """
        根据任务ID查询导入记录

        参数:
            任务ID: 任务ID
            用户id: 用户id

        返回:
            Optional[Dict]: 导入记录信息，如果不存在返回None
        """
        try:
            查询SQL = """
            SELECT
                id, 任务id as "任务ID", 用户id, 文件名, 文件大小, 任务状态,
                总行数, 成功数量, 失败数量, 跳过数量, 错误信息,
                开始时间, 完成时间, 进度百分比, 文件路径, 更新时间,
                当前批次, 已处理行数, 可续传, 文件hash
            FROM 店铺订单_导入记录表
            WHERE 任务id = $1 AND 用户id = $2
            """

            查询结果 = await self.数据库连接池.执行查询(查询SQL, (任务ID, 用户id))

            if 查询结果:
                return 查询结果[0]
            else:
                数据库日志器.warning(
                    f"导入记录不存在: 任务ID={任务ID}, 用户id={用户id}"
                )
                return None

        except Exception as e:
            错误日志器.error(f"查询导入记录失败: {e}", exc_info=True)
            return None

    async def 查询导入记录_通过ID(
        self, 记录id: int, 用户id: int
    ) -> Optional[Dict[str, Any]]:
        """
        根据记录ID查询导入记录

        参数:
            记录id: 记录ID
            用户id: 用户id

        返回:
            Optional[Dict]: 导入记录信息，如果不存在返回None
        """
        try:
            查询SQL = """
            SELECT
                id, 任务id as "任务ID", 用户id, 文件名, 文件大小, 任务状态,
                总行数, 成功数量, 失败数量, 跳过数量, 错误信息,
                开始时间, 完成时间, 进度百分比, 文件路径, 更新时间,
                当前批次, 已处理行数, 可续传, 文件hash
            FROM 店铺订单_导入记录表
            WHERE id = $1 AND 用户id = $2
            """

            查询结果 = await self.数据库连接池.执行查询(查询SQL, (记录id, 用户id))

            if 查询结果:
                return 查询结果[0]
            else:
                数据库日志器.warning(
                    f"导入记录不存在: 记录id={记录id}, 用户id={用户id}"
                )
                return None

        except Exception as e:
            错误日志器.error(f"查询导入记录失败: {e}", exc_info=True)
            return None

    async def 更新导入记录总行数(self, 记录id: int, 总行数: int) -> bool:
        """
        更新导入记录的总行数

        参数:
            记录id: 记录ID
            总行数: Excel文件总行数

        返回:
            bool: 更新是否成功
        """
        try:
            更新查询 = """
            UPDATE 店铺订单_导入记录表
            SET 总行数 = $1, 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $2
            """

            更新结果 = await self.数据库连接池.执行更新(更新查询, (总行数, 记录id))

            if 更新结果:
                数据库日志器.info(
                    f"更新导入记录总行数成功: 记录id={记录id}, 总行数={总行数}"
                )
                return True
            else:
                错误日志器.error(f"更新导入记录总行数失败: 记录id={记录id}")
                return False

        except Exception as e:
            错误日志器.error(f"更新导入记录总行数失败: {e}", exc_info=True)
            return False

    async def 更新批次进度(
        self,
        记录id: int,
        当前批次: int,
        已处理行数: int,
        成功数量: int,
        失败数量: int,
        跳过数量: int,
        进度百分比: float,
    ) -> bool:
        """
        更新批次进度

        参数:
            记录id: 记录ID
            当前批次: 当前批次号
            已处理行数: 已处理行数
            成功数量: 成功数量
            失败数量: 失败数量
            跳过数量: 跳过数量
            进度百分比: 进度百分比

        返回:
            bool: 更新是否成功
        """
        try:
            更新查询 = """
            UPDATE 店铺订单_导入记录表
            SET 当前批次 = $1, 已处理行数 = $2, 成功数量 = $3, 失败数量 = $4,
                跳过数量 = $5, 进度百分比 = $6, 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $7
            """

            更新结果 = await self.数据库连接池.执行更新(
                更新查询,
                (
                    当前批次,
                    已处理行数,
                    成功数量,
                    失败数量,
                    跳过数量,
                    进度百分比,
                    记录id,
                ),
            )

            if 更新结果:
                数据库日志器.info(
                    f"更新批次进度成功: 记录id={记录id}, 批次={当前批次}, 进度={进度百分比:.1f}%"
                )
                return True
            else:
                错误日志器.error(f"更新批次进度失败: 记录id={记录id}")
                return False

        except Exception as e:
            错误日志器.error(f"更新批次进度失败: {e}", exc_info=True)
            return False

    async def 完成导入记录(
        self,
        记录id: int,
        最终状态: str,
        成功数量: int,
        失败数量: int,
        跳过数量: int,
        进度百分比: float,
    ) -> bool:
        """
        完成导入记录

        参数:
            记录id: 记录ID
            最终状态: 最终状态
            成功数量: 成功数量
            失败数量: 失败数量
            跳过数量: 跳过数量
            进度百分比: 进度百分比

        返回:
            bool: 更新是否成功
        """
        try:
            更新查询 = """
            UPDATE 店铺订单_导入记录表
            SET 任务状态 = $1, 成功数量 = $2, 失败数量 = $3, 跳过数量 = $4,
                进度百分比 = $5, 完成时间 = CURRENT_TIMESTAMP, 更新时间 = CURRENT_TIMESTAMP,
                可续传 = 0
            WHERE id = $6
            """

            更新结果 = await self.数据库连接池.执行更新(
                更新查询, (最终状态, 成功数量, 失败数量, 跳过数量, 进度百分比, 记录id)
            )

            if 更新结果:
                数据库日志器.info(
                    f"完成导入记录成功: 记录id={记录id}, 状态={最终状态}, 成功={成功数量}, 失败={失败数量}"
                )
                return True
            else:
                错误日志器.error(f"完成导入记录失败: 记录id={记录id}")
                return False

        except Exception as e:
            错误日志器.error(f"完成导入记录失败: {e}", exc_info=True)
            return False

    async def 查询导入记录列表(
        self,
        用户id: int,
        页码: int = 1,
        每页数量: int = 20,
        状态筛选: Optional[str] = None,
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        查询导入记录列表

        参数:
            用户id: 用户id
            页码: 页码
            每页数量: 每页数量
            状态筛选: 状态筛选

        返回:
            Tuple[List[Dict], int]: (记录列表, 总数)
        """
        try:
            # 构建查询条件
            查询条件 = ["用户id = $1"]
            查询参数 = [用户id]
            param_count = 1

            if 状态筛选:
                param_count += 1
                查询条件.append(f"任务状态 = ${param_count}")
                查询参数.append(状态筛选)

            条件字符串 = " AND ".join(查询条件)

            # 查询总数
            计数查询 = f"""
            SELECT COUNT(*) as total
            FROM 店铺订单_导入记录表
            WHERE {条件字符串}
            """

            计数结果 = await self.数据库连接池.执行查询(计数查询, 查询参数)
            总记录数 = 计数结果[0]["total"] if 计数结果 else 0

            # 查询列表
            列表查询 = f"""
            SELECT
                id, 任务id as "任务ID", 文件名, 文件大小, 任务状态, 总行数, 成功数量, 失败数量, 跳过数量,
                错误信息, 开始时间, 完成时间, 更新时间, 进度百分比, 可续传, 已处理行数, 当前批次
            FROM 店铺订单_导入记录表
            WHERE {条件字符串}
            ORDER BY 开始时间 DESC
            LIMIT ${len(查询参数) + 1} OFFSET ${len(查询参数) + 2}
            """

            偏移量 = (页码 - 1) * 每页数量
            查询参数.extend([每页数量, 偏移量])

            记录列表 = await self.数据库连接池.执行查询(列表查询, 查询参数)

            数据库日志器.info(f"查询导入记录列表成功: 用户id={用户id}, 总数={总记录数}")
            return 记录列表 or [], 总记录数

        except Exception as e:
            错误日志器.error(f"查询导入记录列表失败: {e}", exc_info=True)
            return [], 0


# 创建全局实例
店铺订单数据层实例 = 店铺订单数据层()
