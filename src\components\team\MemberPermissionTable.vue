<template>
  <div class="member-permission-table">
    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <a-row :gutter="16" align="middle">
        <a-col :span="8">
          <a-input
            v-model:value="searchKeyword"
            placeholder="搜索成员姓名或手机号"
            allowClear
            @input="handleSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="roleFilter"
            placeholder="筛选角色"
            allowClear
            style="width: 100%"
            @change="handleRoleFilter"
          >
            <a-select-option 
              v-for="option in roleFilterOptions" 
              :key="option.value" 
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="12">
          <a-space>
            <a-button
              type="primary"
              :disabled="!hasSelectedMembers"
              @click="$emit('batchSetRole')"
            >
              批量设置角色
            </a-button>
            <a-button
              :disabled="!hasSelectedMembers"
              @click="$emit('batchSetPermissions')"
            >
              批量设置权限
            </a-button>
            <a-button @click="$emit('showPermissionLogs')">
              权限日志
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </div>

    <!-- 成员表格 -->
    <a-table
      :dataSource="filteredMembers"
      :columns="memberColumns"
      :loading="loading"
      :scroll="{ x: 1200 }"
      :pagination="paginationConfig"
      row-key="用户id"
      :row-selection="rowSelection"
    >
      <!-- 成员信息 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === '成员信息'">
          <div class="member-info">
            <a-avatar :src="record.头像" :size="32">
              {{ record.昵称?.charAt(0) || '用' }}
            </a-avatar>
            <div class="member-details">
              <div class="member-name">{{ record.昵称 || '未设置昵称' }}</div>
              <div class="member-phone">{{ record.手机号 }}</div>
            </div>
          </div>
        </template>
        
        <!-- 角色显示 -->
        <template v-else-if="column.dataIndex === '角色'">
          <a-tag :color="getRoleColor(record.角色类型)">
            <template #icon>
              <component :is="getRoleIcon(record.角色类型)" />
            </template>
            {{ getRoleDisplayName(record) || '未知角色' }}
          </a-tag>
        </template>
        
        <!-- 权限概览 -->
        <template v-else-if="column.dataIndex === '权限概览'">
          <a-button
            type="link"
            size="small"
            @click="$emit('viewMemberPermissions', record)"
          >
            查看详情 ({{ record.权限数量 || 0 }})
          </a-button>
        </template>
        
        <!-- 状态 -->
        <template v-else-if="column.dataIndex === '状态'">
          <a-tag :color="record.状态 === '正常' ? 'success' : 'warning'">
            {{ record.状态 || '未知状态' }}
          </a-tag>
        </template>
        
        <!-- 操作 -->
        <template v-else-if="column.dataIndex === '操作'">
          <MemberActionDropdown
            :member="record"
            @edit-role="$emit('editMemberRole', record)"
            @edit-permissions="$emit('editMemberPermissions', record)"
            @transfer-ownership="$emit('transferOwnership', record)"
            @remove-member="$emit('removeMember', record)"
          />
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import { getRoleColor, getRoleDisplayName, getRoleIcon, ROLE_TYPES } from '../../utils/roleUtils'
import { debounce } from '../../utils/teamUtils'
import MemberActionDropdown from './MemberActionDropdown.vue'

defineOptions({
  name: 'MemberPermissionTable'
})

const props = defineProps({
  members: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  selectedMemberIds: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits([
  'memberSelectionChange',
  'batchSetRole',
  'batchSetPermissions', 
  'showPermissionLogs',
  'viewMemberPermissions',
  'editMemberRole',
  'editMemberPermissions',
  'transferOwnership',
  'removeMember'
])

// 搜索和筛选状态
const searchKeyword = ref('')
const roleFilter = ref(undefined)

// 表格列配置
const memberColumns = [
  {
    title: '成员信息',
    dataIndex: '成员信息',
    key: 'memberInfo',
    width: 200,
    fixed: 'left'
  },
  {
    title: '角色',
    dataIndex: '角色',
    key: 'role',
    width: 120
  },
  {
    title: '权限概览',
    dataIndex: '权限概览',
    key: 'permissions',
    width: 150
  },
  {
    title: '加入时间',
    dataIndex: '加入时间',
    key: 'joinTime',
    width: 120
  },
  {
    title: '状态',
    dataIndex: '状态',
    key: 'status',
    width: 80
  },
  {
    title: '操作',
    dataIndex: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right'
  }
]

// 角色筛选选项
const roleFilterOptions = [
  { value: ROLE_TYPES.FOUNDER, label: '创始人' },
  { value: ROLE_TYPES.LEADER, label: '负责人' },
  { value: ROLE_TYPES.ADMIN, label: '管理员' },
  { value: ROLE_TYPES.MEMBER, label: '成员' },
  { value: ROLE_TYPES.CUSTOM, label: '自定义岗位' }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
}

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: props.selectedMemberIds,
  onChange: (selectedRowKeys) => {
    emit('memberSelectionChange', selectedRowKeys)
  }
}))

// 是否有选中的成员
const hasSelectedMembers = computed(() => {
  return props.selectedMemberIds && props.selectedMemberIds.length > 0
})

// 过滤后的成员列表
const filteredMembers = computed(() => {
  let result = [...props.members]
  
  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(member => {
      return (member.昵称 && member.昵称.toLowerCase().includes(keyword)) ||
             (member.手机号 && member.手机号.includes(keyword))
    })
  }
  
  // 角色筛选
  if (roleFilter.value) {
    result = result.filter(member => {
      if (roleFilter.value === ROLE_TYPES.CUSTOM) {
        return member.角色类型 === ROLE_TYPES.CUSTOM
      }
      return member.角色类型 === roleFilter.value
    })
  }
  
  return result
})

// 防抖搜索
const handleSearch = debounce(() => {
  // 搜索逻辑已在computed中处理
}, 300)

// 角色筛选处理
const handleRoleFilter = () => {
  // 筛选逻辑已在computed中处理
}

// 监听搜索关键词变化
watch(searchKeyword, () => {
  handleSearch()
})
</script>

<style scoped>
.member-permission-table {
  width: 100%;
}

.search-section {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.member-details {
  flex: 1;
}

.member-name {
  font-weight: 500;
  font-size: 14px;
  color: #262626;
  margin-bottom: 2px;
}

.member-phone {
  font-size: 12px;
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-section .ant-col {
    margin-bottom: 8px;
  }
  
  .search-section .ant-col:last-child {
    margin-bottom: 0;
  }
}
</style> 