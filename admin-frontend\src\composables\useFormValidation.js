/**
 * 通用表单验证Hook
 * 提供统一的表单验证、错误处理、提交逻辑
 */
import {computed, reactive, ref} from 'vue';
import {message} from 'ant-design-vue';

/**
 * 创建表单验证Hook
 * @param {Object} initialData 初始表单数据
 * @param {Object} validationRules 验证规则
 * @param {Object} options 配置选项
 * @returns {Object} 表单验证相关的响应式数据和方法
 */
export function useFormValidation(initialData = {}, validationRules = {}, options = {}) {
  const {
    // 是否自动显示验证错误消息
    showErrorMessage = true,
    // 是否自动显示成功消息
    showSuccessMessage = true,
    // 提交成功后是否重置表单
    resetOnSuccess = false,
    // 自定义错误消息
    customErrorMessages = {}
  } = options;

  // 表单数据
  const formData = reactive({ ...initialData });
  
  // 验证状态
  const validationErrors = reactive({});
  const isValidating = ref(false);
  const isSubmitting = ref(false);
  
  // 字段状态跟踪
  const fieldStates = reactive({});
  
  // 初始化字段状态
  Object.keys(initialData).forEach(key => {
    fieldStates[key] = {
      touched: false,
      dirty: false,
      valid: true
    };
  });

  /**
   * 验证单个字段
   * @param {String} fieldName 字段名
   * @param {*} value 字段值
   * @returns {Boolean} 是否验证通过
   */
  const validateField = (fieldName, value = formData[fieldName]) => {
    const rules = validationRules[fieldName];
    if (!rules) return true;

    const errors = [];
    
    // 执行验证规则
    for (const rule of rules) {
      const result = executeValidationRule(rule, value, formData);
      if (result !== true) {
        errors.push(result);
      }
    }
    
    // 更新验证错误
    if (errors.length > 0) {
      validationErrors[fieldName] = errors;
      fieldStates[fieldName].valid = false;
      return false;
    } else {
      delete validationErrors[fieldName];
      fieldStates[fieldName].valid = true;
      return true;
    }
  };

  /**
   * 执行单个验证规则
   * @param {Object|Function} rule 验证规则
   * @param {*} value 字段值
   * @param {Object} allData 所有表单数据
   * @returns {Boolean|String} 验证结果
   */
  const executeValidationRule = (rule, value, allData) => {
    // 如果是函数，直接执行
    if (typeof rule === 'function') {
      return rule(value, allData);
    }
    
    // 如果是对象，根据类型执行不同验证
    const { type, message, ...params } = rule;
    
    switch (type) {
      case 'required':
        return validateRequired(value) || message || '此字段为必填项';
      
      case 'minLength':
        return validateMinLength(value, params.min) || message || `最少需要${params.min}个字符`;
      
      case 'maxLength':
        return validateMaxLength(value, params.max) || message || `最多允许${params.max}个字符`;
      
      case 'email':
        return validateEmail(value) || message || '请输入有效的邮箱地址';
      
      case 'phone':
        return validatePhone(value) || message || '请输入有效的手机号码';
      
      case 'pattern':
        return validatePattern(value, params.pattern) || message || '格式不正确';
      
      case 'custom':
        return params.validator(value, allData) || message || '验证失败';
      
      case 'confirm':
        return validateConfirm(value, allData[params.target]) || message || '两次输入不一致';
      
      default:
        console.warn(`未知的验证规则类型: ${type}`);
        return true;
    }
  };

  // 内置验证函数
  const validateRequired = (value) => {
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'string') return value.trim().length > 0;
    return value !== null && value !== undefined && value !== '';
  };

  const validateMinLength = (value, min) => {
    if (!value) return true; // 空值由required规则处理
    return String(value).length >= min;
  };

  const validateMaxLength = (value, max) => {
    if (!value) return true;
    return String(value).length <= max;
  };

  const validateEmail = (value) => {
    if (!value) return true;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value);
  };

  const validatePhone = (value) => {
    if (!value) return true;
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(value);
  };

  const validatePattern = (value, pattern) => {
    if (!value) return true;
    const regex = new RegExp(pattern);
    return regex.test(value);
  };

  const validateConfirm = (value, targetValue) => {
    return value === targetValue;
  };

  /**
   * 验证整个表单
   * @returns {Boolean} 是否验证通过
   */
  const validateForm = () => {
    isValidating.value = true;
    let isValid = true;
    
    // 验证所有字段
    Object.keys(validationRules).forEach(fieldName => {
      const fieldValid = validateField(fieldName);
      if (!fieldValid) {
        isValid = false;
      }
    });
    
    isValidating.value = false;
    return isValid;
  };

  /**
   * 处理字段变化
   * @param {String} fieldName 字段名
   * @param {*} value 新值
   */
  const handleFieldChange = (fieldName, value) => {
    // 更新表单数据
    formData[fieldName] = value;
    
    // 更新字段状态
    fieldStates[fieldName].dirty = true;
    
    // 如果字段已经被触摸过，立即验证
    if (fieldStates[fieldName].touched) {
      validateField(fieldName, value);
    }
  };

  /**
   * 处理字段失焦
   * @param {String} fieldName 字段名
   */
  const handleFieldBlur = (fieldName) => {
    fieldStates[fieldName].touched = true;
    validateField(fieldName);
  };

  /**
   * 提交表单
   * @param {Function} submitHandler 提交处理函数
   * @returns {Promise} 提交结果
   */
  const submitForm = async (submitHandler) => {
    if (isSubmitting.value) return;
    
    // 验证表单
    const isValid = validateForm();
    if (!isValid) {
      if (showErrorMessage) {
        message.error('请检查表单中的错误信息');
      }
      return Promise.reject(new Error('表单验证失败'));
    }
    
    isSubmitting.value = true;
    
    try {
      const result = await submitHandler(formData);
      
      if (showSuccessMessage) {
        message.success('提交成功');
      }
      
      if (resetOnSuccess) {
        resetForm();
      }
      
      return result;
    } catch (error) {
      if (showErrorMessage) {
        const errorMsg = error.userFriendlyMessage || error.message || '提交失败';
        message.error(errorMsg);
      }
      throw error;
    } finally {
      isSubmitting.value = false;
    }
  };

  /**
   * 重置表单
   */
  const resetForm = () => {
    // 重置表单数据
    Object.keys(initialData).forEach(key => {
      formData[key] = initialData[key];
    });
    
    // 重置验证错误
    Object.keys(validationErrors).forEach(key => {
      delete validationErrors[key];
    });
    
    // 重置字段状态
    Object.keys(fieldStates).forEach(key => {
      fieldStates[key] = {
        touched: false,
        dirty: false,
        valid: true
      };
    });
  };

  /**
   * 设置表单数据
   * @param {Object} data 新的表单数据
   */
  const setFormData = (data) => {
    Object.keys(data).forEach(key => {
      if (key in formData) {
        formData[key] = data[key];
      }
    });
  };

  /**
   * 设置字段错误
   * @param {String} fieldName 字段名
   * @param {String|Array} errors 错误信息
   */
  const setFieldError = (fieldName, errors) => {
    validationErrors[fieldName] = Array.isArray(errors) ? errors : [errors];
    fieldStates[fieldName].valid = false;
  };

  /**
   * 清除字段错误
   * @param {String} fieldName 字段名
   */
  const clearFieldError = (fieldName) => {
    delete validationErrors[fieldName];
    fieldStates[fieldName].valid = true;
  };

  // 计算属性
  const isFormValid = computed(() => {
    return Object.keys(validationErrors).length === 0;
  });

  const hasErrors = computed(() => {
    return Object.keys(validationErrors).length > 0;
  });

  const isDirty = computed(() => {
    return Object.values(fieldStates).some(state => state.dirty);
  });

  const touchedFields = computed(() => {
    return Object.keys(fieldStates).filter(key => fieldStates[key].touched);
  });

  return {
    // 响应式数据
    formData,
    validationErrors,
    fieldStates,
    isValidating,
    isSubmitting,
    
    // 计算属性
    isFormValid,
    hasErrors,
    isDirty,
    touchedFields,
    
    // 方法
    validateField,
    validateForm,
    handleFieldChange,
    handleFieldBlur,
    submitForm,
    resetForm,
    setFormData,
    setFieldError,
    clearFieldError
  };
}

/**
 * 常用验证规则
 */
export const commonRules = {
  required: { type: 'required' },
  email: { type: 'email' },
  phone: { type: 'phone' },
  minLength: (min) => ({ type: 'minLength', min }),
  maxLength: (max) => ({ type: 'maxLength', max }),
  pattern: (pattern, message) => ({ type: 'pattern', pattern, message }),
  confirm: (target) => ({ type: 'confirm', target }),
  custom: (validator, message) => ({ type: 'custom', validator, message })
};

/**
 * 预定义的表单验证规则集
 */
export const rulesets = {
  // 用户注册表单
  userRegister: {
    username: [commonRules.required, commonRules.minLength(3), commonRules.maxLength(20)],
    email: [commonRules.required, commonRules.email],
    phone: [commonRules.required, commonRules.phone],
    password: [commonRules.required, commonRules.minLength(6)],
    confirmPassword: [commonRules.required, commonRules.confirm('password')]
  },
  
  // 用户登录表单
  userLogin: {
    username: [commonRules.required],
    password: [commonRules.required]
  },
  
  // 联系信息表单
  contactInfo: {
    name: [commonRules.required, commonRules.maxLength(50)],
    email: [commonRules.email],
    phone: [commonRules.phone],
    message: [commonRules.required, commonRules.maxLength(500)]
  }
};