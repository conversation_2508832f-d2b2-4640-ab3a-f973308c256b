<template>
  <div class="performance-data-table">
    <a-table
      :columns="columns"
      :data-source="data"
      :loading="loading"
      :pagination="pagination"
      :scroll="{ x: 1200 }"
      row-key="id"
      size="middle"
      @change="handleTableChange"
    >
      <!-- 时间列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'date'">
          <span>{{ formatDate(record.date) }}</span>
        </template>
        
        <!-- 销售额列 -->
        <template v-else-if="column.key === 'sales'">
          <div class="amount-cell">
            <span class="amount-value">{{ formatAmount(record.sales) }}</span>
            <div v-if="record.salesGrowth" class="growth-indicator" :class="record.salesGrowth.trend">
              <component :is="getGrowthIcon(record.salesGrowth.trend)" />
              {{ record.salesGrowth.display }}
            </div>
          </div>
        </template>
        
        <!-- 订单数列 -->
        <template v-else-if="column.key === 'orders'">
          <div class="number-cell">
            <span class="number-value">{{ record.orders }}单</span>
            <div v-if="record.ordersGrowth" class="growth-indicator" :class="record.ordersGrowth.trend">
              <component :is="getGrowthIcon(record.ordersGrowth.trend)" />
              {{ record.ordersGrowth.display }}
            </div>
          </div>
        </template>
        
        <!-- 客单价列 -->
        <template v-else-if="column.key === 'avgOrder'">
          <span>{{ formatAmount(record.avgOrder) }}</span>
        </template>
        
        <!-- 佣金列 -->
        <template v-else-if="column.key === 'commission'">
          <div class="commission-cell">
            <div class="commission-item">
              <span class="commission-label">预估:</span>
              <span class="commission-value">{{ formatAmount(record.estimatedCommission) }}</span>
            </div>
            <div class="commission-item">
              <span class="commission-label">实际:</span>
              <span class="commission-value actual">{{ formatAmount(record.actualCommission) }}</span>
            </div>
          </div>
        </template>
        
        <!-- 佣金率列 -->
        <template v-else-if="column.key === 'commissionRate'">
          <a-progress
            :percent="record.commissionRate"
            :stroke-color="getCommissionRateColor(record.commissionRate)"
            :show-info="false"
            size="small"
          />
          <span class="rate-text">{{ record.commissionRate }}%</span>
        </template>
        
        <!-- 店铺数列 -->
        <template v-else-if="column.key === 'stores'">
          <a-tag color="blue">{{ record.stores }}家</a-tag>
        </template>
        
        <!-- 达人数列 -->
        <template v-else-if="column.key === 'talents'">
          <a-tag color="green">{{ record.talents }}人</a-tag>
        </template>
        
        <!-- 操作列 -->
        <template v-else-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="handleView(record)">
              查看详情
            </a-button>
            <a-button type="link" size="small" @click="handleAnalyze(record)">
              深入分析
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ArrowUpOutlined, ArrowDownOutlined, MinusOutlined } from '@ant-design/icons-vue'
import gvmService from '../../services/gvmService'

defineOptions({
  name: 'PerformanceDataTable'
})

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: Object,
    default: () => ({})
  },
  showGrowth: {
    type: Boolean,
    default: true
  },
  compact: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['change', 'view', 'analyze'])

// 表格列配置
const columns = computed(() => {
  const baseColumns = [
    {
      title: '时间',
      key: 'date',
      dataIndex: 'date',
      width: 120,
      fixed: 'left',
      sorter: true
    },
    {
      title: '销售额',
      key: 'sales',
      dataIndex: 'sales',
      width: 140,
      sorter: true,
      align: 'right'
    },
    {
      title: '订单数',
      key: 'orders',
      dataIndex: 'orders',
      width: 120,
      sorter: true,
      align: 'right'
    },
    {
      title: '客单价',
      key: 'avgOrder',
      dataIndex: 'avgOrder',
      width: 100,
      sorter: true,
      align: 'right'
    },
    {
      title: '佣金',
      key: 'commission',
      width: 160,
      align: 'center'
    },
    {
      title: '佣金率',
      key: 'commissionRate',
      dataIndex: 'commissionRate',
      width: 120,
      sorter: true,
      align: 'center'
    }
  ]
  
  if (!props.compact) {
    baseColumns.push(
      {
        title: '合作店铺',
        key: 'stores',
        dataIndex: 'stores',
        width: 100,
        align: 'center'
      },
      {
        title: '关联达人',
        key: 'talents',
        dataIndex: 'talents',
        width: 100,
        align: 'center'
      }
    )
  }
  
  baseColumns.push({
    title: '操作',
    key: 'action',
    width: 160,
    fixed: 'right',
    align: 'center'
  })
  
  return baseColumns
})

// 格式化日期
const formatDate = (date) => {
  if (!date) return '--'
  const d = new Date(date)
  return `${d.getMonth() + 1}/${d.getDate()}`
}

// 格式化金额
const formatAmount = (amount) => {
  return gvmService.formatAmount(amount)
}

// 获取增长趋势图标
const getGrowthIcon = (trend) => {
  switch (trend) {
    case 'up':
      return ArrowUpOutlined
    case 'down':
      return ArrowDownOutlined
    default:
      return MinusOutlined
  }
}

// 获取佣金率颜色
const getCommissionRateColor = (rate) => {
  if (rate >= 15) return '#52c41a'
  if (rate >= 10) return '#faad14'
  return '#f5222d'
}

// 事件处理
const handleTableChange = (pagination, filters, sorter) => {
  emit('change', { pagination, filters, sorter })
}

const handleView = (record) => {
  emit('view', record)
}

const handleAnalyze = (record) => {
  emit('analyze', record)
}
</script>

<style scoped>
.performance-data-table {
  width: 100%;
}

.amount-cell,
.number-cell {
  text-align: right;
}

.amount-value,
.number-value {
  display: block;
  font-weight: 600;
  font-size: 14px;
}

.growth-indicator {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 2px;
  font-size: 12px;
  margin-top: 2px;
}

.growth-indicator.up {
  color: #52c41a;
}

.growth-indicator.down {
  color: #f5222d;
}

.growth-indicator.stable {
  color: #8c8c8c;
}

.commission-cell {
  text-align: center;
}

.commission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
  font-size: 12px;
}

.commission-item:last-child {
  margin-bottom: 0;
}

.commission-label {
  color: #8c8c8c;
  margin-right: 4px;
}

.commission-value {
  font-weight: 500;
}

.commission-value.actual {
  color: #52c41a;
  font-weight: 600;
}

.rate-text {
  display: block;
  text-align: center;
  font-size: 12px;
  margin-top: 4px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .performance-data-table :deep(.ant-table) {
    font-size: 12px;
  }
  
  .amount-value,
  .number-value {
    font-size: 13px;
  }
  
  .growth-indicator {
    font-size: 11px;
  }
  
  .commission-item {
    font-size: 11px;
  }
}
</style>
