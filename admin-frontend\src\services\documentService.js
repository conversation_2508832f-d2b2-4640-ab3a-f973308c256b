/**
 * 文档管理服务
 * 提供文档的增删改查功能
 */

import apiClient from './apiClient'

const API_PREFIX = '/admin/langchain'

/**
 * 文档管理服务类
 */
class DocumentService {

  /**
   * 获取文档列表数据 - 中文命名方法
   * @param {number} 知识id - 知识id
   * @param {Object} 查询参数 - 查询参数
   * @returns {Promise} 文档列表
   */
  async 获取文档列表数据(知识id, 查询参数 = {}) {
    try {
      console.log('📄 文档服务 - 获取文档列表:', 知识id, 查询参数)

      // 构建请求数据，使用后端期望的字段名
      const 请求数据 = {
        页码: 查询参数.页码 || 1,
        每页数量: 查询参数.每页数量 || 20,
        搜索关键字: 查询参数.搜索关键字 || '',
        文件类型: 查询参数.文件类型 || '',
        处理状态: 查询参数.处理状态 || ''
      }

      console.log('📤 发送请求数据:', 请求数据)

      const response = await apiClient.post(`${API_PREFIX}/knowledge/${知识id}/documents/list`, 请求数据)

      console.log('📥 接收响应数据:', response)

      if (response.status === 100) {
        console.log('✅ 文档服务 - 获取文档列表成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 文档服务 - 获取文档列表失败:', response.message)
        return {
          success: false,
          error: response.message || '获取文档列表失败'
        }
      }
    } catch (error) {
      console.error('❌ 文档服务 - 获取文档列表异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 获取文档列表 - 兼容性方法
   * @param {number} knowledgeBaseId - 知识id
   * @param {Object} params - 查询参数
   * @returns {Promise} 文档列表
   */
  async getDocumentList(knowledgeBaseId, params = {}) {
    return this.获取文档列表数据(knowledgeBaseId, params);
  }

  /**
   * 获取支持的文件格式数据 - 中文命名方法
   * @returns {Promise} 支持的文件格式列表
   */
  async 获取支持的文件格式数据() {
    try {
      console.log('📄 文档服务 - 获取支持的文件格式')

      const response = await apiClient.get(`${API_PREFIX}/knowledge/supported-formats`)

      if (response.status === 100) {
        console.log('✅ 文档服务 - 获取支持的文件格式成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 文档服务 - 获取支持的文件格式失败:', response.message)
        return {
          success: false,
          error: response.message || '获取支持的文件格式失败'
        }
      }
    } catch (error) {
      console.error('❌ 文档服务 - 获取支持的文件格式异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 获取支持的文件格式 - 兼容性方法
   * @returns {Promise} 支持的文件格式列表
   */
  async getSupportedFormats() {
    return this.获取支持的文件格式数据();
  }

  /**
   * 获取文档详情数据 - 中文命名方法
   * @param {string} 文档id - 文档id
   * @returns {Promise} 文档详情
   */
  async 获取文档详情数据(文档id) {
    try {
      console.log('📄 文档服务 - 获取文档详情:', 文档id)

      const 请求数据 = {
        文档id: 文档id
      }

      const response = await apiClient.post(`${API_PREFIX}/documents/detail`, 请求数据)

      if (response.status === 100) {
        console.log('✅ 文档服务 - 获取文档详情成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 文档服务 - 获取文档详情失败:', response.message)
        return {
          success: false,
          error: response.message || '获取文档详情失败'
        }
      }
    } catch (error) {
      console.error('❌ 文档服务 - 获取文档详情异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 更新文档
   * @param {string} documentId - 文档id
   * @param {Object} updateData - 更新数据
   * @returns {Promise} 更新结果
   */
  async updateDocument(documentId, updateData) {
    try {
      console.log('📄 文档服务 - 更新文档:', documentId, updateData)

      const 请求数据 = {
        文档id: documentId,
        ...updateData
      }

      const response = await apiClient.post(`${API_PREFIX}/documents/update`, 请求数据)

      if (response.status === 100) {
        console.log('✅ 文档服务 - 更新文档成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 文档服务 - 更新文档失败:', response.message)
        return {
          success: false,
          error: response.message || '更新文档失败'
        }
      }
    } catch (error) {
      console.error('❌ 文档服务 - 更新文档异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 删除文档
   * @param {string} documentId - 文档id
   * @returns {Promise} 删除结果
   */
  async deleteDocument(documentId) {
    try {
      console.log('📄 文档服务 - 删除文档:', documentId)

      const response = await apiClient.post(`${API_PREFIX}/knowledge/documents/${documentId}/delete`)

      if (response.status === 100) {
        console.log('✅ 文档服务 - 删除文档成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 文档服务 - 删除文档失败:', response.message)
        return {
          success: false,
          error: response.message || '删除文档失败'
        }
      }
    } catch (error) {
      console.error('❌ 文档服务 - 删除文档异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 批量删除文档
   * @param {Array} documentIds - 文档id列表
   * @returns {Promise} 删除结果
   */
  async batchDeleteDocuments(documentIds) {
    try {
      console.log('📄 文档服务 - 批量删除文档:', documentIds)

      const 请求数据 = {
        文档id列表: documentIds
      }

      const response = await apiClient.post(`${API_PREFIX}/documents/batch_delete`, 请求数据)

      if (response.status === 100) {
        console.log('✅ 文档服务 - 批量删除文档成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 文档服务 - 批量删除文档失败:', response.message)
        return {
          success: false,
          error: response.message || '批量删除文档失败'
        }
      }
    } catch (error) {
      console.error('❌ 文档服务 - 批量删除文档异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 预览文档
   * @param {string} documentId - 文档id
   * @param {Object} previewOptions - 预览选项
   * @returns {Promise} 预览内容
   */
  async previewDocument(documentId, previewOptions = {}) {
    try {
      console.log('📄 文档服务 - 预览文档:', documentId, previewOptions)

      const 请求数据 = {
        文档id: documentId,
        最大长度: previewOptions.最大长度 || 5000,
        包含元数据: previewOptions.包含元数据 !== false
      }

      const response = await apiClient.post(`${API_PREFIX}/documents/preview`, 请求数据)

      if (response.status === 100) {
        console.log('✅ 文档服务 - 预览文档成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 文档服务 - 预览文档失败:', response.message)
        return {
          success: false,
          error: response.message || '预览文档失败'
        }
      }
    } catch (error) {
      console.error('❌ 文档服务 - 预览文档异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 下载文档
   * @param {string} documentId - 文档id
   * @returns {Promise} 下载结果
   */
  async downloadDocument(documentId) {
    try {
      console.log('📄 文档服务 - 下载文档:', documentId)

      const 请求数据 = {
        文档id: documentId
      }

      const response = await apiClient.post(`${API_PREFIX}/documents/download`, 请求数据)

      if (response.status === 100) {
        console.log('✅ 文档服务 - 下载文档成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 文档服务 - 下载文档失败:', response.message)
        return {
          success: false,
          error: response.message || '下载文档失败'
        }
      }
    } catch (error) {
      console.error('❌ 文档服务 - 下载文档异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 重新处理文档
   * @param {string} documentId - 文档id
   * @returns {Promise} 处理结果
   */
  async reprocessDocument(documentId) {
    try {
      console.log('📄 文档服务 - 重新处理文档:', documentId)

      const response = await apiClient.post(`${API_PREFIX}/documents/${documentId}/reprocess`)

      if (response.status === 100) {
        console.log('✅ 文档服务 - 重新处理文档成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 文档服务 - 重新处理文档失败:', response.message)
        return {
          success: false,
          error: response.message || '重新处理文档失败'
        }
      }
    } catch (error) {
      console.error('❌ 文档服务 - 重新处理文档异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }



  /**
   * 获取文档上传进度
   * @param {string} taskId - 任务ID
   * @returns {Promise} 上传进度
   */
  async getUploadProgress(taskId) {
    try {
      console.log('📄 文档服务 - 获取上传进度:', taskId)

      const response = await apiClient.get(`${API_PREFIX}/documents/upload/progress/${taskId}`)

      if (response.status === 100) {
        console.log('✅ 文档服务 - 获取上传进度成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 文档服务 - 获取上传进度失败:', response.message)
        return {
          success: false,
          error: response.message || '获取上传进度失败'
        }
      }
    } catch (error) {
      console.error('❌ 文档服务 - 获取上传进度异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }


}

// 创建服务实例
const documentService = new DocumentService()

export default documentService
