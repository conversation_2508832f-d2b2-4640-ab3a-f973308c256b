<template>
  <div 
    class="metric-card" 
    :class="{ 'clickable': clickable, 'loading': loading }"
    @click="handleClick"
  >
    <a-card :loading="loading" class="card-content">
      <div class="metric-header">
        <div class="metric-icon" :style="{ backgroundColor: color }">
          <component :is="iconComponent" />
        </div>
        <div class="metric-info">
          <div class="metric-title">{{ title }}</div>
          <div class="metric-value">
            {{ formattedValue }}
            <span v-if="suffix" class="value-suffix">{{ suffix }}</span>
          </div>
        </div>
      </div>
      
      <div v-if="growth && showGrowth" class="metric-growth">
        <div 
          class="growth-indicator"
          :class="`growth-${growth.trend}`"
        >
          <component :is="growthIcon" class="growth-icon" />
          <span class="growth-text">{{ growth.display }}</span>
        </div>
        <div class="growth-label">较上期</div>
      </div>
      
      <div v-if="description" class="metric-description">
        {{ description }}
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import {
  DollarOutlined,
  ShoppingOutlined,
  CalculatorOutlined,
  WalletOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined
} from '@ant-design/icons-vue'
import gvmService from '../../services/gvmService'

defineOptions({
  name: 'MetricCard'
})

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [Number, String],
    default: 0
  },
  growth: {
    type: Object,
    default: null
    // { rate: number, trend: 'up'|'down'|'stable', display: string }
  },
  icon: {
    type: String,
    default: 'DollarOutlined'
  },
  color: {
    type: String,
    default: '#1890ff'
  },
  suffix: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  clickable: {
    type: Boolean,
    default: true
  },
  showGrowth: {
    type: Boolean,
    default: true
  },
  formatType: {
    type: String,
    default: 'currency', // 'currency', 'number', 'percentage'
    validator: (value) => ['currency', 'number', 'percentage'].includes(value)
  }
})

const emit = defineEmits(['click'])

// 图标组件映射
const iconComponents = {
  DollarOutlined,
  ShoppingOutlined,
  CalculatorOutlined,
  WalletOutlined
}

// 计算图标组件
const iconComponent = computed(() => {
  return iconComponents[props.icon] || DollarOutlined
})

// 计算增长趋势图标
const growthIcon = computed(() => {
  if (!props.growth) return MinusOutlined
  
  switch (props.growth.trend) {
    case 'up':
      return ArrowUpOutlined
    case 'down':
      return ArrowDownOutlined
    default:
      return MinusOutlined
  }
})

// 格式化数值显示
const formattedValue = computed(() => {
  if (props.loading) return '--'
  
  const value = Number(props.value) || 0
  
  switch (props.formatType) {
    case 'currency':
      return gvmService.formatAmount(value)
    case 'percentage':
      return gvmService.formatPercentage(value)
    case 'number':
    default:
      if (value >= 10000) {
        return `${(value / 10000).toFixed(1)}万`
      } else if (value >= 1000) {
        return `${(value / 1000).toFixed(1)}k`
      } else {
        return value.toLocaleString()
      }
  }
})

// 处理点击事件
const handleClick = () => {
  if (props.clickable && !props.loading) {
    emit('click')
  }
}
</script>

<style scoped>
.metric-card {
  height: 100%;
  transition: all 0.3s ease;
}

.metric-card.clickable {
  cursor: pointer;
}

.metric-card.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.metric-card.loading {
  pointer-events: none;
}

.card-content {
  height: 100%;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.card-content :deep(.ant-card-body) {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.metric-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24px;
  flex-shrink: 0;
}

.metric-info {
  flex: 1;
  min-width: 0;
}

.metric-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
  line-height: 1.4;
}

.metric-value {
  font-size: 28px;
  font-weight: 600;
  color: #262626;
  line-height: 1.2;
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.value-suffix {
  font-size: 16px;
  font-weight: 400;
  color: #8c8c8c;
}

.metric-growth {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 12px;
}

.growth-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
}

.growth-indicator.growth-up {
  color: #52c41a;
}

.growth-indicator.growth-down {
  color: #f5222d;
}

.growth-indicator.growth-stable {
  color: #8c8c8c;
}

.growth-icon {
  font-size: 12px;
}

.growth-text {
  font-weight: 600;
}

.growth-label {
  font-size: 12px;
  color: #8c8c8c;
}

.metric-description {
  margin-top: 8px;
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .metric-header {
    gap: 12px;
    margin-bottom: 12px;
  }
  
  .metric-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .metric-value {
    font-size: 24px;
  }
  
  .value-suffix {
    font-size: 14px;
  }
  
  .card-content :deep(.ant-card-body) {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .metric-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 8px;
  }
  
  .metric-value {
    font-size: 20px;
    justify-content: center;
  }
  
  .metric-growth {
    justify-content: center;
    gap: 8px;
  }
}

/* 加载状态样式 */
.metric-card.loading .metric-value {
  color: #d9d9d9;
}

.metric-card.loading .growth-indicator {
  color: #d9d9d9;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .card-content {
    background: #1f1f1f;
    border-color: #303030;
  }
  
  .metric-title {
    color: #a6a6a6;
  }
  
  .metric-value {
    color: #ffffff;
  }
  
  .value-suffix {
    color: #a6a6a6;
  }
  
  .growth-label {
    color: #a6a6a6;
  }
  
  .metric-description {
    color: #a6a6a6;
  }
}
</style>
